<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class AdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin user
        User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'admin',
            'phone_number' => '0788613669',
            'is_phone_verified' => true,
            'email_verified_at' => now(),
        ]);

        // Create owner user
        User::create([
            'name' => 'Car Owner',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'owner',
            'phone_number' => '0788613670',
            'is_phone_verified' => true,
            'email_verified_at' => now(),
        ]);

        // Create driver user
        User::create([
            'name' => 'Professional Driver',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'driver',
            'phone_number' => '0788613671',
            'is_phone_verified' => true,
            'email_verified_at' => now(),
        ]);

        // Create client user
        User::create([
            'name' => 'Client User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'client',
            'phone_number' => '0788613672',
            'is_phone_verified' => true,
            'email_verified_at' => now(),
        ]);
    }
}
