<?php

namespace App\Http\Controllers;

use App\Models\Booking;
use Illuminate\Http\Request;
use Inertia\Inertia;

class BookingController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function index(Request $request)
    {
        $query = Booking::with(['item', 'user'])
            ->where('user_id', auth()->id());

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('type')) {
            $query->where('item_type', $request->type);
        }

        $bookings = $query->latest()->get();

        return Inertia::render('Bookings/Index', [
            'bookings' => $bookings,
            'filters' => $request->only(['status', 'type']),
        ]);
    }

    public function show(Booking $booking)
    {
        // Ensure user can only view their own bookings
        if ($booking->user_id !== auth()->id()) {
            abort(403);
        }

        $booking->load(['item', 'user']);

        // Load the owner/user relationship for the item
        if ($booking->item_type === 'car' && $booking->item) {
            $booking->item->load('owner');
        } elseif ($booking->item_type === 'driver' && $booking->item) {
            $booking->item->load('user');
            // For drivers, the owner is the user associated with the driver
            $booking->item->owner = $booking->item->user;
        }

        return Inertia::render('Bookings/Show', [
            'booking' => $booking,
        ]);
    }

    public function cancel(Booking $booking)
    {
        // Ensure user can only cancel their own bookings
        if ($booking->user_id !== auth()->id()) {
            abort(403);
        }

        // Check if booking can be cancelled
        if (!in_array($booking->status, ['pending', 'confirmed'])) {
            return back()->withErrors(['error' => 'This booking cannot be cancelled.']);
        }

        // Check if booking is not too close to start time (e.g., 2 hours before)
        $startTime = new \DateTime($booking->start_time);
        $now = new \DateTime();
        $hoursUntilStart = ($startTime->getTimestamp() - $now->getTimestamp()) / 3600;

        if ($hoursUntilStart < 2) {
            return back()->withErrors(['error' => 'Bookings cannot be cancelled less than 2 hours before the start time.']);
        }

        $booking->update(['status' => 'cancelled']);

        // Send cancellation notification
        $booking->user->notify(new \App\Notifications\BookingCancelled($booking));

        return redirect()->route('bookings.show', $booking)
            ->with('message', 'Booking has been cancelled successfully.');
    }
}
