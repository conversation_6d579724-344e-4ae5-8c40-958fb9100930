<?php

use App\Http\Controllers\Admin\AdminController;
use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\Auth\RegisterController;
use App\Http\Controllers\BookingController;
use App\Http\Controllers\CarController;
use App\Http\Controllers\ChatController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\DriverController;
use App\Http\Controllers\FileUploadController;
use App\Http\Controllers\GpsRequestController;
use App\Http\Controllers\HomeController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

// Test Route
Route::get('/test', function () {
    return Inertia::render('Test');
});

Route::get('/', [HomeController::class, 'index'])->name('home');

// Authentication Routes
Route::get('/login', [LoginController::class, 'show'])->name('login');
Route::post('/login', [LoginController::class, 'store']);
Route::get('/register', [RegisterController::class, 'show'])->name('register');
Route::post('/register', [RegisterController::class, 'store']);
Route::post('/logout', [LoginController::class, 'destroy'])->name('logout');

// Dashboard
Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard')->middleware('auth');

// Cars Routes
Route::get('/cars', [CarController::class, 'index'])->name('cars.index');
Route::get('/cars/{car}', [CarController::class, 'show'])->name('cars.show');
Route::post('/cars/{car}/book', [CarController::class, 'book'])->name('cars.book')->middleware('auth');
Route::middleware('auth')->group(function () {
    Route::get('/cars/create', [CarController::class, 'create'])->name('cars.create');
    Route::post('/cars', [CarController::class, 'store'])->name('cars.store');
});

// Drivers Routes
Route::get('/drivers', [DriverController::class, 'index'])->name('drivers.index');
Route::get('/drivers/{driver}', [DriverController::class, 'show'])->name('drivers.show');
Route::post('/drivers/{driver}/book', [DriverController::class, 'book'])->name('drivers.book')->middleware('auth');

// Booking Routes
Route::middleware('auth')->group(function () {
    Route::get('/bookings', [BookingController::class, 'index'])->name('bookings.index');
    Route::get('/bookings/{booking}', [BookingController::class, 'show'])->name('bookings.show');
    Route::post('/bookings/{booking}/cancel', [BookingController::class, 'cancel'])->name('bookings.cancel');
});

// GPS Installation Request Routes
Route::middleware('auth')->group(function () {
    Route::get('/gps-request', [GpsRequestController::class, 'create'])->name('gps-request.create');
    Route::post('/gps-request', [GpsRequestController::class, 'store'])->name('gps-request.store');
    Route::get('/gps-requests', [GpsRequestController::class, 'index'])->name('gps-requests.index');
});

// Chat Routes
Route::middleware('auth')->group(function () {
    Route::get('/chats', [ChatController::class, 'index'])->name('chats.index');
    Route::get('/chats/{chat}', [ChatController::class, 'show'])->name('chats.show');
    Route::post('/chats', [ChatController::class, 'store'])->name('chats.store');
    Route::post('/chats/{chat}/messages', [ChatController::class, 'storeMessage'])->name('chats.messages.store');
    Route::post('/start-chat', [ChatController::class, 'startChat'])->name('chats.start');
});

// File Upload Routes
Route::middleware('auth')->group(function () {
    Route::post('/upload/car-images', [FileUploadController::class, 'uploadCarImages'])->name('upload.car-images');
    Route::post('/upload/driver-image', [FileUploadController::class, 'uploadDriverImage'])->name('upload.driver-image');
    Route::post('/upload/license-document', [FileUploadController::class, 'uploadLicenseDocument'])->name('upload.license-document');
    Route::post('/upload/chat-file', [FileUploadController::class, 'uploadChatFile'])->name('upload.chat-file');
    Route::delete('/upload/file', [FileUploadController::class, 'deleteFile'])->name('upload.delete');
    Route::get('/upload/file-info', [FileUploadController::class, 'getFileInfo'])->name('upload.file-info');
});

// Admin Routes
Route::middleware(['auth'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/dashboard', [AdminController::class, 'dashboard'])->name('dashboard');
    Route::get('/users', [AdminController::class, 'users'])->name('users');
    Route::get('/cars', [AdminController::class, 'cars'])->name('cars');
    Route::get('/drivers', [AdminController::class, 'drivers'])->name('drivers');
    Route::get('/bookings', [AdminController::class, 'bookings'])->name('bookings');
    Route::get('/gps-requests', [AdminController::class, 'gpsRequests'])->name('gps-requests');

    // Admin Actions
    Route::post('/drivers/{driver}/verification', [AdminController::class, 'updateDriverVerification'])->name('drivers.verification');
    Route::post('/gps-requests/{gpsRequest}/update', [AdminController::class, 'updateGpsRequest'])->name('gps-requests.update');
    Route::post('/cars/{car}/toggle-status', [AdminController::class, 'toggleCarStatus'])->name('cars.toggle-status');
    Route::post('/drivers/{driver}/toggle-status', [AdminController::class, 'toggleDriverStatus'])->name('drivers.toggle-status');
});
