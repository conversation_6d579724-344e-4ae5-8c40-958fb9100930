<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use App\Models\Car;
use App\Models\Driver;
use App\Models\GpsInstallationRequest;
use App\Models\User;
use Illuminate\Http\Request;
use Inertia\Inertia;

class AdminController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware(function ($request, $next) {
            if (auth()->user()->role !== 'admin') {
                abort(403, 'Access denied. Admin privileges required.');
            }
            return $next($request);
        });
    }

    public function dashboard()
    {
        // Calculate stats
        $stats = [
            'total_users' => User::count(),
            'total_cars' => Car::count(),
            'total_drivers' => Driver::count(),
            'total_bookings' => Booking::count(),
            'pending_verifications' => Driver::where('license_verification_status', 'pending')->count(),
            'pending_gps_requests' => GpsInstallationRequest::where('status', 'pending')->count(),
            'total_revenue' => Booking::where('status', 'completed')->sum('total_price'),
            'monthly_revenue' => Booking::where('status', 'completed')
                ->whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->sum('total_price'),
        ];

        // Get recent data
        $recent_users = User::latest()->take(5)->get();
        $recent_bookings = Booking::with(['user', 'item'])->latest()->take(5)->get();
        $pending_verifications = Driver::where('license_verification_status', 'pending')
            ->with('user')
            ->take(5)
            ->get();
        $pending_gps_requests = GpsInstallationRequest::where('status', 'pending')
            ->with(['user', 'car'])
            ->take(5)
            ->get();

        return Inertia::render('Admin/Dashboard', [
            'stats' => $stats,
            'recent_users' => $recent_users,
            'recent_bookings' => $recent_bookings,
            'pending_verifications' => $pending_verifications,
            'pending_gps_requests' => $pending_gps_requests,
        ]);
    }

    public function users(Request $request)
    {
        $query = User::query();

        // Apply filters
        if ($request->filled('role')) {
            $query->where('role', $request->role);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        $users = $query->latest()->paginate(20);

        return Inertia::render('Admin/Users', [
            'users' => $users,
            'filters' => $request->only(['role', 'search']),
        ]);
    }

    public function cars(Request $request)
    {
        $query = Car::with('owner');

        // Apply filters
        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('make', 'like', "%{$search}%")
                  ->orWhere('model', 'like', "%{$search}%")
                  ->orWhere('location', 'like', "%{$search}%");
            });
        }

        $cars = $query->latest()->paginate(20);

        return Inertia::render('Admin/Cars', [
            'cars' => $cars,
            'filters' => $request->only(['status', 'search']),
        ]);
    }

    public function drivers(Request $request)
    {
        $query = Driver::with('user');

        // Apply filters
        if ($request->filled('status')) {
            if ($request->status === 'available') {
                $query->where('is_available', true);
            } elseif ($request->status === 'unavailable') {
                $query->where('is_available', false);
            } elseif (in_array($request->status, ['pending', 'verified', 'rejected'])) {
                $query->where('license_verification_status', $request->status);
            }
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('location', 'like', "%{$search}%")
                  ->orWhere('license_number', 'like', "%{$search}%");
            });
        }

        $drivers = $query->latest()->paginate(20);

        return Inertia::render('Admin/Drivers', [
            'drivers' => $drivers,
            'filters' => $request->only(['status', 'search']),
        ]);
    }

    public function bookings(Request $request)
    {
        $query = Booking::with(['user', 'item']);

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('type')) {
            $query->where('item_type', $request->type);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->whereHas('user', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        $bookings = $query->latest()->paginate(20);

        return Inertia::render('Admin/Bookings', [
            'bookings' => $bookings,
            'filters' => $request->only(['status', 'type', 'search']),
        ]);
    }

    public function gpsRequests(Request $request)
    {
        $query = GpsInstallationRequest::with(['user', 'car']);

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        $gpsRequests = $query->latest()->paginate(20);

        return Inertia::render('Admin/GpsRequests', [
            'gpsRequests' => $gpsRequests,
            'filters' => $request->only(['status']),
        ]);
    }

    public function updateDriverVerification(Request $request, Driver $driver)
    {
        $request->validate([
            'status' => 'required|in:pending,verified,rejected',
            'notes' => 'nullable|string|max:1000',
        ]);

        $driver->update([
            'license_verification_status' => $request->status,
        ]);

        return back()->with('message', 'Driver verification status updated successfully.');
    }

    public function updateGpsRequest(Request $request, GpsInstallationRequest $gpsRequest)
    {
        $request->validate([
            'status' => 'required|in:pending,approved,rejected,completed',
            'admin_notes' => 'nullable|string|max:1000',
        ]);

        $updateData = [
            'status' => $request->status,
            'admin_notes' => $request->admin_notes,
        ];

        if ($request->status === 'approved') {
            $updateData['approved_at'] = now();
        } elseif ($request->status === 'completed') {
            $updateData['completed_at'] = now();
        }

        $gpsRequest->update($updateData);

        return back()->with('message', 'GPS installation request updated successfully.');
    }

    public function toggleCarStatus(Car $car)
    {
        $car->update(['is_active' => !$car->is_active]);

        return back()->with('message', 'Car status updated successfully.');
    }

    public function toggleDriverStatus(Driver $driver)
    {
        $driver->update(['is_available' => !$driver->is_available]);

        return back()->with('message', 'Driver availability updated successfully.');
    }
}
