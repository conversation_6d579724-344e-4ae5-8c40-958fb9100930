<?php

namespace App\Http\Controllers;

use App\Models\Car;
use App\Models\Driver;
use Illuminate\Http\Request;
use Inertia\Inertia;

class HomeController extends Controller
{
    public function index()
    {
        try {
            $cars = Car::with('owner')
                ->where('is_available', true)
                ->latest()
                ->take(6)
                ->get();

            $drivers = Driver::with('user')
                ->where('is_available', true)
                ->where('license_verification_status', 'verified')
                ->latest()
                ->take(6)
                ->get();

            return Inertia::render('Home', [
                'cars' => $cars,
                'drivers' => $drivers,
            ]);
        } catch (\Exception $e) {
            // If there's a database error, return a simple version
            return Inertia::render('Home', [
                'cars' => [],
                'drivers' => [],
                'error' => 'Database connection issue: ' . $e->getMessage()
            ]);
        }
    }
}
