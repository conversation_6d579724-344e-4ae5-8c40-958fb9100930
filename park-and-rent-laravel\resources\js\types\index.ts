export interface User {
  id: number;
  name: string;
  email: string;
  email_verified_at?: string;
  role: 'client' | 'owner' | 'driver' | 'admin';
  phone_number?: string;
  is_phone_verified: boolean;
  license_image_url?: string;
  license_verification_status?: 'pending' | 'verified' | 'rejected';
  created_at: string;
  updated_at: string;
}

export interface Car {
  id: number;
  owner_id: number;
  make: string;
  model: string;
  year: number;
  images: string[];
  description: string;
  features: string[];
  location: string;
  price_per_hour: number;
  availability_notes?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  owner?: User;
}

export interface Driver {
  id: number;
  user_id: number;
  name: string;
  age: number;
  experience: number;
  profile_image?: string;
  license_number: string;
  license_verification_status: 'pending' | 'verified' | 'rejected';
  location: string;
  price_per_hour: number;
  rating: number;
  reviews: number;
  specialties?: string[];
  availability_notes?: string;
  is_available: boolean;
  created_at: string;
  updated_at: string;
  user?: User;
}

export interface Booking {
  id: number;
  user_id: number;
  item_type: 'car' | 'driver';
  item_id: number;
  start_time: string;
  end_time: string;
  total_price: number;
  status: 'pending' | 'confirmed' | 'completed' | 'cancelled';
  notes?: string;
  is_paid: boolean;
  created_at: string;
  updated_at: string;
  user?: User;
  item?: Car | Driver;
}

export interface Chat {
  id: number;
  user_id: number;
  recipient_id: number;
  related_to_type?: 'car' | 'driver';
  related_to_id?: number;
  is_active: boolean;
  last_message_at?: string;
  created_at: string;
  updated_at: string;
  user?: User;
  recipient?: User;
  related_to?: Car | Driver;
  messages?: Message[];
}

export interface Message {
  id: number;
  chat_id: number;
  sender_id: number;
  content: string;
  is_read: boolean;
  read_at?: string;
  created_at: string;
  updated_at: string;
  sender?: User;
  chat?: Chat;
}

export interface GpsInstallationRequest {
  id: number;
  user_id: number;
  car_id: number;
  car_make: string;
  car_model: string;
  car_year: string;
  license_plate?: string;
  reason: string;
  contact_phone: string;
  preferred_installation_date?: string;
  status: 'pending' | 'approved' | 'rejected' | 'completed';
  admin_notes?: string;
  approved_at?: string;
  completed_at?: string;
  created_at: string;
  updated_at: string;
  user?: User;
  car?: Car;
}

export type PageProps<T extends Record<string, unknown> = Record<string, unknown>> = T & {
  auth: {
    user: User;
  };
};
