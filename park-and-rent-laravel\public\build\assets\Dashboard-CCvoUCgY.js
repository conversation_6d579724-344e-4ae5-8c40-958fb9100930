import{j as e,$ as h,Y as a}from"./app-Cj-kSxmT.js";import{c as g,C as t}from"./car-CXkXzXL9.js";import{S as j,C as n}from"./settings-CPuJKZnh.js";import{U as d}from"./user-round-Dc3ptzYJ.js";import{C as i}from"./calendar-C03a_VmS.js";/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const p=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]],N=g("plus",p);function _({auth:x,bookings:c,cars:u,driver:f,stats:l}){const r=x.user,m=()=>{const s=new Date().getHours();return s<12?"Good morning":s<18?"Good afternoon":"Good evening"},o=()=>{switch(r.role){case"admin":return e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[e.jsx("div",{className:"bg-white p-6 rounded-lg shadow-md",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"p-3 bg-blue-100 rounded-full",children:e.jsx(d,{className:"h-6 w-6 text-blue-600"})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Total Users"}),e.jsx("p",{className:"text-2xl font-bold text-blue-600",children:"1,234"})]})]})}),e.jsx("div",{className:"bg-white p-6 rounded-lg shadow-md",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"p-3 bg-green-100 rounded-full",children:e.jsx(t,{className:"h-6 w-6 text-green-600"})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Total Cars"}),e.jsx("p",{className:"text-2xl font-bold text-green-600",children:"567"})]})]})}),e.jsx("div",{className:"bg-white p-6 rounded-lg shadow-md",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"p-3 bg-purple-100 rounded-full",children:e.jsx(i,{className:"h-6 w-6 text-purple-600"})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Total Bookings"}),e.jsx("p",{className:"text-2xl font-bold text-purple-600",children:l.total_bookings})]})]})}),e.jsx("div",{className:"bg-white p-6 rounded-lg shadow-md",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"p-3 bg-yellow-100 rounded-full",children:e.jsx(n,{className:"h-6 w-6 text-yellow-600"})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Revenue"}),e.jsxs("p",{className:"text-2xl font-bold text-yellow-600",children:["RWF ",l.total_earnings.toLocaleString()]})]})]})})]});case"owner":return e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[e.jsx("div",{className:"bg-white p-6 rounded-lg shadow-md",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"p-3 bg-green-100 rounded-full",children:e.jsx(t,{className:"h-6 w-6 text-green-600"})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"My Cars"}),e.jsx("p",{className:"text-2xl font-bold text-green-600",children:l.active_cars||0})]})]})}),e.jsx("div",{className:"bg-white p-6 rounded-lg shadow-md",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"p-3 bg-blue-100 rounded-full",children:e.jsx(i,{className:"h-6 w-6 text-blue-600"})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Total Bookings"}),e.jsx("p",{className:"text-2xl font-bold text-blue-600",children:l.total_bookings})]})]})}),e.jsx("div",{className:"bg-white p-6 rounded-lg shadow-md",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"p-3 bg-purple-100 rounded-full",children:e.jsx(n,{className:"h-6 w-6 text-purple-600"})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Earnings"}),e.jsxs("p",{className:"text-2xl font-bold text-purple-600",children:["RWF ",l.total_earnings.toLocaleString()]})]})]})})]});default:return e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8",children:[e.jsx("div",{className:"bg-white p-6 rounded-lg shadow-md",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"p-3 bg-blue-100 rounded-full",children:e.jsx(i,{className:"h-6 w-6 text-blue-600"})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"My Bookings"}),e.jsx("p",{className:"text-2xl font-bold text-blue-600",children:l.total_bookings})]})]})}),e.jsx("div",{className:"bg-white p-6 rounded-lg shadow-md",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"p-3 bg-green-100 rounded-full",children:e.jsx(i,{className:"h-6 w-6 text-green-600"})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Pending"}),e.jsx("p",{className:"text-2xl font-bold text-green-600",children:l.pending_bookings})]})]})})]})}};return e.jsxs(e.Fragment,{children:[e.jsx(h,{title:"Dashboard"}),e.jsxs("div",{className:"min-h-screen bg-gray-50",children:[e.jsx("header",{className:"bg-white shadow-md",children:e.jsx("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"flex justify-between items-center py-4",children:[e.jsxs(a,{href:"/",className:"flex items-center space-x-2",children:[e.jsx(t,{className:"h-8 w-8 text-primary-600"}),e.jsx("span",{className:"text-xl font-bold text-gray-900",children:"Park & Rent"})]}),e.jsxs("nav",{className:"hidden md:flex space-x-8",children:[e.jsx(a,{href:"/",className:"text-gray-700 hover:text-primary-600",children:"Home"}),e.jsx(a,{href:"/cars",className:"text-gray-700 hover:text-primary-600",children:"Browse Cars"}),e.jsx(a,{href:"/drivers",className:"text-gray-700 hover:text-primary-600",children:"Hire a Driver"}),e.jsx(a,{href:"/dashboard",className:"text-primary-600 font-medium",children:"Dashboard"})]}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("span",{className:"text-gray-700",children:["Welcome, ",r.name]}),e.jsx(a,{href:"/profile",className:"text-gray-700 hover:text-primary-600",children:e.jsx(j,{size:20})})]})]})})}),e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsxs("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:[m(),", ",r.name,"!"]}),e.jsxs("p",{className:"text-gray-600",children:[r.role==="admin"&&"Manage your platform from here.",r.role==="owner"&&"Manage your cars and bookings.",r.role==="driver"&&"View your bookings and profile.",r.role==="client"&&"Track your bookings and discover new cars."]})]}),o(),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Quick Actions"}),e.jsxs("div",{className:"space-y-3",children:[r.role==="admin"&&e.jsxs(e.Fragment,{children:[e.jsxs(a,{href:"/admin/users",className:"flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors",children:[e.jsx(d,{size:20,className:"text-primary-600 mr-3"}),e.jsx("span",{children:"Manage Users"})]}),e.jsxs(a,{href:"/admin/cars",className:"flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors",children:[e.jsx(t,{size:20,className:"text-primary-600 mr-3"}),e.jsx("span",{children:"Manage Cars"})]})]}),r.role==="owner"&&e.jsxs(e.Fragment,{children:[e.jsxs(a,{href:"/cars/create",className:"flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors",children:[e.jsx(N,{size:20,className:"text-primary-600 mr-3"}),e.jsx("span",{children:"Add New Car"})]}),e.jsxs(a,{href:"/owner/cars",className:"flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors",children:[e.jsx(t,{size:20,className:"text-primary-600 mr-3"}),e.jsx("span",{children:"Manage My Cars"})]})]}),e.jsxs(a,{href:"/cars",className:"flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors",children:[e.jsx(t,{size:20,className:"text-primary-600 mr-3"}),e.jsx("span",{children:"Browse Cars"})]}),e.jsxs(a,{href:"/drivers",className:"flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors",children:[e.jsx(d,{size:20,className:"text-primary-600 mr-3"}),e.jsx("span",{children:"Find Drivers"})]})]})]}),e.jsxs("div",{className:"lg:col-span-2 bg-white rounded-lg shadow-md p-6",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Recent Bookings"}),e.jsx(a,{href:"/bookings",className:"text-primary-600 hover:text-primary-700 text-sm font-medium",children:"View All"})]}),c.length>0?e.jsx("div",{className:"space-y-4",children:c.slice(0,5).map(s=>e.jsxs("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center mr-4",children:s.item_type==="car"?e.jsx(t,{size:20,className:"text-primary-600"}):e.jsx(d,{size:20,className:"text-primary-600"})}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-gray-900",children:s.item_type==="car"?"Car Rental":"Driver Service"}),e.jsxs("p",{className:"text-sm text-gray-600",children:[new Date(s.start_time).toLocaleDateString()," - ",new Date(s.end_time).toLocaleDateString()]})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("span",{className:`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${s.status==="confirmed"?"bg-green-100 text-green-800":s.status==="pending"?"bg-yellow-100 text-yellow-800":s.status==="completed"?"bg-blue-100 text-blue-800":"bg-red-100 text-red-800"}`,children:s.status.charAt(0).toUpperCase()+s.status.slice(1)}),e.jsxs("p",{className:"text-sm font-medium text-gray-900 mt-1",children:["RWF ",s.total_price.toLocaleString()]})]})]},s.id))}):e.jsxs("div",{className:"text-center py-8",children:[e.jsx(i,{size:48,className:"mx-auto text-gray-400 mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"No bookings yet"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"Start by browsing available cars or drivers."}),e.jsx(a,{href:"/cars",className:"bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700",children:"Browse Cars"})]})]})]})]})]})]})}export{_ as default};
