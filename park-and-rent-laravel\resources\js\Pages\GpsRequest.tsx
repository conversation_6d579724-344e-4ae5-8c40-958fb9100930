import React from 'react';
import { Head, Link, useForm } from '@inertiajs/react';
import { Car, Shield, MapPin, Phone, Calendar, FileText } from 'lucide-react';
import { PageProps, Car as CarType } from '@/types';

interface GpsRequestProps extends PageProps {
  cars: CarType[];
}

export default function GpsRequest({ auth, cars }: GpsRequestProps) {
  const { data, setData, post, processing, errors } = useForm({
    car_id: '',
    car_make: '',
    car_model: '',
    car_year: '',
    license_plate: '',
    reason: '',
    contact_phone: auth.user?.phone_number || '',
    preferred_installation_date: '',
  });

  const handleCarSelection = (carId: string) => {
    const selectedCar = cars.find(car => car.id.toString() === carId);
    if (selectedCar) {
      setData({
        ...data,
        car_id: carId,
        car_make: selectedCar.make,
        car_model: selectedCar.model,
        car_year: selectedCar.year.toString(),
      });
    }
  };

  const submit = (e: React.FormEvent) => {
    e.preventDefault();
    post('/gps-request');
  };

  return (
    <>
      <Head title="GPS Installation Request" />
      
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <header className="bg-white shadow-md sticky top-0 z-50">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-4">
              <Link href="/" className="flex items-center space-x-2">
                <Car className="h-8 w-8 text-primary-600" />
                <span className="text-xl font-bold text-gray-900">Park & Rent</span>
              </Link>

              <nav className="hidden md:flex space-x-8">
                <Link href="/" className="text-gray-700 hover:text-primary-600">
                  Home
                </Link>
                <Link href="/cars" className="text-gray-700 hover:text-primary-600">
                  Browse Cars
                </Link>
                <Link href="/drivers" className="text-gray-700 hover:text-primary-600">
                  Hire a Driver
                </Link>
                <Link href="/dashboard" className="text-gray-700 hover:text-primary-600">
                  Dashboard
                </Link>
              </nav>

              <div className="flex items-center space-x-4">
                <span className="text-gray-700">Welcome, {auth.user.name}</span>
                <Link
                  href="/dashboard"
                  className="bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700"
                >
                  Dashboard
                </Link>
              </div>
            </div>
          </div>
        </header>

        <div className="container mx-auto px-4 py-8">
          <div className="max-w-2xl mx-auto">
            {/* Header */}
            <div className="text-center mb-8">
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Shield className="h-8 w-8 text-primary-600" />
              </div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                GPS Installation Request
              </h1>
              <p className="text-gray-600">
                Request GPS installation for your car to enhance security and tracking capabilities.
              </p>
            </div>

            {/* Benefits Section */}
            <div className="bg-white rounded-lg shadow-md p-6 mb-8">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Benefits of GPS Installation</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-start">
                  <Shield className="h-5 w-5 text-green-600 mr-2 mt-0.5" />
                  <div>
                    <h3 className="font-medium text-gray-900">Enhanced Security</h3>
                    <p className="text-sm text-gray-600">Real-time tracking and theft protection</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <MapPin className="h-5 w-5 text-blue-600 mr-2 mt-0.5" />
                  <div>
                    <h3 className="font-medium text-gray-900">Location Tracking</h3>
                    <p className="text-sm text-gray-600">Always know where your car is located</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <Car className="h-5 w-5 text-purple-600 mr-2 mt-0.5" />
                  <div>
                    <h3 className="font-medium text-gray-900">Rental Confidence</h3>
                    <p className="text-sm text-gray-600">Increased trust from potential renters</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <FileText className="h-5 w-5 text-orange-600 mr-2 mt-0.5" />
                  <div>
                    <h3 className="font-medium text-gray-900">Trip Reports</h3>
                    <p className="text-sm text-gray-600">Detailed usage and mileage reports</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Request Form */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-6">Request Form</h2>
              
              <form onSubmit={submit} className="space-y-6">
                {/* Car Selection */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Select Your Car
                  </label>
                  <select
                    value={data.car_id}
                    onChange={(e) => handleCarSelection(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
                    required
                  >
                    <option value="">Choose a car...</option>
                    {cars.map((car) => (
                      <option key={car.id} value={car.id}>
                        {car.make} {car.model} ({car.year}) - {car.location}
                      </option>
                    ))}
                  </select>
                  {errors.car_id && (
                    <p className="mt-1 text-sm text-red-600">{errors.car_id}</p>
                  )}
                </div>

                {/* Car Details (Auto-filled) */}
                {data.car_id && (
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Make
                      </label>
                      <input
                        type="text"
                        value={data.car_make}
                        readOnly
                        className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Model
                      </label>
                      <input
                        type="text"
                        value={data.car_model}
                        readOnly
                        className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Year
                      </label>
                      <input
                        type="text"
                        value={data.car_year}
                        readOnly
                        className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50"
                      />
                    </div>
                  </div>
                )}

                {/* License Plate */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    License Plate Number
                  </label>
                  <input
                    type="text"
                    value={data.license_plate}
                    onChange={(e) => setData('license_plate', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
                    placeholder="Enter license plate number"
                  />
                  {errors.license_plate && (
                    <p className="mt-1 text-sm text-red-600">{errors.license_plate}</p>
                  )}
                </div>

                {/* Contact Phone */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Contact Phone Number
                  </label>
                  <div className="relative">
                    <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                    <input
                      type="tel"
                      value={data.contact_phone}
                      onChange={(e) => setData('contact_phone', e.target.value)}
                      className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
                      placeholder="Enter your phone number"
                      required
                    />
                  </div>
                  {errors.contact_phone && (
                    <p className="mt-1 text-sm text-red-600">{errors.contact_phone}</p>
                  )}
                </div>

                {/* Preferred Installation Date */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Preferred Installation Date
                  </label>
                  <div className="relative">
                    <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                    <input
                      type="date"
                      value={data.preferred_installation_date}
                      onChange={(e) => setData('preferred_installation_date', e.target.value)}
                      min={new Date().toISOString().split('T')[0]}
                      className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
                    />
                  </div>
                  {errors.preferred_installation_date && (
                    <p className="mt-1 text-sm text-red-600">{errors.preferred_installation_date}</p>
                  )}
                </div>

                {/* Reason */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Reason for GPS Installation
                  </label>
                  <textarea
                    value={data.reason}
                    onChange={(e) => setData('reason', e.target.value)}
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
                    placeholder="Please explain why you need GPS installation (e.g., security concerns, rental business, fleet management, etc.)"
                    required
                  />
                  {errors.reason && (
                    <p className="mt-1 text-sm text-red-600">{errors.reason}</p>
                  )}
                </div>

                {/* Submit Button */}
                <div className="flex justify-end space-x-4">
                  <Link
                    href="/dashboard"
                    className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                  >
                    Cancel
                  </Link>
                  <button
                    type="submit"
                    disabled={processing}
                    className="px-6 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {processing ? 'Submitting...' : 'Submit Request'}
                  </button>
                </div>
              </form>
            </div>

            {/* Contact Information */}
            <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h3 className="text-lg font-medium text-blue-900 mb-2">Need Help?</h3>
              <p className="text-blue-800 mb-2">
                If you have any questions about GPS installation, please contact our support team:
              </p>
              <div className="text-blue-800">
                <p><strong>Email:</strong> <EMAIL></p>
                <p><strong>Phone:</strong> 0788613669</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
