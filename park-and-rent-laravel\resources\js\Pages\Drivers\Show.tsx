import React, { useState } from 'react';
import { Head, Link, useForm } from '@inertiajs/react';
import { 
  Car, 
  MapPin, 
  Calendar, 
  Clock, 
  Star, 
  Award, 
  MessageCircle,
  Phone,
  Mail,
  UserRound,
  Check,
  X
} from 'lucide-react';
import { PageProps, Driver } from '@/types';

interface DriverShowProps extends PageProps {
  driver: Driver;
}

export default function DriverShow({ auth, driver }: DriverShowProps) {
  const [showBookingForm, setShowBookingForm] = useState(false);
  
  const { data, setData, post, processing, errors } = useForm({
    start_time: '',
    end_time: '',
    notes: '',
  });

  const handleBooking = (e: React.FormEvent) => {
    e.preventDefault();
    post(`/drivers/${driver.id}/book`);
  };

  const calculatePrice = () => {
    if (!data.start_time || !data.end_time) return 0;
    
    const start = new Date(data.start_time);
    const end = new Date(data.end_time);
    const hours = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60));
    
    return hours > 0 ? hours * driver.price_per_hour : 0;
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        size={20}
        className={i < Math.floor(rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'}
      />
    ));
  };

  return (
    <>
      <Head title={`${driver.name} - Professional Driver`} />
      
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <header className="bg-white shadow-md sticky top-0 z-50">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-4">
              <Link href="/" className="flex items-center space-x-2">
                <Car className="h-8 w-8 text-primary-600" />
                <span className="text-xl font-bold text-gray-900">Park & Rent</span>
              </Link>

              <nav className="hidden md:flex space-x-8">
                <Link href="/" className="text-gray-700 hover:text-primary-600">
                  Home
                </Link>
                <Link href="/cars" className="text-gray-700 hover:text-primary-600">
                  Browse Cars
                </Link>
                <Link href="/drivers" className="text-primary-600 font-medium">
                  Hire a Driver
                </Link>
              </nav>

              <div className="flex items-center space-x-4">
                {auth.user ? (
                  <div className="flex items-center space-x-4">
                    <span className="text-gray-700">Welcome, {auth.user.name}</span>
                    <Link
                      href="/dashboard"
                      className="bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700"
                    >
                      Dashboard
                    </Link>
                  </div>
                ) : (
                  <div className="flex items-center space-x-4">
                    <Link
                      href="/login"
                      className="text-gray-700 hover:text-primary-600"
                    >
                      Log In
                    </Link>
                    <Link
                      href="/register"
                      className="bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700"
                    >
                      Sign Up
                    </Link>
                  </div>
                )}
              </div>
            </div>
          </div>
        </header>

        {/* Breadcrumb */}
        <div className="bg-white border-b">
          <div className="container mx-auto px-4 py-3">
            <nav className="flex items-center space-x-2 text-sm">
              <Link href="/" className="text-gray-600 hover:text-primary-600">Home</Link>
              <span className="text-gray-400">/</span>
              <Link href="/drivers" className="text-gray-600 hover:text-primary-600">Drivers</Link>
              <span className="text-gray-400">/</span>
              <span className="text-gray-900">{driver.name}</span>
            </nav>
          </div>
        </div>

        <div className="container mx-auto px-4 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-2">
              {/* Driver Profile Header */}
              <div className="bg-white rounded-lg shadow-md p-6 mb-6">
                <div className="flex items-start justify-between mb-6">
                  <div className="flex items-center">
                    <div className="w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center mr-6 overflow-hidden">
                      {driver.profile_image ? (
                        <img
                          src={driver.profile_image}
                          alt={driver.name}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <UserRound size={48} className="text-gray-400" />
                      )}
                    </div>
                    <div>
                      <h1 className="text-3xl font-bold text-gray-900 mb-2">{driver.name}</h1>
                      <div className="flex items-center text-gray-600 mb-2">
                        <MapPin size={16} className="mr-2" />
                        <span>{driver.location}</span>
                      </div>
                      <div className="flex items-center mb-2">
                        <div className="flex items-center mr-4">
                          {renderStars(driver.rating)}
                        </div>
                        <span className="text-gray-600">
                          {driver.rating}/5 ({driver.reviews} reviews)
                        </span>
                      </div>
                      <div className="flex items-center space-x-4 text-sm text-gray-600">
                        <div className="flex items-center">
                          <Award size={16} className="mr-1 text-primary-600" />
                          <span>{driver.experience} years experience</span>
                        </div>
                        <div className="flex items-center">
                          <Clock size={16} className="mr-1 text-primary-600" />
                          <span>{driver.age} years old</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-3xl font-bold text-primary-600">
                      RWF {driver.price_per_hour.toLocaleString()}
                    </div>
                    <div className="text-gray-600">per hour</div>
                  </div>
                </div>

                {/* Verification Status */}
                <div className="mb-4">
                  <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                    driver.license_verification_status === 'verified'
                      ? 'bg-green-100 text-green-800'
                      : driver.license_verification_status === 'pending'
                      ? 'bg-yellow-100 text-yellow-800'
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {driver.license_verification_status === 'verified' && <Check size={16} className="mr-1" />}
                    {driver.license_verification_status === 'verified' ? 'Verified Driver' : 
                     driver.license_verification_status === 'pending' ? 'Verification Pending' : 
                     'Not Verified'}
                  </span>
                </div>

                {/* Availability Status */}
                <div className="mb-4">
                  <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                    driver.is_available ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  }`}>
                    {driver.is_available ? 'Available' : 'Unavailable'}
                  </span>
                </div>

                {driver.availability_notes && (
                  <div className="border-t pt-4">
                    <h3 className="text-lg font-semibold text-gray-900 mb-3">Availability Notes</h3>
                    <p className="text-gray-700">{driver.availability_notes}</p>
                  </div>
                )}
              </div>

              {/* Specialties */}
              {driver.specialties && driver.specialties.length > 0 && (
                <div className="bg-white rounded-lg shadow-md p-6 mb-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Specialties & Services</h3>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                    {driver.specialties.map((specialty, index) => (
                      <div key={index} className="flex items-center p-3 bg-primary-50 rounded-lg">
                        <Check size={16} className="text-primary-600 mr-2" />
                        <span className="text-gray-700 font-medium">{specialty}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Driver Stats */}
              <div className="bg-white rounded-lg shadow-md p-6 mb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Driver Statistics</h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center p-4 bg-gray-50 rounded-lg">
                    <div className="text-2xl font-bold text-primary-600">{driver.experience}</div>
                    <div className="text-sm text-gray-600">Years Experience</div>
                  </div>
                  <div className="text-center p-4 bg-gray-50 rounded-lg">
                    <div className="text-2xl font-bold text-primary-600">{driver.rating}</div>
                    <div className="text-sm text-gray-600">Average Rating</div>
                  </div>
                  <div className="text-center p-4 bg-gray-50 rounded-lg">
                    <div className="text-2xl font-bold text-primary-600">{driver.reviews}</div>
                    <div className="text-sm text-gray-600">Total Reviews</div>
                  </div>
                  <div className="text-center p-4 bg-gray-50 rounded-lg">
                    <div className="text-2xl font-bold text-primary-600">{driver.age}</div>
                    <div className="text-sm text-gray-600">Age</div>
                  </div>
                </div>
              </div>

              {/* Contact Information */}
              {driver.user && (
                <div className="bg-white rounded-lg shadow-md p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Contact Information</h3>
                  <div className="space-y-3">
                    <div className="flex items-center">
                      <Mail size={16} className="text-gray-400 mr-3" />
                      <span className="text-gray-700">{driver.user.email}</span>
                    </div>
                    {driver.user.phone_number && (
                      <div className="flex items-center">
                        <Phone size={16} className="text-gray-400 mr-3" />
                        <span className="text-gray-700">{driver.user.phone_number}</span>
                      </div>
                    )}
                  </div>
                  
                  {auth.user && auth.user.id !== driver.user.id && (
                    <div className="mt-4 pt-4 border-t">
                      <button className="flex items-center px-4 py-2 bg-primary-100 text-primary-700 rounded-md hover:bg-primary-200 transition-colors">
                        <MessageCircle size={16} className="mr-2" />
                        Send Message
                      </button>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Booking Sidebar */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow-md p-6 sticky top-24">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Hire This Driver</h3>
                
                {auth.user ? (
                  driver.is_available && driver.license_verification_status === 'verified' ? (
                    <form onSubmit={handleBooking} className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Start Date & Time
                        </label>
                        <input
                          type="datetime-local"
                          value={data.start_time}
                          onChange={(e) => setData('start_time', e.target.value)}
                          min={new Date().toISOString().slice(0, 16)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
                          required
                        />
                        {errors.start_time && (
                          <p className="mt-1 text-sm text-red-600">{errors.start_time}</p>
                        )}
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          End Date & Time
                        </label>
                        <input
                          type="datetime-local"
                          value={data.end_time}
                          onChange={(e) => setData('end_time', e.target.value)}
                          min={data.start_time || new Date().toISOString().slice(0, 16)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
                          required
                        />
                        {errors.end_time && (
                          <p className="mt-1 text-sm text-red-600">{errors.end_time}</p>
                        )}
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Service Notes (Optional)
                        </label>
                        <textarea
                          value={data.notes}
                          onChange={(e) => setData('notes', e.target.value)}
                          rows={3}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
                          placeholder="Describe your driving needs, destinations, special requirements..."
                        />
                      </div>

                      {/* Price Calculation */}
                      {data.start_time && data.end_time && (
                        <div className="bg-gray-50 p-4 rounded-md">
                          <div className="flex justify-between items-center mb-2">
                            <span className="text-gray-700">Duration:</span>
                            <span className="font-medium">
                              {Math.ceil((new Date(data.end_time).getTime() - new Date(data.start_time).getTime()) / (1000 * 60 * 60))} hours
                            </span>
                          </div>
                          <div className="flex justify-between items-center mb-2">
                            <span className="text-gray-700">Rate:</span>
                            <span className="font-medium">RWF {driver.price_per_hour.toLocaleString()}/hr</span>
                          </div>
                          <div className="border-t pt-2 flex justify-between items-center">
                            <span className="font-semibold text-gray-900">Total:</span>
                            <span className="font-bold text-primary-600 text-lg">
                              RWF {calculatePrice().toLocaleString()}
                            </span>
                          </div>
                        </div>
                      )}

                      <button
                        type="submit"
                        disabled={processing || !data.start_time || !data.end_time}
                        className="w-full bg-primary-600 text-white py-3 rounded-md hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed font-medium"
                      >
                        {processing ? 'Booking...' : 'Book Driver'}
                      </button>
                    </form>
                  ) : (
                    <div className="text-center py-8">
                      <X size={48} className="mx-auto text-red-400 mb-4" />
                      <h4 className="text-lg font-medium text-gray-900 mb-2">Driver Unavailable</h4>
                      <p className="text-gray-600">
                        {!driver.is_available 
                          ? 'This driver is currently not available for booking.'
                          : 'This driver is pending verification.'
                        }
                      </p>
                    </div>
                  )
                ) : (
                  <div className="text-center py-8">
                    <h4 className="text-lg font-medium text-gray-900 mb-2">Sign in to Book</h4>
                    <p className="text-gray-600 mb-4">You need to be logged in to hire this driver.</p>
                    <Link
                      href="/login"
                      className="w-full bg-primary-600 text-white py-3 rounded-md hover:bg-primary-700 font-medium inline-block text-center"
                    >
                      Sign In
                    </Link>
                  </div>
                )}

                {/* Driver License Info */}
                <div className="mt-6 pt-6 border-t">
                  <h4 className="text-sm font-medium text-gray-900 mb-2">License Information</h4>
                  <p className="text-sm text-gray-600">License #: {driver.license_number}</p>
                  <p className="text-sm text-gray-600">
                    Status: <span className={`font-medium ${
                      driver.license_verification_status === 'verified' ? 'text-green-600' : 'text-yellow-600'
                    }`}>
                      {driver.license_verification_status.charAt(0).toUpperCase() + driver.license_verification_status.slice(1)}
                    </span>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
