<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use App\Models\Car;
use App\Models\Driver;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Notifications\BookingCreated;
use App\Notifications\BookingConfirmed;
use App\Notifications\BookingCancelled;

class BookingController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        // Users can only see their own bookings unless they're admin
        if (auth()->user()->role === 'admin') {
            $bookings = Booking::with(['user'])->get();
        } else {
            $bookings = Booking::where('user_id', auth()->id())->get();
        }

        return response()->json($bookings);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'item_type' => 'required|in:car,driver',
            'item_id' => 'required|integer',
            'start_time' => 'required|date|after:now',
            'end_time' => 'required|date|after:start_time',
            'notes' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Check if the item exists and is available
        if ($request->item_type === 'car') {
            $car = Car::findOrFail($request->item_id);
            if (!$car->is_active) {
                return response()->json(['message' => 'Car is not available for booking'], 422);
            }

            // Calculate total price
            $startTime = Carbon::parse($request->start_time);
            $endTime = Carbon::parse($request->end_time);
            $hours = $endTime->diffInHours($startTime);
            $totalPrice = $car->price_per_hour * $hours;

        } elseif ($request->item_type === 'driver') {
            $driver = Driver::findOrFail($request->item_id);
            if (!$driver->is_available) {
                return response()->json(['message' => 'Driver is not available for booking'], 422);
            }

            // Calculate total price
            $startTime = Carbon::parse($request->start_time);
            $endTime = Carbon::parse($request->end_time);
            $hours = $endTime->diffInHours($startTime);
            $totalPrice = $driver->price_per_hour * $hours;
        }

        // Check for booking conflicts
        $conflictingBookings = Booking::where('item_type', $request->item_type)
            ->where('item_id', $request->item_id)
            ->where('status', '!=', 'cancelled')
            ->where(function ($query) use ($request) {
                $query->whereBetween('start_time', [$request->start_time, $request->end_time])
                    ->orWhereBetween('end_time', [$request->start_time, $request->end_time])
                    ->orWhere(function ($query) use ($request) {
                        $query->where('start_time', '<=', $request->start_time)
                            ->where('end_time', '>=', $request->end_time);
                    });
            })
            ->count();

        if ($conflictingBookings > 0) {
            return response()->json(['message' => 'The selected time slot is not available'], 422);
        }

        $booking = Booking::create([
            'user_id' => auth()->id(),
            'item_type' => $request->item_type,
            'item_id' => $request->item_id,
            'start_time' => $request->start_time,
            'end_time' => $request->end_time,
            'total_price' => $totalPrice,
            'status' => 'pending',
            'notes' => $request->notes,
            'is_paid' => false,
        ]);

        // Send notification to the owner/driver
        if ($request->item_type === 'car') {
            $car = Car::find($request->item_id);
            if ($car && $car->owner) {
                $car->owner->notify(new BookingCreated($booking));
            }
        } elseif ($request->item_type === 'driver') {
            $driver = Driver::find($request->item_id);
            if ($driver && $driver->user) {
                $driver->user->notify(new BookingCreated($booking));
            }
        }

        return response()->json([
            'message' => 'Booking created successfully! The owner has been notified.',
            'booking' => $booking
        ], 201);
    }

    /**
     * Display the specified resource.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(string $id)
    {
        $booking = Booking::findOrFail($id);

        // Users can only view their own bookings unless they're admin or the owner of the item
        if (auth()->id() != $booking->user_id && auth()->user()->role !== 'admin') {
            // Check if user is the owner of the car or the driver
            $isOwnerOrDriver = false;

            if ($booking->item_type === 'car') {
                $car = Car::find($booking->item_id);
                if ($car && $car->owner_id === auth()->id()) {
                    $isOwnerOrDriver = true;
                }
            } elseif ($booking->item_type === 'driver') {
                $driver = Driver::find($booking->item_id);
                if ($driver && $driver->user_id === auth()->id()) {
                    $isOwnerOrDriver = true;
                }
            }

            if (!$isOwnerOrDriver) {
                return response()->json(['message' => 'Unauthorized'], 403);
            }
        }

        return response()->json($booking);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, string $id)
    {
        $booking = Booking::findOrFail($id);

        // Only allow updates to pending bookings
        if ($booking->status !== 'pending') {
            return response()->json(['message' => 'Only pending bookings can be updated'], 422);
        }

        // Users can only update their own bookings unless they're admin
        if (auth()->id() != $booking->user_id && auth()->user()->role !== 'admin') {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $validator = Validator::make($request->all(), [
            'start_time' => 'sometimes|date|after:now',
            'end_time' => 'sometimes|date|after:start_time',
            'notes' => 'sometimes|nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // If changing dates, recalculate price and check for conflicts
        if ($request->has('start_time') || $request->has('end_time')) {
            $startTime = $request->has('start_time') ? Carbon::parse($request->start_time) : Carbon::parse($booking->start_time);
            $endTime = $request->has('end_time') ? Carbon::parse($request->end_time) : Carbon::parse($booking->end_time);

            // Check for booking conflicts
            $conflictingBookings = Booking::where('item_type', $booking->item_type)
                ->where('item_id', $booking->item_id)
                ->where('id', '!=', $booking->id)
                ->where('status', '!=', 'cancelled')
                ->where(function ($query) use ($startTime, $endTime) {
                    $query->whereBetween('start_time', [$startTime, $endTime])
                        ->orWhereBetween('end_time', [$startTime, $endTime])
                        ->orWhere(function ($query) use ($startTime, $endTime) {
                            $query->where('start_time', '<=', $startTime)
                                ->where('end_time', '>=', $endTime);
                        });
                })
                ->count();

            if ($conflictingBookings > 0) {
                return response()->json(['message' => 'The selected time slot is not available'], 422);
            }

            // Recalculate total price
            $hours = $endTime->diffInHours($startTime);

            if ($booking->item_type === 'car') {
                $car = Car::find($booking->item_id);
                $totalPrice = $car->price_per_hour * $hours;
            } elseif ($booking->item_type === 'driver') {
                $driver = Driver::find($booking->item_id);
                $totalPrice = $driver->price_per_hour * $hours;
            }

            $booking->total_price = $totalPrice;
        }

        $booking->update($request->only(['start_time', 'end_time', 'notes']));

        return response()->json([
            'message' => 'Booking updated successfully',
            'booking' => $booking
        ]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(string $id)
    {
        $booking = Booking::findOrFail($id);

        // Users can only cancel their own bookings unless they're admin
        if (auth()->id() != $booking->user_id && auth()->user()->role !== 'admin') {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        // Instead of deleting, mark as cancelled
        $booking->update(['status' => 'cancelled']);

        return response()->json([
            'message' => 'Booking cancelled successfully'
        ]);
    }

    /**
     * Get bookings for the authenticated user.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function myBookings()
    {
        $bookings = Booking::where('user_id', auth()->id())->get();

        return response()->json($bookings);
    }

    /**
     * Update booking status (for car owners and drivers).
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateStatus(Request $request, string $id)
    {
        $booking = Booking::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'status' => 'required|in:confirmed,completed,cancelled',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Check if user is authorized to update the status
        $isAuthorized = false;

        if (auth()->user()->role === 'admin') {
            $isAuthorized = true;
        } elseif ($booking->item_type === 'car') {
            $car = Car::find($booking->item_id);
            if ($car && $car->owner_id === auth()->id()) {
                $isAuthorized = true;
            }
        } elseif ($booking->item_type === 'driver') {
            $driver = Driver::find($booking->item_id);
            if ($driver && $driver->user_id === auth()->id()) {
                $isAuthorized = true;
            }
        }

        if (!$isAuthorized) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $booking->update(['status' => $request->status]);

        // Send notification to customer based on status
        if ($request->status === 'confirmed') {
            $booking->user->notify(new BookingConfirmed($booking));
        } elseif ($request->status === 'cancelled') {
            $booking->user->notify(new BookingCancelled($booking));
        }

        return response()->json([
            'message' => 'Booking status updated successfully. Customer has been notified.',
            'status' => $request->status
        ]);
    }
}
