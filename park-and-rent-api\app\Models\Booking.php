<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use App\Models\Car;
use App\Models\Driver;

class Booking extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'item_type',
        'item_id',
        'start_time',
        'end_time',
        'total_price',
        'status',
        'notes',
        'is_paid',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'start_time' => 'datetime',
        'end_time' => 'datetime',
        'total_price' => 'decimal:2',
        'is_paid' => 'boolean',
    ];

    /**
     * Get the user that made the booking.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the booked item (car or driver).
     */
    public function item(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get the car if this booking is for a car.
     */
    public function car(): BelongsTo
    {
        return $this->belongsTo(Car::class, 'item_id')->where('item_type', 'car');
    }

    /**
     * Get the driver if this booking is for a driver.
     */
    public function driver(): BelongsTo
    {
        return $this->belongsTo(Driver::class, 'item_id')->where('item_type', 'driver');
    }
}
