<?php

namespace App\Http\Controllers;

use App\Models\Booking;
use App\Models\Car;
use App\Models\Driver;
use App\Models\User;
use Illuminate\Http\Request;
use Inertia\Inertia;

class DashboardController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function index()
    {
        $user = auth()->user();
        
        // Get user's bookings
        $bookings = Booking::with(['item'])
            ->where('user_id', $user->id)
            ->latest()
            ->take(10)
            ->get();

        // Calculate stats based on user role
        $stats = $this->calculateStats($user);

        // Get additional data based on user role
        $additionalData = $this->getAdditionalData($user);

        return Inertia::render('Dashboard', array_merge([
            'bookings' => $bookings,
            'stats' => $stats,
        ], $additionalData));
    }

    private function calculateStats($user)
    {
        $stats = [
            'total_bookings' => 0,
            'pending_bookings' => 0,
            'total_earnings' => 0,
        ];

        switch ($user->role) {
            case 'admin':
                $stats['total_bookings'] = Booking::count();
                $stats['pending_bookings'] = Booking::where('status', 'pending')->count();
                $stats['total_earnings'] = Booking::where('status', 'completed')->sum('total_price');
                break;

            case 'owner':
                $carIds = Car::where('owner_id', $user->id)->pluck('id');
                $stats['total_bookings'] = Booking::where('item_type', 'car')
                    ->whereIn('item_id', $carIds)
                    ->count();
                $stats['pending_bookings'] = Booking::where('item_type', 'car')
                    ->whereIn('item_id', $carIds)
                    ->where('status', 'pending')
                    ->count();
                $stats['total_earnings'] = Booking::where('item_type', 'car')
                    ->whereIn('item_id', $carIds)
                    ->where('status', 'completed')
                    ->sum('total_price');
                $stats['active_cars'] = Car::where('owner_id', $user->id)
                    ->where('is_active', true)
                    ->count();
                break;

            case 'driver':
                $driver = Driver::where('user_id', $user->id)->first();
                if ($driver) {
                    $stats['total_bookings'] = Booking::where('item_type', 'driver')
                        ->where('item_id', $driver->id)
                        ->count();
                    $stats['pending_bookings'] = Booking::where('item_type', 'driver')
                        ->where('item_id', $driver->id)
                        ->where('status', 'pending')
                        ->count();
                    $stats['total_earnings'] = Booking::where('item_type', 'driver')
                        ->where('item_id', $driver->id)
                        ->where('status', 'completed')
                        ->sum('total_price');
                }
                break;

            case 'client':
            default:
                $stats['total_bookings'] = Booking::where('user_id', $user->id)->count();
                $stats['pending_bookings'] = Booking::where('user_id', $user->id)
                    ->where('status', 'pending')
                    ->count();
                break;
        }

        return $stats;
    }

    private function getAdditionalData($user)
    {
        $data = [];

        switch ($user->role) {
            case 'owner':
                $data['cars'] = Car::where('owner_id', $user->id)->get();
                break;

            case 'driver':
                $data['driver'] = Driver::where('user_id', $user->id)->first();
                break;
        }

        return $data;
    }
}
