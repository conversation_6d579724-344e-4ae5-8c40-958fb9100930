import{r as n,j as e,$ as v,Y as a,m as g}from"./app-Cj-kSxmT.js";import{C as y}from"./car-CXkXzXL9.js";import{S as w}from"./search-BLfvhpd5.js";import{F as _}from"./funnel-C0C45j41.js";import{U as j}from"./user-round-Dc3ptzYJ.js";import{M as S}from"./map-pin-DpBG7oTH.js";import{A as C,S as k}from"./star-CCEgP9Rb.js";import{C as F}from"./clock-B-cuJnHn.js";function H({auth:m,drivers:l,filters:t}){const[x,d]=n.useState(t.search||""),[o,f]=n.useState(!1),[r,i]=n.useState({location:t.location||"",min_price:t.min_price||"",max_price:t.max_price||"",min_experience:t.min_experience||"",specialty:t.specialty||""}),u=s=>{s.preventDefault(),g.get("/drivers",{search:x,...r},{preserveState:!0,preserveScroll:!0})},h=()=>{d(""),i({location:"",min_price:"",max_price:"",min_experience:"",specialty:""}),g.get("/drivers")},N=[...new Set(l.flatMap(s=>s.specialties||[]))],b=s=>Array.from({length:5},(p,c)=>e.jsx(k,{size:16,className:c<Math.floor(s)?"text-yellow-400 fill-current":"text-gray-300"},c));return e.jsxs(e.Fragment,{children:[e.jsx(v,{title:"Hire a Driver"}),e.jsxs("div",{className:"min-h-screen bg-gray-50",children:[e.jsx("header",{className:"bg-white shadow-md sticky top-0 z-50",children:e.jsx("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"flex justify-between items-center py-4",children:[e.jsxs(a,{href:"/",className:"flex items-center space-x-2",children:[e.jsx(y,{className:"h-8 w-8 text-primary-600"}),e.jsx("span",{className:"text-xl font-bold text-gray-900",children:"Park & Rent"})]}),e.jsxs("nav",{className:"hidden md:flex space-x-8",children:[e.jsx(a,{href:"/",className:"text-gray-700 hover:text-primary-600",children:"Home"}),e.jsx(a,{href:"/cars",className:"text-gray-700 hover:text-primary-600",children:"Browse Cars"}),e.jsx(a,{href:"/drivers",className:"text-primary-600 font-medium",children:"Hire a Driver"})]}),e.jsx("div",{className:"flex items-center space-x-4",children:m.user?e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("span",{className:"text-gray-700",children:["Welcome, ",m.user.name]}),e.jsx(a,{href:"/dashboard",className:"bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700",children:"Dashboard"})]}):e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx(a,{href:"/login",className:"text-gray-700 hover:text-primary-600",children:"Log In"}),e.jsx(a,{href:"/register",className:"bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700",children:"Sign Up"})]})})]})})}),e.jsx("section",{className:"bg-primary-600 text-white py-12",children:e.jsx("div",{className:"container mx-auto px-4",children:e.jsxs("div",{className:"text-center",children:[e.jsx("h1",{className:"text-4xl font-bold mb-4",children:"Hire Professional Drivers"}),e.jsxs("p",{className:"text-xl text-primary-100 mb-8",children:["Find experienced drivers from our network of ",l.length," verified professionals"]}),e.jsx("form",{onSubmit:u,className:"max-w-4xl mx-auto",children:e.jsxs("div",{className:"bg-white rounded-lg p-4 shadow-lg",children:[e.jsxs("div",{className:"flex flex-col md:flex-row gap-4",children:[e.jsx("div",{className:"flex-1",children:e.jsxs("div",{className:"relative",children:[e.jsx(w,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5"}),e.jsx("input",{type:"text",placeholder:"Search by name, location, or specialty...",value:x,onChange:s=>d(s.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500 text-gray-900"})]})}),e.jsxs("button",{type:"button",onClick:()=>f(!o),className:"flex items-center justify-center px-6 py-3 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50",children:[e.jsx(_,{className:"h-5 w-5 mr-2"}),"Filters"]}),e.jsx("button",{type:"submit",className:"bg-primary-600 text-white px-8 py-3 rounded-md hover:bg-primary-700 font-medium",children:"Search"})]}),o&&e.jsxs("div",{className:"mt-4 pt-4 border-t border-gray-200",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Location"}),e.jsx("input",{type:"text",placeholder:"Enter location",value:r.location,onChange:s=>i({...r,location:s.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500 text-gray-900"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Specialty"}),e.jsxs("select",{value:r.specialty,onChange:s=>i({...r,specialty:s.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500 text-gray-900",children:[e.jsx("option",{value:"",children:"All Specialties"}),N.map(s=>e.jsx("option",{value:s,children:s},s))]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Min Experience (years)"}),e.jsx("input",{type:"number",placeholder:"0",value:r.min_experience,onChange:s=>i({...r,min_experience:s.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500 text-gray-900"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Min Price (RWF/hr)"}),e.jsx("input",{type:"number",placeholder:"0",value:r.min_price,onChange:s=>i({...r,min_price:s.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500 text-gray-900"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Max Price (RWF/hr)"}),e.jsx("input",{type:"number",placeholder:"50000",value:r.max_price,onChange:s=>i({...r,max_price:s.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500 text-gray-900"})]})]}),e.jsx("div",{className:"mt-4 flex justify-end",children:e.jsx("button",{type:"button",onClick:h,className:"text-gray-600 hover:text-gray-800 mr-4",children:"Clear Filters"})})]})]})})]})})}),e.jsx("section",{className:"py-12",children:e.jsx("div",{className:"container mx-auto px-4",children:l.length>0?e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:l.map(s=>e.jsx("div",{className:"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow",children:e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx("div",{className:"w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mr-4 overflow-hidden",children:s.profile_image?e.jsx("img",{src:s.profile_image,alt:s.name,className:"w-full h-full object-cover"}):e.jsx(j,{size:32,className:"text-gray-400"})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:"text-xl font-semibold text-gray-900",children:s.name}),e.jsxs("div",{className:"flex items-center text-gray-600 mb-1",children:[e.jsx(S,{size:14,className:"mr-1"}),e.jsx("span",{className:"text-sm",children:s.location})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex items-center mr-2",children:b(s.rating)}),e.jsxs("span",{className:"text-sm text-gray-600",children:[s.rating,"/5 (",s.reviews," reviews)"]})]})]})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 mb-4",children:[e.jsxs("div",{className:"text-center p-3 bg-gray-50 rounded-lg",children:[e.jsxs("div",{className:"flex items-center justify-center mb-1",children:[e.jsx(C,{size:16,className:"text-primary-600 mr-1"}),e.jsx("span",{className:"text-sm font-medium text-gray-700",children:"Experience"})]}),e.jsxs("div",{className:"text-lg font-bold text-gray-900",children:[s.experience," years"]})]}),e.jsxs("div",{className:"text-center p-3 bg-gray-50 rounded-lg",children:[e.jsxs("div",{className:"flex items-center justify-center mb-1",children:[e.jsx(F,{size:16,className:"text-primary-600 mr-1"}),e.jsx("span",{className:"text-sm font-medium text-gray-700",children:"Age"})]}),e.jsxs("div",{className:"text-lg font-bold text-gray-900",children:[s.age," years"]})]})]}),s.specialties&&s.specialties.length>0&&e.jsxs("div",{className:"mb-4",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"Specialties"}),e.jsxs("div",{className:"flex flex-wrap gap-2",children:[s.specialties.slice(0,4).map((p,c)=>e.jsx("span",{className:"bg-primary-100 text-primary-800 text-xs px-2 py-1 rounded-full",children:p},c)),s.specialties.length>4&&e.jsxs("span",{className:"text-xs text-gray-500",children:["+",s.specialties.length-4," more"]})]})]}),s.availability_notes&&e.jsxs("div",{className:"mb-4",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-700 mb-1",children:"Availability"}),e.jsx("p",{className:"text-sm text-gray-600",children:s.availability_notes})]}),e.jsx("div",{className:"mb-4",children:e.jsxs("span",{className:`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${s.license_verification_status==="verified"?"bg-green-100 text-green-800":s.license_verification_status==="pending"?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"}`,children:[s.license_verification_status==="verified"&&"✓ ",s.license_verification_status==="verified"?"Verified Driver":s.license_verification_status==="pending"?"Verification Pending":"Not Verified"]})}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsxs("span",{className:"text-2xl font-bold text-primary-600",children:["RWF ",s.price_per_hour.toLocaleString()]}),e.jsx("span",{className:"text-sm text-gray-600",children:"/hr"})]}),e.jsx(a,{href:`/drivers/${s.id}`,className:"bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700 transition duration-200 text-sm font-medium",children:"View Profile"})]})]})},s.id))}):e.jsxs("div",{className:"text-center py-12",children:[e.jsx(j,{size:64,className:"mx-auto text-gray-400 mb-4"}),e.jsx("h3",{className:"text-xl font-medium text-gray-900 mb-2",children:"No drivers found"}),e.jsx("p",{className:"text-gray-600 mb-6",children:"Try adjusting your search criteria or browse all available drivers."}),e.jsx("button",{onClick:h,className:"bg-primary-600 text-white px-6 py-2 rounded-md hover:bg-primary-700",children:"Clear Filters"})]})})}),e.jsx("footer",{className:"bg-gray-900 text-white py-12",children:e.jsxs("div",{className:"container mx-auto px-4",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center space-x-2 mb-4",children:[e.jsx(y,{className:"h-8 w-8 text-primary-400"}),e.jsx("span",{className:"text-xl font-bold",children:"Park & Rent"})]}),e.jsx("p",{className:"text-gray-400",children:"The trusted peer-to-peer car rental platform connecting car owners with renters."})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-lg font-semibold mb-4",children:"Quick Links"}),e.jsxs("ul",{className:"space-y-2",children:[e.jsx("li",{children:e.jsx(a,{href:"/cars",className:"text-gray-400 hover:text-white",children:"Browse Cars"})}),e.jsx("li",{children:e.jsx(a,{href:"/drivers",className:"text-gray-400 hover:text-white",children:"Find Drivers"})}),e.jsx("li",{children:e.jsx(a,{href:"/register",className:"text-gray-400 hover:text-white",children:"List Your Car"})})]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-lg font-semibold mb-4",children:"Support"}),e.jsxs("ul",{className:"space-y-2",children:[e.jsx("li",{children:e.jsx("a",{href:"#",className:"text-gray-400 hover:text-white",children:"Help Center"})}),e.jsx("li",{children:e.jsx("a",{href:"#",className:"text-gray-400 hover:text-white",children:"Safety"})}),e.jsx("li",{children:e.jsx("a",{href:"#",className:"text-gray-400 hover:text-white",children:"Contact Us"})})]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-lg font-semibold mb-4",children:"Contact"}),e.jsxs("p",{className:"text-gray-400",children:["Email: <EMAIL>",e.jsx("br",{}),"Phone: 0788613669"]})]})]}),e.jsx("div",{className:"border-t border-gray-800 mt-8 pt-8 text-center",children:e.jsx("p",{className:"text-gray-400",children:"© 2025 Park & Rent. All rights reserved."})})]})})]})]})}export{H as default};
