<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class FileUploadController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function uploadCarImages(Request $request)
    {
        $request->validate([
            'images' => 'required|array|max:10',
            'images.*' => 'image|mimes:jpeg,png,jpg,gif,webp|max:5120', // 5MB max
        ]);

        $uploadedFiles = [];

        foreach ($request->file('images') as $image) {
            $filename = $this->generateUniqueFilename($image);
            $path = $image->storeAs('cars', $filename, 'public');
            $uploadedFiles[] = Storage::url($path);
        }

        return response()->json([
            'success' => true,
            'files' => $uploadedFiles,
            'message' => 'Images uploaded successfully'
        ]);
    }

    public function uploadDriverImage(Request $request)
    {
        $request->validate([
            'image' => 'required|image|mimes:jpeg,png,jpg,gif,webp|max:5120', // 5MB max
        ]);

        $image = $request->file('image');
        $filename = $this->generateUniqueFilename($image);
        $path = $image->storeAs('drivers', $filename, 'public');

        return response()->json([
            'success' => true,
            'file' => Storage::url($path),
            'message' => 'Image uploaded successfully'
        ]);
    }

    public function uploadLicenseDocument(Request $request)
    {
        $request->validate([
            'document' => 'required|file|mimes:pdf,jpeg,png,jpg|max:10240', // 10MB max
        ]);

        $document = $request->file('document');
        $filename = $this->generateUniqueFilename($document);
        $path = $document->storeAs('licenses', $filename, 'public');

        return response()->json([
            'success' => true,
            'file' => Storage::url($path),
            'message' => 'Document uploaded successfully'
        ]);
    }

    public function uploadChatFile(Request $request)
    {
        $request->validate([
            'file' => 'required|file|max:20480', // 20MB max
            'chat_id' => 'required|exists:chats,id',
        ]);

        // Verify user is part of the chat
        $chat = \App\Models\Chat::findOrFail($request->chat_id);
        if ($chat->user_id !== auth()->id() && $chat->recipient_id !== auth()->id()) {
            abort(403);
        }

        $file = $request->file('file');
        $filename = $this->generateUniqueFilename($file);
        $path = $file->storeAs('chat-files', $filename, 'public');

        return response()->json([
            'success' => true,
            'file' => Storage::url($path),
            'filename' => $file->getClientOriginalName(),
            'size' => $file->getSize(),
            'message' => 'File uploaded successfully'
        ]);
    }

    public function deleteFile(Request $request)
    {
        $request->validate([
            'file_path' => 'required|string',
        ]);

        $filePath = $request->file_path;
        
        // Remove the storage URL prefix to get the actual path
        $actualPath = str_replace('/storage/', '', $filePath);

        if (Storage::disk('public')->exists($actualPath)) {
            Storage::disk('public')->delete($actualPath);
            
            return response()->json([
                'success' => true,
                'message' => 'File deleted successfully'
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'File not found'
        ], 404);
    }

    private function generateUniqueFilename($file)
    {
        $extension = $file->getClientOriginalExtension();
        $filename = Str::random(40) . '.' . $extension;
        
        return $filename;
    }

    public function getFileInfo(Request $request)
    {
        $request->validate([
            'file_path' => 'required|string',
        ]);

        $filePath = $request->file_path;
        $actualPath = str_replace('/storage/', '', $filePath);

        if (Storage::disk('public')->exists($actualPath)) {
            $size = Storage::disk('public')->size($actualPath);
            $lastModified = Storage::disk('public')->lastModified($actualPath);
            
            return response()->json([
                'success' => true,
                'file' => [
                    'path' => $filePath,
                    'size' => $size,
                    'last_modified' => date('Y-m-d H:i:s', $lastModified),
                    'url' => Storage::url($actualPath)
                ]
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'File not found'
        ], 404);
    }
}
