import{r as N,j as e,v as L,$ as T,Y as o}from"./app-Cj-kSxmT.js";import{c as v,C as E}from"./car-CXkXzXL9.js";import{C as Y}from"./circle-alert-CML8Sh-8.js";import{X as B}from"./x-D2_ybCL2.js";import{A as G}from"./arrow-left-DM9sBmRu.js";import{M as U}from"./map-pin-DpBG7oTH.js";/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const W=[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]],R=v("dollar-sign",W);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const K=[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}]],O=v("file",K);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const V=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]],J=v("image",V);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const X=[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]],Z=v("save",X);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Q=[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]],ee=v("upload",Q);function se({onFileSelect:w,accept:p="image/*",multiple:g=!1,maxSize:t=5,maxFiles:l=1,existingFiles:y=[],onRemoveExisting:u,className:a="",label:f="Upload Files",description:F="Drag and drop files here, or click to select files"}){const[k,b]=N.useState(!1),[h,C]=N.useState([]),[_,j]=N.useState([]),s=N.useRef(null),d=r=>r.size>t*1024*1024?`File "${r.name}" is too large. Maximum size is ${t}MB.`:p!=="*"&&!r.type.match(p.replace(/\*/g,".*"))?`File "${r.name}" is not a supported file type.`:null,n=r=>{const i=Array.from(r),c=[],x=[];if(y.length+h.length+i.length>l){c.push(`Maximum ${l} file${l>1?"s":""} allowed.`),j(c);return}if(i.forEach(M=>{const A=d(M);A?c.push(A):x.push(M)}),c.length>0){j(c);return}j([]);const D=g?[...h,...x]:x;C(D),w(D)},m=r=>{r.preventDefault(),r.stopPropagation(),r.type==="dragenter"||r.type==="dragover"?b(!0):r.type==="dragleave"&&b(!1)},$=r=>{r.preventDefault(),r.stopPropagation(),b(!1),r.dataTransfer.files&&r.dataTransfer.files[0]&&n(r.dataTransfer.files)},P=r=>{r.preventDefault(),r.target.files&&r.target.files[0]&&n(r.target.files)},z=r=>{const i=h.filter((c,x)=>x!==r);C(i),w(i)},H=r=>{u&&u(r)},q=()=>{var r;(r=s.current)==null||r.click()},S=r=>{var c;const i=(c=r.split(".").pop())==null?void 0:c.toLowerCase();return["jpg","jpeg","png","gif","webp"].includes(i||"")?e.jsx(J,{size:20,className:"text-blue-600"}):e.jsx(O,{size:20,className:"text-gray-600"})},I=r=>{if(r===0)return"0 Bytes";const i=1024,c=["Bytes","KB","MB","GB"],x=Math.floor(Math.log(r)/Math.log(i));return parseFloat((r/Math.pow(i,x)).toFixed(2))+" "+c[x]};return e.jsxs("div",{className:`w-full ${a}`,children:[f&&e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:f}),e.jsxs("div",{className:`relative border-2 border-dashed rounded-lg p-6 transition-colors ${k?"border-primary-500 bg-primary-50":"border-gray-300 hover:border-gray-400"}`,onDragEnter:m,onDragLeave:m,onDragOver:m,onDrop:$,onClick:q,children:[e.jsx("input",{ref:s,type:"file",multiple:g,accept:p,onChange:P,className:"hidden"}),e.jsxs("div",{className:"text-center cursor-pointer",children:[e.jsx(ee,{className:`mx-auto h-12 w-12 ${k?"text-primary-600":"text-gray-400"}`}),e.jsxs("div",{className:"mt-4",children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:f}),e.jsx("p",{className:"text-sm text-gray-600 mt-1",children:F}),e.jsxs("p",{className:"text-xs text-gray-500 mt-2",children:[p==="image/*"?"PNG, JPG, GIF up to":"Files up to"," ",t,"MB",g&&` (max ${l} files)`]})]})]})]}),_.length>0&&e.jsx("div",{className:"mt-3 space-y-1",children:_.map((r,i)=>e.jsxs("div",{className:"flex items-center text-sm text-red-600",children:[e.jsx(Y,{size:16,className:"mr-2"}),r]},i))}),y.length>0&&e.jsxs("div",{className:"mt-4",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-900 mb-2",children:"Current Files"}),e.jsx("div",{className:"space-y-2",children:y.map((r,i)=>e.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[e.jsxs("div",{className:"flex items-center",children:[S(r),e.jsx("span",{className:"ml-3 text-sm text-gray-900 truncate",children:r.split("/").pop()})]}),u&&e.jsx("button",{type:"button",onClick:()=>H(i),className:"ml-3 text-red-600 hover:text-red-800",children:e.jsx(B,{size:16})})]},i))})]}),h.length>0&&e.jsxs("div",{className:"mt-4",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-900 mb-2",children:"Selected Files"}),e.jsx("div",{className:"space-y-2",children:h.map((r,i)=>e.jsxs("div",{className:"flex items-center justify-between p-3 bg-blue-50 rounded-lg",children:[e.jsxs("div",{className:"flex items-center",children:[S(r.name),e.jsxs("div",{className:"ml-3",children:[e.jsx("p",{className:"text-sm text-gray-900 truncate",children:r.name}),e.jsx("p",{className:"text-xs text-gray-600",children:I(r.size)})]})]}),e.jsx("button",{type:"button",onClick:()=>z(i),className:"ml-3 text-red-600 hover:text-red-800",children:e.jsx(B,{size:16})})]},i))})]})]})}function ne({auth:w}){const[p,g]=N.useState([]),{data:t,setData:l,post:y,processing:u,errors:a}=L({make:"",model:"",year:new Date().getFullYear(),color:"",license_plate:"",location:"",description:"",price_per_hour:"",fuel_type:"petrol",transmission:"manual",seats:4,features:[],is_active:!0}),f=new Date().getFullYear(),F=Array.from({length:30},(s,d)=>f-d),k=[{value:"petrol",label:"Petrol"},{value:"diesel",label:"Diesel"},{value:"hybrid",label:"Hybrid"},{value:"electric",label:"Electric"}],b=[{value:"manual",label:"Manual"},{value:"automatic",label:"Automatic"}],h=["Air Conditioning","GPS Navigation","Bluetooth","USB Charging","Backup Camera","Parking Sensors","Sunroof","Leather Seats","Heated Seats","Cruise Control","Keyless Entry","Push Start","Premium Sound System","WiFi Hotspot","Child Safety Locks","Anti-lock Brakes (ABS)","Airbags","Traction Control"],C=s=>{const d=t.features.includes(s)?t.features.filter(n=>n!==s):[...t.features,s];l("features",d)},_=s=>{g(s)},j=s=>{s.preventDefault();const d=new FormData;Object.entries(t).forEach(([n,m])=>{n==="features"?d.append(n,JSON.stringify(m)):d.append(n,m.toString())}),p.forEach((n,m)=>{d.append(`images[${m}]`,n)}),y("/cars",{data:d,forceFormData:!0})};return e.jsxs(e.Fragment,{children:[e.jsx(T,{title:"Add New Car"}),e.jsxs("div",{className:"min-h-screen bg-gray-50",children:[e.jsx("header",{className:"bg-white shadow-md sticky top-0 z-50",children:e.jsx("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"flex justify-between items-center py-4",children:[e.jsxs(o,{href:"/",className:"flex items-center space-x-2",children:[e.jsx(E,{className:"h-8 w-8 text-primary-600"}),e.jsx("span",{className:"text-xl font-bold text-gray-900",children:"Park & Rent"})]}),e.jsxs("nav",{className:"hidden md:flex space-x-8",children:[e.jsx(o,{href:"/",className:"text-gray-700 hover:text-primary-600",children:"Home"}),e.jsx(o,{href:"/cars",className:"text-gray-700 hover:text-primary-600",children:"Browse Cars"}),e.jsx(o,{href:"/drivers",className:"text-gray-700 hover:text-primary-600",children:"Hire a Driver"}),e.jsx(o,{href:"/dashboard",className:"text-gray-700 hover:text-primary-600",children:"Dashboard"})]}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("span",{className:"text-gray-700",children:["Welcome, ",w.user.name]}),e.jsx(o,{href:"/dashboard",className:"bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700",children:"Dashboard"})]})]})})}),e.jsx("div",{className:"bg-white border-b",children:e.jsx("div",{className:"container mx-auto px-4 py-3",children:e.jsxs("nav",{className:"flex items-center space-x-2 text-sm",children:[e.jsx(o,{href:"/",className:"text-gray-600 hover:text-primary-600",children:"Home"}),e.jsx("span",{className:"text-gray-400",children:"/"}),e.jsx(o,{href:"/dashboard",className:"text-gray-600 hover:text-primary-600",children:"Dashboard"}),e.jsx("span",{className:"text-gray-400",children:"/"}),e.jsx("span",{className:"text-gray-900",children:"Add New Car"})]})})}),e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs("div",{className:"max-w-4xl mx-auto",children:[e.jsx("div",{className:"flex items-center justify-between mb-8",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(o,{href:"/dashboard",className:"mr-4 p-2 hover:bg-gray-100 rounded-full",children:e.jsx(G,{size:20})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Add New Car"}),e.jsx("p",{className:"text-gray-600 mt-1",children:"List your car for rent on Park & Rent"})]})]})}),e.jsxs("form",{onSubmit:j,className:"space-y-8",children:[e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[e.jsx("h2",{className:"text-lg font-semibold text-gray-900 mb-6",children:"Basic Information"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Make *"}),e.jsx("input",{type:"text",value:t.make,onChange:s=>l("make",s.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500",placeholder:"e.g., Toyota, Honda, BMW",required:!0}),a.make&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:a.make})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Model *"}),e.jsx("input",{type:"text",value:t.model,onChange:s=>l("model",s.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500",placeholder:"e.g., Camry, Civic, X5",required:!0}),a.model&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:a.model})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Year *"}),e.jsx("select",{value:t.year,onChange:s=>l("year",parseInt(s.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500",required:!0,children:F.map(s=>e.jsx("option",{value:s,children:s},s))}),a.year&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:a.year})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Color"}),e.jsx("input",{type:"text",value:t.color,onChange:s=>l("color",s.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500",placeholder:"e.g., White, Black, Silver"}),a.color&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:a.color})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"License Plate"}),e.jsx("input",{type:"text",value:t.license_plate,onChange:s=>l("license_plate",s.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500",placeholder:"e.g., ABC 123"}),a.license_plate&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:a.license_plate})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Location *"}),e.jsxs("div",{className:"relative",children:[e.jsx(U,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"}),e.jsx("input",{type:"text",value:t.location,onChange:s=>l("location",s.target.value),className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500",placeholder:"e.g., Kigali, Nyamirambo",required:!0})]}),a.location&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:a.location})]})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Description"}),e.jsx("textarea",{value:t.description,onChange:s=>l("description",s.target.value),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500",placeholder:"Describe your car, its condition, and any special notes for renters..."}),a.description&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:a.description})]})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[e.jsx("h2",{className:"text-lg font-semibold text-gray-900 mb-6",children:"Specifications"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Fuel Type *"}),e.jsx("select",{value:t.fuel_type,onChange:s=>l("fuel_type",s.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500",required:!0,children:k.map(s=>e.jsx("option",{value:s.value,children:s.label},s.value))}),a.fuel_type&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:a.fuel_type})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Transmission *"}),e.jsx("select",{value:t.transmission,onChange:s=>l("transmission",s.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500",required:!0,children:b.map(s=>e.jsx("option",{value:s.value,children:s.label},s.value))}),a.transmission&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:a.transmission})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Number of Seats *"}),e.jsx("select",{value:t.seats,onChange:s=>l("seats",parseInt(s.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500",required:!0,children:[2,4,5,6,7,8,9].map(s=>e.jsxs("option",{value:s,children:[s," seats"]},s))}),a.seats&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:a.seats})]})]})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[e.jsx("h2",{className:"text-lg font-semibold text-gray-900 mb-6",children:"Pricing"}),e.jsxs("div",{className:"max-w-md",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Price per Hour (RWF) *"}),e.jsxs("div",{className:"relative",children:[e.jsx(R,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"}),e.jsx("input",{type:"number",value:t.price_per_hour,onChange:s=>l("price_per_hour",s.target.value),className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500",placeholder:"e.g., 5000",min:"0",step:"100",required:!0})]}),a.price_per_hour&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:a.price_per_hour}),e.jsx("p",{className:"mt-1 text-sm text-gray-600",children:"Set a competitive hourly rate for your car"})]})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[e.jsx("h2",{className:"text-lg font-semibold text-gray-900 mb-6",children:"Features & Amenities"}),e.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3",children:h.map(s=>e.jsxs("label",{className:"flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50",children:[e.jsx("input",{type:"checkbox",checked:t.features.includes(s),onChange:()=>C(s),className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"}),e.jsx("span",{className:"ml-2 text-sm text-gray-700",children:s})]},s))}),a.features&&e.jsx("p",{className:"mt-2 text-sm text-red-600",children:a.features})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[e.jsx("h2",{className:"text-lg font-semibold text-gray-900 mb-6",children:"Car Images"}),e.jsx(se,{onFileSelect:_,accept:"image/*",multiple:!0,maxSize:5,maxFiles:10,label:"Upload Car Images",description:"Add photos of your car from different angles. Good photos attract more renters!"}),a.images&&e.jsx("p",{className:"mt-2 text-sm text-red-600",children:a.images})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[e.jsx("h2",{className:"text-lg font-semibold text-gray-900 mb-6",children:"Availability"}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",id:"is_active",checked:t.is_active,onChange:s=>l("is_active",s.target.checked),className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"}),e.jsx("label",{htmlFor:"is_active",className:"ml-2 text-sm text-gray-700",children:"Make this car available for rent immediately"})]}),e.jsx("p",{className:"mt-1 text-sm text-gray-600",children:"You can change this setting later from your dashboard"})]}),e.jsxs("div",{className:"flex justify-end space-x-4",children:[e.jsx(o,{href:"/dashboard",className:"px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50",children:"Cancel"}),e.jsxs("button",{type:"submit",disabled:u,className:"flex items-center px-6 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed",children:[e.jsx(Z,{size:16,className:"mr-2"}),u?"Adding Car...":"Add Car"]})]})]})]})})]})]})}export{ne as default};
