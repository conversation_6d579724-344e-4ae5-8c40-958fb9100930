import{r as h,R as se}from"./vendor-DbAb9B2p.js";/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function B(){return B=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},B.apply(this,arguments)}var R;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(R||(R={}));const z="popstate";function ue(e){e===void 0&&(e={});function t(r,a){let{pathname:l,search:i,hash:o}=r.location;return $("",{pathname:l,search:i,hash:o},a.state&&a.state.usr||null,a.state&&a.state.key||"default")}function n(r,a){return typeof a=="string"?a:T(a)}return he(t,n,null,e)}function g(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function Y(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function ce(){return Math.random().toString(36).substr(2,8)}function A(e,t){return{usr:e.state,key:e.key,idx:t}}function $(e,t,n,r){return n===void 0&&(n=null),B({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?L(t):t,{state:n,key:t&&t.key||r||ce()})}function T(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function L(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function he(e,t,n,r){r===void 0&&(r={});let{window:a=document.defaultView,v5Compat:l=!1}=r,i=a.history,o=R.Pop,s=null,c=f();c==null&&(c=0,i.replaceState(B({},i.state,{idx:c}),""));function f(){return(i.state||{idx:null}).idx}function u(){o=R.Pop;let d=f(),p=d==null?null:d-c;c=d,s&&s({action:o,location:v.location,delta:p})}function m(d,p){o=R.Push;let E=$(v.location,d,p);c=f()+1;let C=A(E,c),P=v.createHref(E);try{i.pushState(C,"",P)}catch(U){if(U instanceof DOMException&&U.name==="DataCloneError")throw U;a.location.assign(P)}l&&s&&s({action:o,location:v.location,delta:1})}function x(d,p){o=R.Replace;let E=$(v.location,d,p);c=f();let C=A(E,c),P=v.createHref(E);i.replaceState(C,"",P),l&&s&&s({action:o,location:v.location,delta:0})}function y(d){let p=a.location.origin!=="null"?a.location.origin:a.location.href,E=typeof d=="string"?d:T(d);return E=E.replace(/ $/,"%20"),g(p,"No window.location.(origin|href) available to create URL for href: "+E),new URL(E,p)}let v={get action(){return o},get location(){return e(a,i)},listen(d){if(s)throw new Error("A history only accepts one active listener");return a.addEventListener(z,u),s=d,()=>{a.removeEventListener(z,u),s=null}},createHref(d){return t(a,d)},createURL:y,encodeLocation(d){let p=y(d);return{pathname:p.pathname,search:p.search,hash:p.hash}},push:m,replace:x,go(d){return i.go(d)}};return v}var K;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(K||(K={}));function fe(e,t,n){return n===void 0&&(n="/"),de(e,t,n,!1)}function de(e,t,n,r){let a=typeof t=="string"?L(t):t,l=F(a.pathname||"/",n);if(l==null)return null;let i=Z(e);pe(i);let o=null;for(let s=0;o==null&&s<i.length;++s){let c=be(l);o=we(i[s],c,r)}return o}function Z(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let a=(l,i,o)=>{let s={relativePath:o===void 0?l.path||"":o,caseSensitive:l.caseSensitive===!0,childrenIndex:i,route:l};s.relativePath.startsWith("/")&&(g(s.relativePath.startsWith(r),'Absolute route path "'+s.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),s.relativePath=s.relativePath.slice(r.length));let c=b([r,s.relativePath]),f=n.concat(s);l.children&&l.children.length>0&&(g(l.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+c+'".')),Z(l.children,t,f,c)),!(l.path==null&&!l.index)&&t.push({path:c,score:Ee(c,l.index),routesMeta:f})};return e.forEach((l,i)=>{var o;if(l.path===""||!((o=l.path)!=null&&o.includes("?")))a(l,i);else for(let s of ee(l.path))a(l,i,s)}),t}function ee(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,a=n.endsWith("?"),l=n.replace(/\?$/,"");if(r.length===0)return a?[l,""]:[l];let i=ee(r.join("/")),o=[];return o.push(...i.map(s=>s===""?l:[l,s].join("/"))),a&&o.push(...i),o.map(s=>e.startsWith("/")&&s===""?"/":s)}function pe(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:Pe(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const me=/^:[\w-]+$/,ve=3,ge=2,ye=1,xe=10,Ce=-2,q=e=>e==="*";function Ee(e,t){let n=e.split("/"),r=n.length;return n.some(q)&&(r+=Ce),t&&(r+=ge),n.filter(a=>!q(a)).reduce((a,l)=>a+(me.test(l)?ve:l===""?ye:xe),r)}function Pe(e,t){return e.length===t.length&&e.slice(0,-1).every((r,a)=>r===t[a])?e[e.length-1]-t[t.length-1]:0}function we(e,t,n){let{routesMeta:r}=e,a={},l="/",i=[];for(let o=0;o<r.length;++o){let s=r[o],c=o===r.length-1,f=l==="/"?t:t.slice(l.length)||"/",u=G({path:s.relativePath,caseSensitive:s.caseSensitive,end:c},f),m=s.route;if(!u&&c&&n&&!r[r.length-1].route.index&&(u=G({path:s.relativePath,caseSensitive:s.caseSensitive,end:!1},f)),!u)return null;Object.assign(a,u.params),i.push({params:a,pathname:b([l,u.pathname]),pathnameBase:Oe(b([l,u.pathnameBase])),route:m}),u.pathnameBase!=="/"&&(l=b([l,u.pathnameBase]))}return i}function G(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=Re(e.path,e.caseSensitive,e.end),a=t.match(n);if(!a)return null;let l=a[0],i=l.replace(/(.)\/+$/,"$1"),o=a.slice(1);return{params:r.reduce((c,f,u)=>{let{paramName:m,isOptional:x}=f;if(m==="*"){let v=o[u]||"";i=l.slice(0,l.length-v.length).replace(/(.)\/+$/,"$1")}const y=o[u];return x&&!y?c[m]=void 0:c[m]=(y||"").replace(/%2F/g,"/"),c},{}),pathname:l,pathnameBase:i,pattern:e}}function Re(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),Y(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(i,o,s)=>(r.push({paramName:o,isOptional:s!=null}),s?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),a+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?a+="\\/*$":e!==""&&e!=="/"&&(a+="(?:(?=\\/|$))"),[new RegExp(a,t?void 0:"i"),r]}function be(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return Y(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function F(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function Se(e,t){t===void 0&&(t="/");let{pathname:n,search:r="",hash:a=""}=typeof e=="string"?L(e):e;return{pathname:n?n.startsWith("/")?n:Ue(n,t):t,search:Be(r),hash:Ie(a)}}function Ue(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(a=>{a===".."?n.length>1&&n.pop():a!=="."&&n.push(a)}),n.length>1?n.join("/"):"/"}function _(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function Le(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function V(e,t){let n=Le(e);return t?n.map((r,a)=>a===n.length-1?r.pathname:r.pathnameBase):n.map(r=>r.pathnameBase)}function D(e,t,n,r){r===void 0&&(r=!1);let a;typeof e=="string"?a=L(e):(a=B({},e),g(!a.pathname||!a.pathname.includes("?"),_("?","pathname","search",a)),g(!a.pathname||!a.pathname.includes("#"),_("#","pathname","hash",a)),g(!a.search||!a.search.includes("#"),_("#","search","hash",a)));let l=e===""||a.pathname==="",i=l?"/":a.pathname,o;if(i==null)o=n;else{let u=t.length-1;if(!r&&i.startsWith("..")){let m=i.split("/");for(;m[0]==="..";)m.shift(),u-=1;a.pathname=m.join("/")}o=u>=0?t[u]:"/"}let s=Se(a,o),c=i&&i!=="/"&&i.endsWith("/"),f=(l||i===".")&&n.endsWith("/");return!s.pathname.endsWith("/")&&(c||f)&&(s.pathname+="/"),s}const b=e=>e.join("/").replace(/\/\/+/g,"/"),Oe=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Be=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,Ie=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function Ne(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const te=["post","put","patch","delete"];new Set(te);const Te=["get",...te];new Set(Te);/**
 * React Router v6.30.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function I(){return I=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},I.apply(this,arguments)}const J=h.createContext(null),je=h.createContext(null),S=h.createContext(null),k=h.createContext(null),w=h.createContext({outlet:null,matches:[],isDataRoute:!1}),ne=h.createContext(null);function ke(e,t){let{relative:n}=t===void 0?{}:t;O()||g(!1);let{basename:r,navigator:a}=h.useContext(S),{hash:l,pathname:i,search:o}=le(e,{relative:n}),s=i;return r!=="/"&&(s=i==="/"?r:b([r,i])),a.createHref({pathname:s,search:o,hash:l})}function O(){return h.useContext(k)!=null}function N(){return O()||g(!1),h.useContext(k).location}function re(e){h.useContext(S).static||h.useLayoutEffect(e)}function ae(){let{isDataRoute:e}=h.useContext(w);return e?Ge():_e()}function _e(){O()||g(!1);let e=h.useContext(J),{basename:t,future:n,navigator:r}=h.useContext(S),{matches:a}=h.useContext(w),{pathname:l}=N(),i=JSON.stringify(V(a,n.v7_relativeSplatPath)),o=h.useRef(!1);return re(()=>{o.current=!0}),h.useCallback(function(c,f){if(f===void 0&&(f={}),!o.current)return;if(typeof c=="number"){r.go(c);return}let u=D(c,JSON.parse(i),l,f.relative==="path");e==null&&t!=="/"&&(u.pathname=u.pathname==="/"?t:b([t,u.pathname])),(f.replace?r.replace:r.push)(u,f.state,f)},[t,r,i,l,e])}function st(){let{matches:e}=h.useContext(w),t=e[e.length-1];return t?t.params:{}}function le(e,t){let{relative:n}=t===void 0?{}:t,{future:r}=h.useContext(S),{matches:a}=h.useContext(w),{pathname:l}=N(),i=JSON.stringify(V(a,r.v7_relativeSplatPath));return h.useMemo(()=>D(e,JSON.parse(i),l,n==="path"),[e,i,l,n])}function $e(e,t){return We(e,t)}function We(e,t,n,r){O()||g(!1);let{navigator:a,static:l}=h.useContext(S),{matches:i}=h.useContext(w),o=i[i.length-1],s=o?o.params:{};o&&o.pathname;let c=o?o.pathnameBase:"/";o&&o.route;let f=N(),u;if(t){var m;let p=typeof t=="string"?L(t):t;c==="/"||(m=p.pathname)!=null&&m.startsWith(c)||g(!1),u=p}else u=f;let x=u.pathname||"/",y=x;if(c!=="/"){let p=c.replace(/^\//,"").split("/");y="/"+x.replace(/^\//,"").split("/").slice(p.length).join("/")}let v=!l&&n&&n.matches&&n.matches.length>0?n.matches:fe(e,{pathname:y}),d=Je(v&&v.map(p=>Object.assign({},p,{params:Object.assign({},s,p.params),pathname:b([c,a.encodeLocation?a.encodeLocation(p.pathname).pathname:p.pathname]),pathnameBase:p.pathnameBase==="/"?c:b([c,a.encodeLocation?a.encodeLocation(p.pathnameBase).pathname:p.pathnameBase])})),i,n,r);return t&&d?h.createElement(k.Provider,{value:{location:I({pathname:"/",search:"",hash:"",state:null,key:"default"},u),navigationType:R.Pop}},d):d}function Me(){let e=qe(),t=Ne(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,a={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return h.createElement(h.Fragment,null,h.createElement("h2",null,"Unexpected Application Error!"),h.createElement("h3",{style:{fontStyle:"italic"}},t),n?h.createElement("pre",{style:a},n):null,null)}const Fe=h.createElement(Me,null);class Ve extends h.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?h.createElement(w.Provider,{value:this.props.routeContext},h.createElement(ne.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function De(e){let{routeContext:t,match:n,children:r}=e,a=h.useContext(J);return a&&a.static&&a.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(a.staticContext._deepestRenderedBoundaryId=n.route.id),h.createElement(w.Provider,{value:t},r)}function Je(e,t,n,r){var a;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var l;if(!n)return null;if(n.errors)e=n.matches;else if((l=r)!=null&&l.v7_partialHydration&&t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let i=e,o=(a=n)==null?void 0:a.errors;if(o!=null){let f=i.findIndex(u=>u.route.id&&(o==null?void 0:o[u.route.id])!==void 0);f>=0||g(!1),i=i.slice(0,Math.min(i.length,f+1))}let s=!1,c=-1;if(n&&r&&r.v7_partialHydration)for(let f=0;f<i.length;f++){let u=i[f];if((u.route.HydrateFallback||u.route.hydrateFallbackElement)&&(c=f),u.route.id){let{loaderData:m,errors:x}=n,y=u.route.loader&&m[u.route.id]===void 0&&(!x||x[u.route.id]===void 0);if(u.route.lazy||y){s=!0,c>=0?i=i.slice(0,c+1):i=[i[0]];break}}}return i.reduceRight((f,u,m)=>{let x,y=!1,v=null,d=null;n&&(x=o&&u.route.id?o[u.route.id]:void 0,v=u.route.errorElement||Fe,s&&(c<0&&m===0?(y=!0,d=null):c===m&&(y=!0,d=u.route.hydrateFallbackElement||null)));let p=t.concat(i.slice(0,m+1)),E=()=>{let C;return x?C=v:y?C=d:u.route.Component?C=h.createElement(u.route.Component,null):u.route.element?C=u.route.element:C=f,h.createElement(De,{match:u,routeContext:{outlet:f,matches:p,isDataRoute:n!=null},children:C})};return n&&(u.route.ErrorBoundary||u.route.errorElement||m===0)?h.createElement(Ve,{location:n.location,revalidation:n.revalidation,component:v,error:x,children:E(),routeContext:{outlet:null,matches:p,isDataRoute:!0}}):E()},null)}var ie=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(ie||{}),j=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(j||{});function ze(e){let t=h.useContext(J);return t||g(!1),t}function Ae(e){let t=h.useContext(je);return t||g(!1),t}function Ke(e){let t=h.useContext(w);return t||g(!1),t}function oe(e){let t=Ke(),n=t.matches[t.matches.length-1];return n.route.id||g(!1),n.route.id}function qe(){var e;let t=h.useContext(ne),n=Ae(j.UseRouteError),r=oe(j.UseRouteError);return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}function Ge(){let{router:e}=ze(ie.UseNavigateStable),t=oe(j.UseNavigateStable),n=h.useRef(!1);return re(()=>{n.current=!0}),h.useCallback(function(a,l){l===void 0&&(l={}),n.current&&(typeof a=="number"?e.navigate(a):e.navigate(a,I({fromRouteId:t},l)))},[e,t])}function Xe(e,t){e==null||e.v7_startTransition,e==null||e.v7_relativeSplatPath}function ut(e){let{to:t,replace:n,state:r,relative:a}=e;O()||g(!1);let{future:l,static:i}=h.useContext(S),{matches:o}=h.useContext(w),{pathname:s}=N(),c=ae(),f=D(t,V(o,l.v7_relativeSplatPath),s,a==="path"),u=JSON.stringify(f);return h.useEffect(()=>c(JSON.parse(u),{replace:n,state:r,relative:a}),[c,u,a,n,r]),null}function He(e){g(!1)}function Qe(e){let{basename:t="/",children:n=null,location:r,navigationType:a=R.Pop,navigator:l,static:i=!1,future:o}=e;O()&&g(!1);let s=t.replace(/^\/*/,"/"),c=h.useMemo(()=>({basename:s,navigator:l,static:i,future:I({v7_relativeSplatPath:!1},o)}),[s,o,l,i]);typeof r=="string"&&(r=L(r));let{pathname:f="/",search:u="",hash:m="",state:x=null,key:y="default"}=r,v=h.useMemo(()=>{let d=F(f,s);return d==null?null:{location:{pathname:d,search:u,hash:m,state:x,key:y},navigationType:a}},[s,f,u,m,x,y,a]);return v==null?null:h.createElement(S.Provider,{value:c},h.createElement(k.Provider,{children:n,value:v}))}function ct(e){let{children:t,location:n}=e;return $e(W(t),n)}new Promise(()=>{});function W(e,t){t===void 0&&(t=[]);let n=[];return h.Children.forEach(e,(r,a)=>{if(!h.isValidElement(r))return;let l=[...t,a];if(r.type===h.Fragment){n.push.apply(n,W(r.props.children,l));return}r.type!==He&&g(!1),!r.props.index||!r.props.children||g(!1);let i={id:r.props.id||l.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(i.children=W(r.props.children,l)),n.push(i)}),n}/**
 * React Router DOM v6.30.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function M(){return M=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},M.apply(this,arguments)}function Ye(e,t){if(e==null)return{};var n={},r=Object.keys(e),a,l;for(l=0;l<r.length;l++)a=r[l],!(t.indexOf(a)>=0)&&(n[a]=e[a]);return n}function Ze(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function et(e,t){return e.button===0&&(!t||t==="_self")&&!Ze(e)}const tt=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],nt="6";try{window.__reactRouterVersion=nt}catch{}const rt="startTransition",X=se[rt];function ht(e){let{basename:t,children:n,future:r,window:a}=e,l=h.useRef();l.current==null&&(l.current=ue({window:a,v5Compat:!0}));let i=l.current,[o,s]=h.useState({action:i.action,location:i.location}),{v7_startTransition:c}=r||{},f=h.useCallback(u=>{c&&X?X(()=>s(u)):s(u)},[s,c]);return h.useLayoutEffect(()=>i.listen(f),[i,f]),h.useEffect(()=>Xe(r),[r]),h.createElement(Qe,{basename:t,children:n,location:o.location,navigationType:o.action,navigator:i,future:r})}const at=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",lt=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,ft=h.forwardRef(function(t,n){let{onClick:r,relative:a,reloadDocument:l,replace:i,state:o,target:s,to:c,preventScrollReset:f,viewTransition:u}=t,m=Ye(t,tt),{basename:x}=h.useContext(S),y,v=!1;if(typeof c=="string"&&lt.test(c)&&(y=c,at))try{let C=new URL(window.location.href),P=c.startsWith("//")?new URL(C.protocol+c):new URL(c),U=F(P.pathname,x);P.origin===C.origin&&U!=null?c=U+P.search+P.hash:v=!0}catch{}let d=ke(c,{relative:a}),p=it(c,{replace:i,state:o,target:s,preventScrollReset:f,relative:a,viewTransition:u});function E(C){r&&r(C),C.defaultPrevented||p(C)}return h.createElement("a",M({},m,{href:y||d,onClick:v||l?r:E,ref:n,target:s}))});var H;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(H||(H={}));var Q;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(Q||(Q={}));function it(e,t){let{target:n,replace:r,state:a,preventScrollReset:l,relative:i,viewTransition:o}=t===void 0?{}:t,s=ae(),c=N(),f=le(e,{relative:i});return h.useCallback(u=>{if(et(u,n)){u.preventDefault();let m=r!==void 0?r:T(c)===T(f);s(e,{replace:m,state:a,preventScrollReset:l,relative:i,viewTransition:o})}},[c,s,f,r,a,n,e,l,i,o])}export{ht as B,ft as L,ut as N,ct as R,st as a,He as b,ae as u};
