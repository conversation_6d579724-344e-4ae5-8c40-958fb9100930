import{r as g,v as N,j as e,$ as v,Y as i}from"./app-Cj-kSxmT.js";import{c as o,C as p}from"./car-CXkXzXL9.js";import{H as w}from"./heart-DQdqQOFe.js";import{C as _}from"./calendar-C03a_VmS.js";import{M as k}from"./map-pin-DpBG7oTH.js";import{C}from"./check-BmzDpNyB.js";import{M as S}from"./message-circle-CPYXbNOk.js";import{X as $}from"./x-D2_ybCL2.js";/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const D=[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]],z=o("chevron-left",D);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const I=[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]],F=o("chevron-right",I);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const T=[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]],B=o("share-2",T);function P({auth:l,car:s}){const[d,n]=g.useState(0),[M,R]=g.useState(!1),{data:a,setData:c,post:j,processing:x,errors:m}=N({start_time:"",end_time:"",notes:""}),b=()=>{s.images&&s.images.length>1&&n(t=>(t+1)%s.images.length)},y=()=>{s.images&&s.images.length>1&&n(t=>(t-1+s.images.length)%s.images.length)},u=t=>{t.preventDefault(),j(`/cars/${s.id}/book`)},f=()=>{if(!a.start_time||!a.end_time)return 0;const t=new Date(a.start_time),r=new Date(a.end_time),h=Math.ceil((r.getTime()-t.getTime())/(1e3*60*60));return h>0?h*s.price_per_hour:0};return e.jsxs(e.Fragment,{children:[e.jsx(v,{title:`${s.make} ${s.model} - ${s.year}`}),e.jsxs("div",{className:"min-h-screen bg-gray-50",children:[e.jsx("header",{className:"bg-white shadow-md sticky top-0 z-50",children:e.jsx("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"flex justify-between items-center py-4",children:[e.jsxs(i,{href:"/",className:"flex items-center space-x-2",children:[e.jsx(p,{className:"h-8 w-8 text-primary-600"}),e.jsx("span",{className:"text-xl font-bold text-gray-900",children:"Park & Rent"})]}),e.jsxs("nav",{className:"hidden md:flex space-x-8",children:[e.jsx(i,{href:"/",className:"text-gray-700 hover:text-primary-600",children:"Home"}),e.jsx(i,{href:"/cars",className:"text-primary-600 font-medium",children:"Browse Cars"}),e.jsx(i,{href:"/drivers",className:"text-gray-700 hover:text-primary-600",children:"Hire a Driver"})]}),e.jsx("div",{className:"flex items-center space-x-4",children:l.user?e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("span",{className:"text-gray-700",children:["Welcome, ",l.user.name]}),e.jsx(i,{href:"/dashboard",className:"bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700",children:"Dashboard"})]}):e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx(i,{href:"/login",className:"text-gray-700 hover:text-primary-600",children:"Log In"}),e.jsx(i,{href:"/register",className:"bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700",children:"Sign Up"})]})})]})})}),e.jsx("div",{className:"bg-white border-b",children:e.jsx("div",{className:"container mx-auto px-4 py-3",children:e.jsxs("nav",{className:"flex items-center space-x-2 text-sm",children:[e.jsx(i,{href:"/",className:"text-gray-600 hover:text-primary-600",children:"Home"}),e.jsx("span",{className:"text-gray-400",children:"/"}),e.jsx(i,{href:"/cars",className:"text-gray-600 hover:text-primary-600",children:"Cars"}),e.jsx("span",{className:"text-gray-400",children:"/"}),e.jsxs("span",{className:"text-gray-900",children:[s.make," ",s.model]})]})})}),e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[e.jsxs("div",{className:"lg:col-span-2",children:[e.jsxs("div",{className:"bg-white rounded-lg shadow-md overflow-hidden mb-6",children:[e.jsxs("div",{className:"relative h-96",children:[s.images&&s.images.length>0?e.jsxs(e.Fragment,{children:[e.jsx("img",{src:s.images[d],alt:`${s.make} ${s.model}`,className:"w-full h-full object-cover"}),s.images.length>1&&e.jsxs(e.Fragment,{children:[e.jsx("button",{onClick:y,className:"absolute left-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70",children:e.jsx(z,{size:20})}),e.jsx("button",{onClick:b,className:"absolute right-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70",children:e.jsx(F,{size:20})})]}),s.images.length>1&&e.jsx("div",{className:"absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2",children:s.images.map((t,r)=>e.jsx("button",{onClick:()=>n(r),className:`w-3 h-3 rounded-full ${r===d?"bg-white":"bg-white bg-opacity-50"}`},r))})]}):e.jsx("div",{className:"w-full h-full flex items-center justify-center bg-gray-200",children:e.jsx(p,{size:64,className:"text-gray-400"})}),e.jsxs("div",{className:"absolute top-4 right-4 flex space-x-2",children:[e.jsx("button",{className:"bg-white p-2 rounded-full shadow-md hover:bg-gray-50",children:e.jsx(w,{size:20,className:"text-gray-600"})}),e.jsx("button",{className:"bg-white p-2 rounded-full shadow-md hover:bg-gray-50",children:e.jsx(B,{size:20,className:"text-gray-600"})})]}),e.jsx("div",{className:"absolute top-4 left-4",children:e.jsx("span",{className:`px-3 py-1 rounded-full text-sm font-medium ${s.is_active?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:s.is_active?"Available":"Unavailable"})})]}),s.images&&s.images.length>1&&e.jsx("div",{className:"p-4 border-t",children:e.jsx("div",{className:"flex space-x-2 overflow-x-auto",children:s.images.map((t,r)=>e.jsx("button",{onClick:()=>n(r),className:`flex-shrink-0 w-20 h-20 rounded-md overflow-hidden border-2 ${r===d?"border-primary-600":"border-gray-200"}`,children:e.jsx("img",{src:t,alt:`${s.make} ${s.model} ${r+1}`,className:"w-full h-full object-cover"})},r))})})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6 mb-6",children:[e.jsxs("div",{className:"flex justify-between items-start mb-4",children:[e.jsxs("div",{children:[e.jsxs("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:[s.make," ",s.model]}),e.jsxs("div",{className:"flex items-center text-gray-600 mb-2",children:[e.jsx(_,{size:16,className:"mr-2"}),e.jsx("span",{children:s.year})]}),e.jsxs("div",{className:"flex items-center text-gray-600",children:[e.jsx(k,{size:16,className:"mr-2"}),e.jsx("span",{children:s.location})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:"text-3xl font-bold text-primary-600",children:["RWF ",s.price_per_hour.toLocaleString()]}),e.jsx("div",{className:"text-gray-600",children:"per hour"})]})]}),e.jsxs("div",{className:"border-t pt-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Description"}),e.jsx("p",{className:"text-gray-700 leading-relaxed",children:s.description})]}),s.availability_notes&&e.jsxs("div",{className:"border-t pt-4 mt-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Availability Notes"}),e.jsx("p",{className:"text-gray-700",children:s.availability_notes})]})]}),s.features&&s.features.length>0&&e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6 mb-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Features & Amenities"}),e.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-3",children:s.features.map((t,r)=>e.jsxs("div",{className:"flex items-center",children:[e.jsx(C,{size:16,className:"text-green-600 mr-2"}),e.jsx("span",{className:"text-gray-700",children:t})]},r))})]}),s.owner&&e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Car Owner"}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mr-4",children:e.jsx("span",{className:"text-primary-600 font-semibold",children:s.owner.name.charAt(0).toUpperCase()})}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-gray-900",children:s.owner.name}),e.jsx("p",{className:"text-sm text-gray-600",children:"Car Owner"})]})]}),l.user&&l.user.id!==s.owner.id&&e.jsxs("button",{className:"flex items-center px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50",children:[e.jsx(S,{size:16,className:"mr-2"}),"Message"]})]})]})]}),e.jsx("div",{className:"lg:col-span-1",children:e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6 sticky top-24",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Book This Car"}),l.user?s.is_active?e.jsxs("form",{onSubmit:u,className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Start Date & Time"}),e.jsx("input",{type:"datetime-local",value:a.start_time,onChange:t=>c("start_time",t.target.value),min:new Date().toISOString().slice(0,16),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500",required:!0}),m.start_time&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:m.start_time})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"End Date & Time"}),e.jsx("input",{type:"datetime-local",value:a.end_time,onChange:t=>c("end_time",t.target.value),min:a.start_time||new Date().toISOString().slice(0,16),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500",required:!0}),m.end_time&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:m.end_time})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Notes (Optional)"}),e.jsx("textarea",{value:a.notes,onChange:t=>c("notes",t.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500",placeholder:"Any special requests or notes..."})]}),a.start_time&&a.end_time&&e.jsxs("div",{className:"bg-gray-50 p-4 rounded-md",children:[e.jsxs("div",{className:"flex justify-between items-center mb-2",children:[e.jsx("span",{className:"text-gray-700",children:"Duration:"}),e.jsxs("span",{className:"font-medium",children:[Math.ceil((new Date(a.end_time).getTime()-new Date(a.start_time).getTime())/(1e3*60*60))," hours"]})]}),e.jsxs("div",{className:"flex justify-between items-center mb-2",children:[e.jsx("span",{className:"text-gray-700",children:"Rate:"}),e.jsxs("span",{className:"font-medium",children:["RWF ",s.price_per_hour.toLocaleString(),"/hr"]})]}),e.jsxs("div",{className:"border-t pt-2 flex justify-between items-center",children:[e.jsx("span",{className:"font-semibold text-gray-900",children:"Total:"}),e.jsxs("span",{className:"font-bold text-primary-600 text-lg",children:["RWF ",f().toLocaleString()]})]})]}),e.jsx("button",{type:"submit",disabled:x||!a.start_time||!a.end_time,className:"w-full bg-primary-600 text-white py-3 rounded-md hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed font-medium",children:x?"Booking...":"Book Now"})]}):e.jsxs("div",{className:"text-center py-8",children:[e.jsx($,{size:48,className:"mx-auto text-red-400 mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"Car Unavailable"}),e.jsx("p",{className:"text-gray-600",children:"This car is currently not available for booking."})]}):e.jsxs("div",{className:"text-center py-8",children:[e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"Sign in to Book"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"You need to be logged in to book this car."}),e.jsx(i,{href:"/login",className:"w-full bg-primary-600 text-white py-3 rounded-md hover:bg-primary-700 font-medium inline-block text-center",children:"Sign In"})]})]})})]})})]})]})}export{P as default};
