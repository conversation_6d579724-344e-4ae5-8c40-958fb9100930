import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { MessageCircle, Users, Send, ArrowLeft, Clock } from 'lucide-react';
import MainLayout from '../../components/layout/MainLayout';
import Button from '../../components/ui/Button';
import Card from '../../components/ui/Card';
import { useAuth } from '../../context/AuthContext';
import { apiClient, API_ENDPOINTS } from '../../config/api';

interface Chat {
  id: string;
  user: { id: string; name: string; email: string };
  recipient: { id: string; name: string; email: string };
  messages: Array<{
    id: string;
    content: string;
    sender_id: string;
    sender: { id: string; name: string };
    created_at: string;
    is_read: boolean;
  }>;
  last_message_at: string;
}

interface Message {
  id: string;
  content: string;
  sender_id: string;
  sender: { id: string; name: string };
  created_at: string;
  is_read: boolean;
}

const OwnerMessagesPage: React.FC = () => {
  const { user, isAuthenticated } = useAuth();
  const [chats, setChats] = useState<Chat[]>([]);
  const [selectedChat, setSelectedChat] = useState<Chat | null>(null);
  const [newMessage, setNewMessage] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isSending, setIsSending] = useState(false);

  // Fetch all chats
  const fetchChats = async () => {
    try {
      setIsLoading(true);
      const response = await apiClient.get(API_ENDPOINTS.OWNER.MESSAGES);
      if (response.data.data) {
        setChats(response.data.data.data || []);
      }
    } catch (error) {
      console.error('Failed to fetch chats:', error);
      // Don't show error if user is not authenticated
      if (isAuthenticated) {
        // Could add toast notification here
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch specific chat conversation
  const fetchChat = async (chatId: string) => {
    try {
      const response = await apiClient.get(`${API_ENDPOINTS.OWNER.CHATS}/${chatId}`);
      if (response.data.data) {
        setSelectedChat(response.data.data);
        // Update the chat in the list with latest messages
        setChats(prevChats =>
          prevChats.map(chat =>
            chat.id === chatId ? response.data.data : chat
          )
        );
      }
    } catch (error) {
      console.error('Failed to fetch chat:', error);
    }
  };

  // Send message
  const sendMessage = async () => {
    if (!selectedChat || !newMessage.trim()) return;

    try {
      setIsSending(true);
      const response = await apiClient.post(API_ENDPOINTS.OWNER.MESSAGES, {
        chat_id: selectedChat.id,
        content: newMessage.trim()
      });

      if (response.data.data) {
        // Add the new message to the selected chat
        setSelectedChat(prev => prev ? {
          ...prev,
          messages: [...prev.messages, response.data.data]
        } : null);

        // Update the chat list
        setChats(prevChats =>
          prevChats.map(chat =>
            chat.id === selectedChat.id
              ? {
                  ...chat,
                  messages: [response.data.data],
                  last_message_at: response.data.data.created_at
                }
              : chat
          )
        );

        setNewMessage('');
      }
    } catch (error) {
      console.error('Failed to send message:', error);
    } finally {
      setIsSending(false);
    }
  };

  useEffect(() => {
    if (isAuthenticated) {
      fetchChats();
    }
  }, [isAuthenticated]);

  const formatMessageTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 168) { // 7 days
      return date.toLocaleDateString([], { weekday: 'short', hour: '2-digit', minute: '2-digit' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  // Show login prompt if not authenticated
  if (!isAuthenticated) {
    return (
      <MainLayout>
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="bg-white rounded-lg shadow-md p-8 text-center">
            <MessageCircle size={64} className="mx-auto text-red-500 mb-4" />
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Authentication Required</h1>
            <p className="text-gray-600 mb-6">
              You need to be logged in as an owner to access your messages.
            </p>
            <div className="space-y-4">
              <p className="text-sm text-gray-500">
                Test credentials: <br />
                <strong>Email:</strong> <EMAIL> <br />
                <strong>Password:</strong> password
              </p>
              <div className="flex gap-3 justify-center">
                <Link to="/login">
                  <Button className="flex items-center">
                    Go to Login
                  </Button>
                </Link>
                <Link to="/owner/dashboard">
                  <Button variant="secondary" className="flex items-center">
                    <ArrowLeft size={16} className="mr-2" />
                    Back to Dashboard
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center mb-6">
          <Link to="/owner/dashboard" className="mr-4">
            <Button variant="secondary" size="sm">
              <ArrowLeft size={16} className="mr-2" />
              Back to Dashboard
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Messages</h1>
            <p className="text-gray-600">
              Communicate with customers interested in your cars.
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6" style={{ height: 'calc(100vh - 200px)' }}>
          {/* Chat List */}
          <div className="lg:col-span-1">
            <Card className="h-full">
              <div className="p-4 border-b border-gray-200">
                <h2 className="text-lg font-semibold text-gray-900">Conversations</h2>
              </div>

              <div className="overflow-y-auto h-full">
                {isLoading ? (
                  <div className="p-4 text-center text-gray-500">
                    Loading conversations...
                  </div>
                ) : chats.length === 0 ? (
                  <div className="p-4 text-center">
                    <MessageCircle size={48} className="mx-auto text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No conversations yet</h3>
                    <p className="text-gray-600 text-sm">
                      Messages from customers will appear here when they contact you about your cars.
                    </p>
                  </div>
                ) : (
                  <div className="divide-y divide-gray-200">
                    {chats.map((chat) => {
                      const otherUser = chat.user.id === user?.id ? chat.recipient : chat.user;
                      const lastMessage = chat.messages[0];
                      const isUnread = lastMessage && lastMessage.sender_id !== user?.id && !lastMessage.is_read;

                      return (
                        <div
                          key={chat.id}
                          onClick={() => {
                            setSelectedChat(chat);
                            fetchChat(chat.id);
                          }}
                          className={`p-4 cursor-pointer hover:bg-gray-50 ${
                            selectedChat?.id === chat.id ? 'bg-blue-50 border-r-2 border-blue-500' : ''
                          }`}
                        >
                          <div className="flex items-start space-x-3">
                            <div className="flex-shrink-0">
                              <div className="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                                <Users className="h-5 w-5 text-gray-600" />
                              </div>
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="flex justify-between items-start">
                                <h4 className={`text-sm font-medium truncate ${
                                  isUnread ? 'text-gray-900 font-semibold' : 'text-gray-700'
                                }`}>
                                  {otherUser.name}
                                </h4>
                                <div className="flex items-center space-x-1">
                                  {lastMessage && (
                                    <span className="text-xs text-gray-400">
                                      {formatMessageTime(lastMessage.created_at)}
                                    </span>
                                  )}
                                  {isUnread && (
                                    <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                                  )}
                                </div>
                              </div>
                              <p className={`text-sm truncate ${
                                isUnread ? 'text-gray-900' : 'text-gray-500'
                              }`}>
                                {lastMessage ? lastMessage.content : 'No messages yet'}
                              </p>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                )}
              </div>
            </Card>
          </div>

          {/* Chat Messages */}
          <div className="lg:col-span-2">
            <Card className="h-full flex flex-col">
              {selectedChat ? (
                <>
                  {/* Chat Header */}
                  <div className="p-4 border-b border-gray-200">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                        <Users className="h-5 w-5 text-gray-600" />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">
                          {selectedChat.user.id === user?.id ? selectedChat.recipient.name : selectedChat.user.name}
                        </h3>
                        <p className="text-sm text-gray-500">
                          {selectedChat.user.id === user?.id ? selectedChat.recipient.email : selectedChat.user.email}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Messages */}
                  <div className="flex-1 overflow-y-auto p-4 space-y-4 min-h-0">
                    {selectedChat.messages.map((message) => {
                      const isOwn = message.sender_id === user?.id;
                      const otherUser = selectedChat.user.id === user?.id ? selectedChat.recipient : selectedChat.user;
                      const senderName = isOwn ? 'You' : otherUser.name;

                      return (
                        <div key={message.id} className={`flex ${
                          isOwn ? 'justify-end' : 'justify-start'
                        }`}>
                          <div className={`max-w-xs lg:max-w-md ${
                            isOwn ? 'order-2' : 'order-1'
                          }`}>
                            {/* Sender Label */}
                            <div className={`text-xs mb-1 ${
                              isOwn ? 'text-right text-green-600' : 'text-left text-gray-600'
                            }`}>
                              {senderName} {isOwn ? '(Owner)' : '(Client)'}
                            </div>

                            {/* Message Bubble */}
                            <div
                              className={`px-4 py-3 rounded-lg shadow-sm ${
                                isOwn
                                  ? 'bg-green-600 text-white rounded-br-sm'
                                  : 'bg-blue-100 text-gray-900 border border-blue-200 rounded-bl-sm'
                              }`}
                            >
                              <p className="text-sm leading-relaxed">{message.content}</p>
                              <p className={`text-xs mt-2 ${
                                isOwn ? 'text-green-100' : 'text-blue-600'
                              }`}>
                                {new Date(message.created_at).toLocaleDateString()} at {formatMessageTime(message.created_at)}
                              </p>
                            </div>
                          </div>

                          {/* Avatar */}
                          <div className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-semibold ${
                            isOwn
                              ? 'bg-green-100 text-green-600 ml-2 order-3'
                              : 'bg-blue-100 text-blue-600 mr-2 order-0'
                          }`}>
                            {isOwn ? 'O' : 'C'}
                          </div>
                        </div>
                      );
                    })}
                  </div>

                  {/* Message Input */}
                  <div className="p-4 border-t border-gray-200">
                    <div className="flex space-x-2">
                      <input
                        type="text"
                        value={newMessage}
                        onChange={(e) => setNewMessage(e.target.value)}
                        onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
                        placeholder="Type your message..."
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        disabled={isSending}
                      />
                      <Button
                        onClick={sendMessage}
                        disabled={!newMessage.trim() || isSending}
                        className="px-4"
                      >
                        <Send size={16} />
                      </Button>
                    </div>
                  </div>
                </>
              ) : (
                <div className="flex-1 flex items-center justify-center">
                  <div className="text-center">
                    <MessageCircle size={64} className="mx-auto text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Select a conversation</h3>
                    <p className="text-gray-600">
                      Choose a conversation from the left to start messaging.
                    </p>
                  </div>
                </div>
              )}
            </Card>
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default OwnerMessagesPage;
