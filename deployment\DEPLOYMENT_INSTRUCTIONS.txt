PARK & RENT - HOSTINGER DEPLOYMENT INSTRUCTIONS
==============================================

Your deployment files are ready! Follow these steps to deploy to Hostinger:

STEP 1: UPLOAD FILES TO HOSTINGER
---------------------------------
1. Login to your Hostinger control panel
2. Go to File Manager
3. Navigate to public_html directory
4. Upload ALL files from this deployment folder to public_html
   - React frontend files (index.html, assets/, etc.)
   - Laravel API files (in api/ folder)
   - Root .htaccess file for routing

STEP 2: SET UP DATABASE
-----------------------
1. In Hostinger control panel, go to MySQL Databases
2. Your database is already configured:
   - Database: u555127771_parkandrent_db
   - Username: u555127771_parkandrent_us
   - Password: Ebisera@2020

STEP 3: CONFIGURE LARAVEL API
-----------------------------
1. Navigate to public_html/api/ in File Manager
2. Rename .env.production to .env
3. Install dependencies (if SSH access available):
   cd public_html/api
   composer install --optimize-autoloader --no-dev
   
4. Run Laravel setup commands:
   php artisan key:generate --force
   php artisan migrate --force
   php artisan db:seed --force
   php artisan storage:link
   php artisan config:cache
   php artisan route:cache

STEP 4: SET FILE PERMISSIONS
----------------------------
Set these folder permissions to 755:
- public_html/api/storage/
- public_html/api/bootstrap/cache/

STEP 5: TEST YOUR APPLICATION
-----------------------------
1. Frontend: https://ebisera.com
2. API Test: https://ebisera.com/api/cars
3. Admin Login: Use seeded admin credentials

TROUBLESHOOTING
---------------
- If 500 error: Check file permissions and .env configuration
- If CORS issues: Verify domain in .env matches ebisera.com
- If database errors: Confirm database credentials are correct

SUPPORT CONTACT
---------------
Phone: 0788613669
Email: <EMAIL>

Your application is configured for:
- Domain: ebisera.com
- Frontend: React SPA with PWA support
- Backend: Laravel 12 API with MySQL
- Features: Car rental, driver booking, admin dashboard
