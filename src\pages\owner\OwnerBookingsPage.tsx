import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Calendar, Clock, DollarSign, User, Car, ArrowLeft, Filter, CheckCircle, XCircle, AlertCircle, Eye } from 'lucide-react';
import MainLayout from '../../components/layout/MainLayout';
import Button from '../../components/ui/Button';
import Card from '../../components/ui/Card';
import { useAuth } from '../../context/AuthContext';
import { apiClient, API_ENDPOINTS } from '../../config/api';

interface Booking {
  id: string;
  user: {
    id: string;
    name: string;
    email: string;
    phone?: string;
  };
  car: {
    id: string;
    make: string;
    model: string;
    year: number;
    license_plate: string;
  };
  start_time: string;
  end_time: string;
  total_price: number;
  status: 'pending' | 'confirmed' | 'cancelled' | 'completed';
  notes?: string;
  created_at: string;
}

interface BookingsResponse {
  data: Booking[];
  current_page: number;
  last_page: number;
  total: number;
}

const OwnerBookingsPage: React.FC = () => {
  const { user, isAuthenticated } = useAuth();
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [showFilters, setShowFilters] = useState(false);

  // Fetch bookings
  const fetchBookings = async (page = 1, status = '') => {
    console.log('fetchBookings called - isAuthenticated:', isAuthenticated, 'user:', user);
    if (!isAuthenticated) {
      console.log('User not authenticated, skipping booking fetch');
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      const params = new URLSearchParams();
      if (status) params.append('status', status);
      params.append('page', page.toString());

      const url = `${API_ENDPOINTS.OWNER.BOOKINGS}?${params}`;
      console.log('Making API call to:', url);
      const response = await apiClient.get(url);
      console.log('Owner bookings API response:', response.data);

      if (response.data.data) {
        const bookingsData = response.data.data;
        console.log('Bookings data structure:', bookingsData);
        console.log('Actual bookings array:', bookingsData.data);
        setBookings(bookingsData.data || []);
        setCurrentPage(bookingsData.current_page || 1);
        setTotalPages(bookingsData.last_page || 1);
      } else {
        console.log('No data.data in response:', response.data);
      }
    } catch (error: any) {
      console.error('Failed to fetch bookings:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Update booking status
  const updateBookingStatus = async (bookingId: string, status: 'confirmed' | 'cancelled' | 'completed') => {
    try {
      await apiClient.patch(`${API_ENDPOINTS.OWNER.BOOKINGS}/${bookingId}/status`, { status });

      // Update the booking in the local state
      setBookings(prev => prev.map(booking =>
        booking.id === bookingId ? { ...booking, status } : booking
      ));
    } catch (error: any) {
      console.error('Failed to update booking status:', error);
    }
  };

  useEffect(() => {
    fetchBookings(currentPage, statusFilter);
  }, [isAuthenticated, currentPage, statusFilter]);

  // Show login prompt if not authenticated
  if (!isAuthenticated) {
    return (
      <MainLayout>
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="bg-white rounded-lg shadow-md p-8 text-center">
            <Calendar size={64} className="mx-auto text-red-500 mb-4" />
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Authentication Required</h1>
            <p className="text-gray-600 mb-6">
              You need to be logged in as an owner to access your bookings.
            </p>
            <div className="space-y-4">
              <p className="text-sm text-gray-500">
                Test credentials: <br />
                <strong>Email:</strong> <EMAIL> <br />
                <strong>Password:</strong> password
              </p>
              <div className="flex gap-3 justify-center">
                <Link to="/login">
                  <Button className="flex items-center">
                    Go to Login
                  </Button>
                </Link>
                <Link to="/owner/dashboard">
                  <Button variant="secondary" className="flex items-center">
                    <ArrowLeft size={16} className="mr-2" />
                    Back to Dashboard
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </MainLayout>
    );
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'confirmed':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'cancelled':
        return <XCircle className="h-5 w-5 text-red-500" />;
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-blue-500" />;
      default:
        return <AlertCircle className="h-5 w-5 text-yellow-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      case 'completed':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-yellow-100 text-yellow-800';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const calculateDuration = (startTime: string, endTime: string) => {
    const start = new Date(startTime);
    const end = new Date(endTime);
    const hours = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60));
    return hours;
  };

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center">
            <Link to="/owner/dashboard" className="mr-4">
              <Button variant="secondary" size="sm">
                <ArrowLeft size={16} className="mr-2" />
                Back to Dashboard
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Bookings</h1>
              <p className="text-gray-600">
                Manage bookings for your cars and communicate with customers.
              </p>
            </div>
          </div>

          <Button
            variant="secondary"
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center"
          >
            <Filter size={16} className="mr-2" />
            Filters
          </Button>
        </div>

        {/* Filters */}
        {showFilters && (
          <Card className="p-6 mb-6">
            <div className="flex flex-wrap gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Status
                </label>
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">All Statuses</option>
                  <option value="pending">Pending</option>
                  <option value="confirmed">Confirmed</option>
                  <option value="completed">Completed</option>
                  <option value="cancelled">Cancelled</option>
                </select>
              </div>

              <div className="flex items-end">
                <Button
                  variant="secondary"
                  onClick={() => {
                    setStatusFilter('');
                    setCurrentPage(1);
                  }}
                >
                  Clear Filters
                </Button>
              </div>
            </div>
          </Card>
        )}

        {/* Bookings List */}
        {isLoading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading bookings...</p>
          </div>
        ) : bookings.length === 0 ? (
          <Card className="p-8 text-center">
            <Calendar size={64} className="mx-auto text-gray-400 mb-4" />
            <h3 className="text-xl font-medium text-gray-900 mb-2">No bookings found</h3>
            <p className="text-gray-600">
              {statusFilter
                ? `No ${statusFilter} bookings found. Try adjusting your filters.`
                : 'You haven\'t received any bookings yet. Make sure your cars are active and visible to customers.'
              }
            </p>
          </Card>
        ) : (
          <div className="space-y-4">
            {bookings.map((booking) => (
              <Card key={booking.id} className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center mb-4">
                      <div className="flex items-center mr-4">
                        {getStatusIcon(booking.status)}
                        <span className={`ml-2 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(booking.status)}`}>
                          {booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}
                        </span>
                      </div>
                      <span className="text-sm text-gray-500">
                        Booking #{booking.id.slice(0, 8)}
                      </span>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
                      {/* Customer Info */}
                      <div className="flex items-center">
                        <User className="h-5 w-5 text-gray-400 mr-3" />
                        <div>
                          <p className="font-medium text-gray-900">{booking.user.name}</p>
                          <p className="text-sm text-gray-500">{booking.user.email}</p>
                          {booking.user.phone && (
                            <p className="text-sm text-gray-500">{booking.user.phone}</p>
                          )}
                        </div>
                      </div>

                      {/* Car Info */}
                      <div className="flex items-center">
                        <Car className="h-5 w-5 text-gray-400 mr-3" />
                        <div>
                          <p className="font-medium text-gray-900">
                            {booking.car.year} {booking.car.make} {booking.car.model}
                          </p>
                          <p className="text-sm text-gray-500">{booking.car.license_plate}</p>
                        </div>
                      </div>

                      {/* Duration & Price */}
                      <div className="flex items-center">
                        <DollarSign className="h-5 w-5 text-gray-400 mr-3" />
                        <div>
                          <p className="font-medium text-gray-900">${booking.total_price}</p>
                          <p className="text-sm text-gray-500">
                            {calculateDuration(booking.start_time, booking.end_time)} hours
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Booking Times */}
                    <div className="flex items-center mb-4">
                      <Clock className="h-5 w-5 text-gray-400 mr-3" />
                      <div>
                        <p className="text-sm text-gray-600">
                          <span className="font-medium">From:</span> {formatDate(booking.start_time)}
                        </p>
                        <p className="text-sm text-gray-600">
                          <span className="font-medium">To:</span> {formatDate(booking.end_time)}
                        </p>
                      </div>
                    </div>

                    {/* Notes */}
                    {booking.notes && (
                      <div className="mb-4">
                        <p className="text-sm text-gray-600">
                          <span className="font-medium">Notes:</span> {booking.notes}
                        </p>
                      </div>
                    )}
                  </div>

                  {/* Actions */}
                  <div className="flex flex-col gap-2 ml-4">
                    {booking.status === 'pending' && (
                      <>
                        <Button
                          size="sm"
                          onClick={() => updateBookingStatus(booking.id, 'confirmed')}
                          className="flex items-center"
                        >
                          <CheckCircle size={14} className="mr-1" />
                          Confirm
                        </Button>
                        <Button
                          size="sm"
                          variant="secondary"
                          onClick={() => updateBookingStatus(booking.id, 'cancelled')}
                          className="flex items-center"
                        >
                          <XCircle size={14} className="mr-1" />
                          Cancel
                        </Button>
                      </>
                    )}

                    {booking.status === 'confirmed' && (
                      <Button
                        size="sm"
                        onClick={() => updateBookingStatus(booking.id, 'completed')}
                        className="flex items-center"
                      >
                        <CheckCircle size={14} className="mr-1" />
                        Complete
                      </Button>
                    )}
                  </div>
                </div>
              </Card>
            ))}
          </div>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex justify-center mt-8">
            <div className="flex space-x-2">
              <Button
                variant="secondary"
                disabled={currentPage === 1}
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
              >
                Previous
              </Button>

              <span className="flex items-center px-4 py-2 text-sm text-gray-700">
                Page {currentPage} of {totalPages}
              </span>

              <Button
                variant="secondary"
                disabled={currentPage === totalPages}
                onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
              >
                Next
              </Button>
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  );
};

export default OwnerBookingsPage;
