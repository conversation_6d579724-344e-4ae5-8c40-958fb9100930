import{r as m,j as e,$ as C,Y as r,m as x}from"./app-Cj-kSxmT.js";import{C as o}from"./car-CXkXzXL9.js";import{F as S}from"./funnel-C0C45j41.js";import{U as z}from"./user-round-Dc3ptzYJ.js";import{E as D}from"./eye-fGPZAapK.js";import{C as i}from"./calendar-C03a_VmS.js";import{C as F}from"./clock-B-cuJnHn.js";import{M as _}from"./message-circle-CPYXbNOk.js";import{C as B}from"./circle-alert-CML8Sh-8.js";import{C as M,a as h}from"./circle-x-BmQPBxO0.js";function I({auth:p,bookings:l,filters:c}){const[n,u]=m.useState(!1),[t,a]=m.useState({status:c.status||"",type:c.type||""}),j=()=>{x.get("/bookings",t,{preserveState:!0,preserveScroll:!0})},y=()=>{a({status:"",type:""}),x.get("/bookings")},g=s=>{switch(s){case"confirmed":return e.jsx(h,{size:16,className:"text-green-600"});case"completed":return e.jsx(h,{size:16,className:"text-blue-600"});case"cancelled":return e.jsx(M,{size:16,className:"text-red-600"});case"pending":default:return e.jsx(B,{size:16,className:"text-yellow-600"})}},N=s=>{switch(s){case"confirmed":return"bg-green-100 text-green-800";case"completed":return"bg-blue-100 text-blue-800";case"cancelled":return"bg-red-100 text-red-800";case"pending":default:return"bg-yellow-100 text-yellow-800"}},d=s=>new Date(s).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),f=(s,v)=>{const b=new Date(s),w=new Date(v);return Math.ceil((w.getTime()-b.getTime())/(1e3*60*60))};return e.jsxs(e.Fragment,{children:[e.jsx(C,{title:"My Bookings"}),e.jsxs("div",{className:"min-h-screen bg-gray-50",children:[e.jsx("header",{className:"bg-white shadow-md sticky top-0 z-50",children:e.jsx("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"flex justify-between items-center py-4",children:[e.jsxs(r,{href:"/",className:"flex items-center space-x-2",children:[e.jsx(o,{className:"h-8 w-8 text-primary-600"}),e.jsx("span",{className:"text-xl font-bold text-gray-900",children:"Park & Rent"})]}),e.jsxs("nav",{className:"hidden md:flex space-x-8",children:[e.jsx(r,{href:"/",className:"text-gray-700 hover:text-primary-600",children:"Home"}),e.jsx(r,{href:"/cars",className:"text-gray-700 hover:text-primary-600",children:"Browse Cars"}),e.jsx(r,{href:"/drivers",className:"text-gray-700 hover:text-primary-600",children:"Hire a Driver"}),e.jsx(r,{href:"/dashboard",className:"text-gray-700 hover:text-primary-600",children:"Dashboard"})]}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("span",{className:"text-gray-700",children:["Welcome, ",p.user.name]}),e.jsx(r,{href:"/dashboard",className:"bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700",children:"Dashboard"})]})]})})}),e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"flex justify-between items-center mb-8",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"My Bookings"}),e.jsx("p",{className:"text-gray-600 mt-2",children:"Manage your car rentals and driver bookings"})]}),e.jsxs("button",{onClick:()=>u(!n),className:"flex items-center px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50",children:[e.jsx(S,{size:16,className:"mr-2"}),"Filters"]})]}),n&&e.jsx("div",{className:"bg-white rounded-lg shadow-md p-6 mb-6",children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Status"}),e.jsxs("select",{value:t.status,onChange:s=>a({...t,status:s.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500",children:[e.jsx("option",{value:"",children:"All Statuses"}),e.jsx("option",{value:"pending",children:"Pending"}),e.jsx("option",{value:"confirmed",children:"Confirmed"}),e.jsx("option",{value:"completed",children:"Completed"}),e.jsx("option",{value:"cancelled",children:"Cancelled"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Type"}),e.jsxs("select",{value:t.type,onChange:s=>a({...t,type:s.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500",children:[e.jsx("option",{value:"",children:"All Types"}),e.jsx("option",{value:"car",children:"Car Rentals"}),e.jsx("option",{value:"driver",children:"Driver Services"})]})]}),e.jsxs("div",{className:"flex items-end space-x-2",children:[e.jsx("button",{onClick:j,className:"bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700",children:"Apply Filters"}),e.jsx("button",{onClick:y,className:"text-gray-600 hover:text-gray-800 px-4 py-2",children:"Clear"})]})]})}),l.length>0?e.jsx("div",{className:"space-y-6",children:l.map(s=>e.jsx("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"flex items-start justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mr-4",children:s.item_type==="car"?e.jsx(o,{size:24,className:"text-primary-600"}):e.jsx(z,{size:24,className:"text-primary-600"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:s.item_type==="car"?"Car Rental":"Driver Service"}),e.jsxs("p",{className:"text-sm text-gray-600",children:["Booking #",s.id]})]})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsxs("span",{className:`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${N(s.status)}`,children:[g(s.status),e.jsx("span",{className:"ml-1",children:s.status.charAt(0).toUpperCase()+s.status.slice(1)})]}),e.jsx(r,{href:`/bookings/${s.id}`,className:"text-primary-600 hover:text-primary-700",children:e.jsx(D,{size:20})})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center text-gray-600 mb-1",children:[e.jsx(i,{size:16,className:"mr-2"}),e.jsx("span",{className:"text-sm font-medium",children:"Start Time"})]}),e.jsx("p",{className:"text-gray-900",children:d(s.start_time)})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center text-gray-600 mb-1",children:[e.jsx(i,{size:16,className:"mr-2"}),e.jsx("span",{className:"text-sm font-medium",children:"End Time"})]}),e.jsx("p",{className:"text-gray-900",children:d(s.end_time)})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center text-gray-600 mb-1",children:[e.jsx(F,{size:16,className:"mr-2"}),e.jsx("span",{className:"text-sm font-medium",children:"Duration"})]}),e.jsxs("p",{className:"text-gray-900",children:[f(s.start_time,s.end_time)," hours"]})]})]}),s.notes&&e.jsxs("div",{className:"mb-4",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-700 mb-1",children:"Notes"}),e.jsx("p",{className:"text-gray-600 text-sm",children:s.notes})]}),e.jsxs("div",{className:"flex items-center justify-between pt-4 border-t",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("div",{children:e.jsxs("span",{className:"text-2xl font-bold text-primary-600",children:["RWF ",s.total_price.toLocaleString()]})}),s.is_paid&&e.jsx("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800",children:"Paid"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs("button",{className:"flex items-center px-3 py-1 text-sm text-gray-600 hover:text-gray-800",children:[e.jsx(_,{size:16,className:"mr-1"}),"Message"]}),e.jsx(r,{href:`/bookings/${s.id}`,className:"bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700 text-sm",children:"View Details"})]})]})]})},s.id))}):e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-12 text-center",children:[e.jsx(i,{size:64,className:"mx-auto text-gray-400 mb-4"}),e.jsx("h3",{className:"text-xl font-medium text-gray-900 mb-2",children:"No bookings found"}),e.jsx("p",{className:"text-gray-600 mb-6",children:Object.values(t).some(s=>s)?"No bookings match your current filters. Try adjusting your search criteria.":"You haven't made any bookings yet. Start by browsing available cars or drivers."}),e.jsxs("div",{className:"flex justify-center space-x-4",children:[e.jsx(r,{href:"/cars",className:"bg-primary-600 text-white px-6 py-2 rounded-md hover:bg-primary-700",children:"Browse Cars"}),e.jsx(r,{href:"/drivers",className:"border border-primary-600 text-primary-600 px-6 py-2 rounded-md hover:bg-primary-50",children:"Find Drivers"})]})]})]})]})]})}export{I as default};
