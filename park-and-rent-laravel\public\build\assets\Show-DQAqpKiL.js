import{r as j,v as f,j as e,$ as y,Y as i}from"./app-Cj-kSxmT.js";import{C as N}from"./car-CXkXzXL9.js";import{U as b}from"./user-round-Dc3ptzYJ.js";import{M as u}from"./map-pin-DpBG7oTH.js";import{A as v,S as _}from"./star-CCEgP9Rb.js";import{C as w}from"./clock-B-cuJnHn.js";import{C as x}from"./check-BmzDpNyB.js";import{M as S}from"./mail-BdhUp7Lm.js";import{P as D}from"./phone-DVvySU_L.js";import{M as k}from"./message-circle-CPYXbNOk.js";import{X as C}from"./x-D2_ybCL2.js";function H({auth:r,driver:s}){const[z,T]=j.useState(!1),{data:t,setData:m,post:o,processing:d,errors:n}=f({start_time:"",end_time:"",notes:""}),h=a=>{a.preventDefault(),o(`/drivers/${s.id}/book`)},g=()=>{if(!t.start_time||!t.end_time)return 0;const a=new Date(t.start_time),c=new Date(t.end_time),l=Math.ceil((c.getTime()-a.getTime())/(1e3*60*60));return l>0?l*s.price_per_hour:0},p=a=>Array.from({length:5},(c,l)=>e.jsx(_,{size:20,className:l<Math.floor(a)?"text-yellow-400 fill-current":"text-gray-300"},l));return e.jsxs(e.Fragment,{children:[e.jsx(y,{title:`${s.name} - Professional Driver`}),e.jsxs("div",{className:"min-h-screen bg-gray-50",children:[e.jsx("header",{className:"bg-white shadow-md sticky top-0 z-50",children:e.jsx("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"flex justify-between items-center py-4",children:[e.jsxs(i,{href:"/",className:"flex items-center space-x-2",children:[e.jsx(N,{className:"h-8 w-8 text-primary-600"}),e.jsx("span",{className:"text-xl font-bold text-gray-900",children:"Park & Rent"})]}),e.jsxs("nav",{className:"hidden md:flex space-x-8",children:[e.jsx(i,{href:"/",className:"text-gray-700 hover:text-primary-600",children:"Home"}),e.jsx(i,{href:"/cars",className:"text-gray-700 hover:text-primary-600",children:"Browse Cars"}),e.jsx(i,{href:"/drivers",className:"text-primary-600 font-medium",children:"Hire a Driver"})]}),e.jsx("div",{className:"flex items-center space-x-4",children:r.user?e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("span",{className:"text-gray-700",children:["Welcome, ",r.user.name]}),e.jsx(i,{href:"/dashboard",className:"bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700",children:"Dashboard"})]}):e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx(i,{href:"/login",className:"text-gray-700 hover:text-primary-600",children:"Log In"}),e.jsx(i,{href:"/register",className:"bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700",children:"Sign Up"})]})})]})})}),e.jsx("div",{className:"bg-white border-b",children:e.jsx("div",{className:"container mx-auto px-4 py-3",children:e.jsxs("nav",{className:"flex items-center space-x-2 text-sm",children:[e.jsx(i,{href:"/",className:"text-gray-600 hover:text-primary-600",children:"Home"}),e.jsx("span",{className:"text-gray-400",children:"/"}),e.jsx(i,{href:"/drivers",className:"text-gray-600 hover:text-primary-600",children:"Drivers"}),e.jsx("span",{className:"text-gray-400",children:"/"}),e.jsx("span",{className:"text-gray-900",children:s.name})]})})}),e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[e.jsxs("div",{className:"lg:col-span-2",children:[e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6 mb-6",children:[e.jsxs("div",{className:"flex items-start justify-between mb-6",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center mr-6 overflow-hidden",children:s.profile_image?e.jsx("img",{src:s.profile_image,alt:s.name,className:"w-full h-full object-cover"}):e.jsx(b,{size:48,className:"text-gray-400"})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:s.name}),e.jsxs("div",{className:"flex items-center text-gray-600 mb-2",children:[e.jsx(u,{size:16,className:"mr-2"}),e.jsx("span",{children:s.location})]}),e.jsxs("div",{className:"flex items-center mb-2",children:[e.jsx("div",{className:"flex items-center mr-4",children:p(s.rating)}),e.jsxs("span",{className:"text-gray-600",children:[s.rating,"/5 (",s.reviews," reviews)"]})]}),e.jsxs("div",{className:"flex items-center space-x-4 text-sm text-gray-600",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(v,{size:16,className:"mr-1 text-primary-600"}),e.jsxs("span",{children:[s.experience," years experience"]})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(w,{size:16,className:"mr-1 text-primary-600"}),e.jsxs("span",{children:[s.age," years old"]})]})]})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:"text-3xl font-bold text-primary-600",children:["RWF ",s.price_per_hour.toLocaleString()]}),e.jsx("div",{className:"text-gray-600",children:"per hour"})]})]}),e.jsx("div",{className:"mb-4",children:e.jsxs("span",{className:`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${s.license_verification_status==="verified"?"bg-green-100 text-green-800":s.license_verification_status==="pending"?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"}`,children:[s.license_verification_status==="verified"&&e.jsx(x,{size:16,className:"mr-1"}),s.license_verification_status==="verified"?"Verified Driver":s.license_verification_status==="pending"?"Verification Pending":"Not Verified"]})}),e.jsx("div",{className:"mb-4",children:e.jsx("span",{className:`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${s.is_available?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:s.is_available?"Available":"Unavailable"})}),s.availability_notes&&e.jsxs("div",{className:"border-t pt-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Availability Notes"}),e.jsx("p",{className:"text-gray-700",children:s.availability_notes})]})]}),s.specialties&&s.specialties.length>0&&e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6 mb-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Specialties & Services"}),e.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-3",children:s.specialties.map((a,c)=>e.jsxs("div",{className:"flex items-center p-3 bg-primary-50 rounded-lg",children:[e.jsx(x,{size:16,className:"text-primary-600 mr-2"}),e.jsx("span",{className:"text-gray-700 font-medium",children:a})]},c))})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6 mb-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Driver Statistics"}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[e.jsxs("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[e.jsx("div",{className:"text-2xl font-bold text-primary-600",children:s.experience}),e.jsx("div",{className:"text-sm text-gray-600",children:"Years Experience"})]}),e.jsxs("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[e.jsx("div",{className:"text-2xl font-bold text-primary-600",children:s.rating}),e.jsx("div",{className:"text-sm text-gray-600",children:"Average Rating"})]}),e.jsxs("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[e.jsx("div",{className:"text-2xl font-bold text-primary-600",children:s.reviews}),e.jsx("div",{className:"text-sm text-gray-600",children:"Total Reviews"})]}),e.jsxs("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[e.jsx("div",{className:"text-2xl font-bold text-primary-600",children:s.age}),e.jsx("div",{className:"text-sm text-gray-600",children:"Age"})]})]})]}),s.user&&e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Contact Information"}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(S,{size:16,className:"text-gray-400 mr-3"}),e.jsx("span",{className:"text-gray-700",children:s.user.email})]}),s.user.phone_number&&e.jsxs("div",{className:"flex items-center",children:[e.jsx(D,{size:16,className:"text-gray-400 mr-3"}),e.jsx("span",{className:"text-gray-700",children:s.user.phone_number})]})]}),r.user&&r.user.id!==s.user.id&&e.jsx("div",{className:"mt-4 pt-4 border-t",children:e.jsxs("button",{className:"flex items-center px-4 py-2 bg-primary-100 text-primary-700 rounded-md hover:bg-primary-200 transition-colors",children:[e.jsx(k,{size:16,className:"mr-2"}),"Send Message"]})})]})]}),e.jsx("div",{className:"lg:col-span-1",children:e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6 sticky top-24",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Hire This Driver"}),r.user?s.is_available&&s.license_verification_status==="verified"?e.jsxs("form",{onSubmit:h,className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Start Date & Time"}),e.jsx("input",{type:"datetime-local",value:t.start_time,onChange:a=>m("start_time",a.target.value),min:new Date().toISOString().slice(0,16),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500",required:!0}),n.start_time&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:n.start_time})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"End Date & Time"}),e.jsx("input",{type:"datetime-local",value:t.end_time,onChange:a=>m("end_time",a.target.value),min:t.start_time||new Date().toISOString().slice(0,16),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500",required:!0}),n.end_time&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:n.end_time})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Service Notes (Optional)"}),e.jsx("textarea",{value:t.notes,onChange:a=>m("notes",a.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500",placeholder:"Describe your driving needs, destinations, special requirements..."})]}),t.start_time&&t.end_time&&e.jsxs("div",{className:"bg-gray-50 p-4 rounded-md",children:[e.jsxs("div",{className:"flex justify-between items-center mb-2",children:[e.jsx("span",{className:"text-gray-700",children:"Duration:"}),e.jsxs("span",{className:"font-medium",children:[Math.ceil((new Date(t.end_time).getTime()-new Date(t.start_time).getTime())/(1e3*60*60))," hours"]})]}),e.jsxs("div",{className:"flex justify-between items-center mb-2",children:[e.jsx("span",{className:"text-gray-700",children:"Rate:"}),e.jsxs("span",{className:"font-medium",children:["RWF ",s.price_per_hour.toLocaleString(),"/hr"]})]}),e.jsxs("div",{className:"border-t pt-2 flex justify-between items-center",children:[e.jsx("span",{className:"font-semibold text-gray-900",children:"Total:"}),e.jsxs("span",{className:"font-bold text-primary-600 text-lg",children:["RWF ",g().toLocaleString()]})]})]}),e.jsx("button",{type:"submit",disabled:d||!t.start_time||!t.end_time,className:"w-full bg-primary-600 text-white py-3 rounded-md hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed font-medium",children:d?"Booking...":"Book Driver"})]}):e.jsxs("div",{className:"text-center py-8",children:[e.jsx(C,{size:48,className:"mx-auto text-red-400 mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"Driver Unavailable"}),e.jsx("p",{className:"text-gray-600",children:s.is_available?"This driver is pending verification.":"This driver is currently not available for booking."})]}):e.jsxs("div",{className:"text-center py-8",children:[e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"Sign in to Book"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"You need to be logged in to hire this driver."}),e.jsx(i,{href:"/login",className:"w-full bg-primary-600 text-white py-3 rounded-md hover:bg-primary-700 font-medium inline-block text-center",children:"Sign In"})]}),e.jsxs("div",{className:"mt-6 pt-6 border-t",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-900 mb-2",children:"License Information"}),e.jsxs("p",{className:"text-sm text-gray-600",children:["License #: ",s.license_number]}),e.jsxs("p",{className:"text-sm text-gray-600",children:["Status: ",e.jsx("span",{className:`font-medium ${s.license_verification_status==="verified"?"text-green-600":"text-yellow-600"}`,children:s.license_verification_status.charAt(0).toUpperCase()+s.license_verification_status.slice(1)})]})]})]})})]})})]})]})}export{H as default};
