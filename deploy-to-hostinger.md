# Park & Rent - Hostinger Deployment Guide

## Overview
This guide will help you deploy your Park & Rent application to Hostinger hosting. The application consists of:
- **React Frontend** (built with Vite)
- **Laravel API Backend** (PHP 8.2+)

## Prerequisites
- Hostinger hosting account with PHP 8.2+ support
- MySQL database access
- Domain configured (ebisera.com)
- FTP/File Manager access

## Deployment Steps

### Step 1: Prepare Files for Upload

#### Frontend Files (React)
The frontend has been built and is ready in the `dist/` folder. These files need to go to your domain's public_html folder.

#### Backend Files (Laravel API)
The Laravel API files in `park-and-rent-api/` need to be uploaded to a subdirectory.

### Step 2: File Structure on Hostinger

```
public_html/
├── index.html (React app entry point)
├── assets/ (React built assets)
├── manifest.webmanifest
├── registerSW.js
├── sw.js
├── workbox-*.js
├── api/ (Laravel API - symbolic link or subdirectory)
│   ├── public/
│   │   └── index.php (Laravel entry point)
│   ├── app/
│   ├── bootstrap/
│   ├── config/
│   ├── database/
│   ├── resources/
│   ├── routes/
│   ├── storage/
│   ├── vendor/
│   ├── .env
│   └── composer.json
└── .htaccess (Root level routing)
```

### Step 3: Upload Files

#### Option A: Using Hostinger File Manager
1. Login to your Hostinger control panel
2. Go to File Manager
3. Navigate to public_html
4. Upload the contents of `dist/` folder to public_html root
5. Create an `api` folder in public_html
6. Upload the entire `park-and-rent-api/` contents to the `api` folder

#### Option B: Using FTP Client
1. Connect to your Hostinger FTP
2. Upload `dist/*` to `/public_html/`
3. Upload `park-and-rent-api/*` to `/public_html/api/`

### Step 4: Configure Laravel on Hostinger

1. **Set up Environment File**
   - Copy `.env.production` to `.env` in the API folder
   - Update database credentials if needed

2. **Install Composer Dependencies**
   - SSH into your Hostinger account (if available) or use Hostinger's terminal
   - Navigate to the API folder
   - Run: `composer install --optimize-autoloader --no-dev`

3. **Set up Database**
   - Create MySQL database in Hostinger control panel
   - Update `.env` with correct database credentials
   - Run migrations: `php artisan migrate --force`
   - Seed admin user: `php artisan db:seed --class=AdminSeeder --force`

4. **Generate Application Key**
   - Run: `php artisan key:generate --force`

5. **Set File Permissions**
   - Set storage and bootstrap/cache folders to 755
   - Set .env file to 644

6. **Create Storage Link**
   - Run: `php artisan storage:link`

### Step 5: Configure Web Server

#### Root .htaccess (public_html/.htaccess)
```apache
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # API Routes - redirect to Laravel
    RewriteRule ^api/(.*)$ api/public/index.php [L,QSA]
    
    # Handle React Router (SPA)
    RewriteBase /
    RewriteRule ^index\.html$ - [L]
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} !^/api/
    RewriteRule . /index.html [L]
</IfModule>

# Security Headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options SAMEORIGIN
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# Compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# Cache static assets
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
</IfModule>
```

### Step 6: Database Configuration

1. **Create Database in Hostinger**
   - Go to Hostinger control panel
   - Create new MySQL database
   - Note down: database name, username, password

2. **Update Environment Variables**
   ```env
   DB_CONNECTION=mysql
   DB_HOST=localhost
   DB_PORT=3306
   DB_DATABASE=u555127771_parkandrent_db
   DB_USERNAME=u555127771_parkandrent_us
   DB_PASSWORD=Ebisera@2020
   ```

3. **Run Migrations**
   ```bash
   cd /public_html/api
   php artisan migrate --force
   php artisan db:seed --class=AdminSeeder --force
   ```

### Step 7: SSL and Domain Configuration

1. **Enable SSL in Hostinger**
   - Go to SSL section in control panel
   - Enable SSL for ebisera.com

2. **Update Environment URLs**
   ```env
   APP_URL=https://ebisera.com
   FRONTEND_URL=https://ebisera.com
   SANCTUM_STATEFUL_DOMAINS=ebisera.com,www.ebisera.com
   SESSION_DOMAIN=.ebisera.com
   ```

### Step 8: Testing

1. **Test Frontend**
   - Visit https://ebisera.com
   - Check if React app loads correctly

2. **Test API**
   - Visit https://ebisera.com/api/cars
   - Should return JSON response

3. **Test Full Integration**
   - Try logging in from frontend
   - Test car listings, driver listings
   - Test admin dashboard

### Step 9: Post-Deployment Optimization

1. **Enable Caching**
   ```bash
   php artisan config:cache
   php artisan route:cache
   php artisan view:cache
   ```

2. **Set up Cron Jobs** (if needed)
   - Add Laravel scheduler to crontab
   ```bash
   * * * * * cd /path/to/api && php artisan schedule:run >> /dev/null 2>&1
   ```

3. **Monitor Logs**
   - Check Laravel logs in `storage/logs/`
   - Monitor error logs in Hostinger control panel

## Troubleshooting

### Common Issues

1. **500 Internal Server Error**
   - Check file permissions
   - Verify .env configuration
   - Check error logs

2. **Database Connection Issues**
   - Verify database credentials
   - Check if database exists
   - Ensure MySQL service is running

3. **CORS Issues**
   - Update CORS configuration in Laravel
   - Check frontend API base URL

4. **File Upload Issues**
   - Check storage folder permissions
   - Verify storage link exists

### Contact Information
- Phone: 0788613669
- Email: <EMAIL>

## Security Checklist

- [ ] SSL certificate enabled
- [ ] .env file protected
- [ ] File permissions set correctly
- [ ] Debug mode disabled in production
- [ ] Database credentials secure
- [ ] Admin user created with strong password
- [ ] CORS properly configured
- [ ] Security headers enabled
