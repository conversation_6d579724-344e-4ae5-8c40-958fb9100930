<?php

namespace Database\Seeders;

use App\Models\Car;
use App\Models\User;
use Illuminate\Database\Seeder;

class CarSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the owner user
        $owner = User::where('email', '<EMAIL>')->first();

        if (!$owner) {
            return;
        }

        // Create sample cars
        Car::create([
            'owner_id' => $owner->id,
            'make' => 'Toyota',
            'model' => 'Corolla',
            'year' => 2020,
            'images' => [
                'https://images.unsplash.com/photo-1549924231-f129b911e442?w=800',
                'https://images.unsplash.com/photo-1549924231-f129b911e442?w=800',
            ],
            'description' => 'A reliable and fuel-efficient sedan perfect for city driving.',
            'features' => [
                'Air Conditioning',
                'Bluetooth',
                'Backup Camera',
                'USB Ports',
            ],
            'location' => 'Kigali, Rwanda',
            'price_per_hour' => 15000, // RWF
            'availability_notes' => 'Available on weekdays and weekends',
            'is_active' => true,
        ]);

        Car::create([
            'owner_id' => $owner->id,
            'make' => 'Honda',
            'model' => 'Civic',
            'year' => 2019,
            'images' => [
                'https://images.unsplash.com/photo-1606664515524-ed2f786a0bd6?w=800',
                'https://images.unsplash.com/photo-1606664515524-ed2f786a0bd6?w=800',
            ],
            'description' => 'Sporty and comfortable compact car with excellent fuel economy.',
            'features' => [
                'Air Conditioning',
                'Bluetooth',
                'Sunroof',
                'Heated Seats',
                'Navigation System',
            ],
            'location' => 'Kigali, Rwanda',
            'price_per_hour' => 18000, // RWF
            'availability_notes' => 'Available daily from 8 AM to 8 PM',
            'is_active' => true,
        ]);

        Car::create([
            'owner_id' => $owner->id,
            'make' => 'Nissan',
            'model' => 'Altima',
            'year' => 2021,
            'images' => [
                'https://images.unsplash.com/photo-1605559424843-9e4c228bf1c2?w=800',
                'https://images.unsplash.com/photo-1605559424843-9e4c228bf1c2?w=800',
            ],
            'description' => 'Modern sedan with advanced safety features and comfortable interior.',
            'features' => [
                'Air Conditioning',
                'Bluetooth',
                'Backup Camera',
                'Lane Departure Warning',
                'Automatic Emergency Braking',
            ],
            'location' => 'Kigali, Rwanda',
            'price_per_hour' => 20000, // RWF
            'availability_notes' => 'Available for long-term rentals',
            'is_active' => true,
        ]);
    }
}
