import React, { useState, useEffect, useRef } from 'react';
import { Head, Link, useForm, router } from '@inertiajs/react';
import { 
  Car, 
  MessageCircle, 
  Send, 
  Search, 
  UserRound,
  Phone,
  Video,
  MoreVertical,
  ArrowLeft
} from 'lucide-react';
import { PageProps, Chat, Message, User } from '@/types';

interface ChatIndexProps extends PageProps {
  chats: Chat[];
  activeChat?: Chat;
  messages: Message[];
}

export default function ChatIndex({ auth, chats, activeChat, messages }: ChatIndexProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [isMobileView, setIsMobileView] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  const { data, setData, post, processing, reset } = useForm({
    content: '',
  });

  const filteredChats = chats.filter(chat => {
    const otherUser = chat.user_id === auth.user.id ? chat.recipient : chat.user;
    return otherUser?.name.toLowerCase().includes(searchTerm.toLowerCase());
  });

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault();
    if (!data.content.trim() || !activeChat) return;

    post(`/chats/${activeChat.id}/messages`, {
      onSuccess: () => {
        reset('content');
        // Refresh messages
        router.reload({ only: ['messages'] });
      }
    });
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) {
      return 'Today';
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Yesterday';
    } else {
      return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
    }
  };

  const getOtherUser = (chat: Chat): User | null => {
    return chat.user_id === auth.user.id ? chat.recipient : chat.user;
  };

  const getLastMessage = (chat: Chat): string => {
    if (chat.messages && chat.messages.length > 0) {
      const lastMessage = chat.messages[chat.messages.length - 1];
      return lastMessage.content.length > 50 
        ? lastMessage.content.substring(0, 50) + '...'
        : lastMessage.content;
    }
    return 'No messages yet';
  };

  return (
    <>
      <Head title="Messages" />
      
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <header className="bg-white shadow-md sticky top-0 z-50">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-4">
              <Link href="/" className="flex items-center space-x-2">
                <Car className="h-8 w-8 text-primary-600" />
                <span className="text-xl font-bold text-gray-900">Park & Rent</span>
              </Link>

              <nav className="hidden md:flex space-x-8">
                <Link href="/" className="text-gray-700 hover:text-primary-600">
                  Home
                </Link>
                <Link href="/cars" className="text-gray-700 hover:text-primary-600">
                  Browse Cars
                </Link>
                <Link href="/drivers" className="text-gray-700 hover:text-primary-600">
                  Hire a Driver
                </Link>
                <Link href="/dashboard" className="text-gray-700 hover:text-primary-600">
                  Dashboard
                </Link>
              </nav>

              <div className="flex items-center space-x-4">
                <span className="text-gray-700">Welcome, {auth.user.name}</span>
                <Link
                  href="/dashboard"
                  className="bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700"
                >
                  Dashboard
                </Link>
              </div>
            </div>
          </div>
        </header>

        <div className="container mx-auto px-4 py-8">
          <div className="bg-white rounded-lg shadow-md overflow-hidden" style={{ height: '600px' }}>
            <div className="flex h-full">
              {/* Chat List Sidebar */}
              <div className={`w-full md:w-1/3 border-r border-gray-200 flex flex-col ${activeChat && isMobileView ? 'hidden md:flex' : ''}`}>
                {/* Chat List Header */}
                <div className="p-4 border-b border-gray-200">
                  <h2 className="text-lg font-semibold text-gray-900 mb-3">Messages</h2>
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Search conversations..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
                    />
                  </div>
                </div>

                {/* Chat List */}
                <div className="flex-1 overflow-y-auto">
                  {filteredChats.length > 0 ? (
                    filteredChats.map((chat) => {
                      const otherUser = getOtherUser(chat);
                      if (!otherUser) return null;

                      return (
                        <Link
                          key={chat.id}
                          href={`/chats/${chat.id}`}
                          className={`flex items-center p-4 hover:bg-gray-50 border-b border-gray-100 ${
                            activeChat?.id === chat.id ? 'bg-primary-50 border-primary-200' : ''
                          }`}
                          onClick={() => setIsMobileView(true)}
                        >
                          <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center mr-3">
                            <span className="text-gray-600 font-semibold">
                              {otherUser.name.charAt(0).toUpperCase()}
                            </span>
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex justify-between items-center mb-1">
                              <h3 className="font-medium text-gray-900 truncate">
                                {otherUser.name}
                              </h3>
                              {chat.last_message_at && (
                                <span className="text-xs text-gray-500">
                                  {formatDate(chat.last_message_at)}
                                </span>
                              )}
                            </div>
                            <p className="text-sm text-gray-600 truncate">
                              {getLastMessage(chat)}
                            </p>
                          </div>
                        </Link>
                      );
                    })
                  ) : (
                    <div className="p-8 text-center">
                      <MessageCircle size={48} className="mx-auto text-gray-400 mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">No conversations</h3>
                      <p className="text-gray-600">
                        Start a conversation by contacting a car owner or driver.
                      </p>
                    </div>
                  )}
                </div>
              </div>

              {/* Chat Area */}
              <div className={`flex-1 flex flex-col ${!activeChat || (!isMobileView && window.innerWidth < 768) ? 'hidden md:flex' : ''}`}>
                {activeChat ? (
                  <>
                    {/* Chat Header */}
                    <div className="p-4 border-b border-gray-200 flex items-center justify-between">
                      <div className="flex items-center">
                        <button
                          onClick={() => setIsMobileView(false)}
                          className="md:hidden mr-3 p-1 hover:bg-gray-100 rounded"
                        >
                          <ArrowLeft size={20} />
                        </button>
                        <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center mr-3">
                          <span className="text-gray-600 font-semibold">
                            {getOtherUser(activeChat)?.name.charAt(0).toUpperCase()}
                          </span>
                        </div>
                        <div>
                          <h3 className="font-medium text-gray-900">
                            {getOtherUser(activeChat)?.name}
                          </h3>
                          <p className="text-sm text-gray-600">
                            {getOtherUser(activeChat)?.role.charAt(0).toUpperCase() + getOtherUser(activeChat)?.role.slice(1)}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <button className="p-2 hover:bg-gray-100 rounded-full">
                          <Phone size={18} className="text-gray-600" />
                        </button>
                        <button className="p-2 hover:bg-gray-100 rounded-full">
                          <Video size={18} className="text-gray-600" />
                        </button>
                        <button className="p-2 hover:bg-gray-100 rounded-full">
                          <MoreVertical size={18} className="text-gray-600" />
                        </button>
                      </div>
                    </div>

                    {/* Messages Area */}
                    <div className="flex-1 overflow-y-auto p-4 space-y-4">
                      {messages.map((message) => {
                        const isOwnMessage = message.sender_id === auth.user.id;
                        return (
                          <div
                            key={message.id}
                            className={`flex ${isOwnMessage ? 'justify-end' : 'justify-start'}`}
                          >
                            <div
                              className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                                isOwnMessage
                                  ? 'bg-primary-600 text-white'
                                  : 'bg-gray-200 text-gray-900'
                              }`}
                            >
                              <p className="text-sm">{message.content}</p>
                              <p
                                className={`text-xs mt-1 ${
                                  isOwnMessage ? 'text-primary-100' : 'text-gray-500'
                                }`}
                              >
                                {formatTime(message.created_at)}
                              </p>
                            </div>
                          </div>
                        );
                      })}
                      <div ref={messagesEndRef} />
                    </div>

                    {/* Message Input */}
                    <div className="p-4 border-t border-gray-200">
                      <form onSubmit={handleSendMessage} className="flex space-x-2">
                        <input
                          type="text"
                          value={data.content}
                          onChange={(e) => setData('content', e.target.value)}
                          placeholder="Type a message..."
                          className="flex-1 px-4 py-2 border border-gray-300 rounded-full focus:ring-primary-500 focus:border-primary-500"
                          disabled={processing}
                        />
                        <button
                          type="submit"
                          disabled={processing || !data.content.trim()}
                          className="p-2 bg-primary-600 text-white rounded-full hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          <Send size={18} />
                        </button>
                      </form>
                    </div>
                  </>
                ) : (
                  <div className="flex-1 flex items-center justify-center">
                    <div className="text-center">
                      <MessageCircle size={64} className="mx-auto text-gray-400 mb-4" />
                      <h3 className="text-xl font-medium text-gray-900 mb-2">
                        Select a conversation
                      </h3>
                      <p className="text-gray-600">
                        Choose a conversation from the sidebar to start messaging.
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
