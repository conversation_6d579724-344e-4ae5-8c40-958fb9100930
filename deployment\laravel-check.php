<?php
// <PERSON><PERSON> Diagnostic Script
// Upload this to public_html/api/ and visit it in browser

echo "<h2><PERSON><PERSON> Diagnostic Check</h2>";

// Check PHP version
echo "<h3>PHP Version</h3>";
echo "Current PHP Version: " . phpversion() . "<br>";
echo "Required: PHP 8.2+<br>";
echo (version_compare(phpversion(), '8.2.0', '>=') ? "✅ PHP version OK" : "❌ PHP version too old") . "<br><br>";

// Check required extensions
echo "<h3>Required PHP Extensions</h3>";
$required_extensions = ['openssl', 'pdo', 'mbstring', 'tokenizer', 'xml', 'ctype', 'json', 'bcmath'];
foreach ($required_extensions as $ext) {
    echo $ext . ": " . (extension_loaded($ext) ? "✅ Loaded" : "❌ Missing") . "<br>";
}

echo "<br><h3>File System Check</h3>";

// Check if files exist
$files_to_check = [
    '.env' => 'Environment file',
    'vendor/autoload.php' => 'Composer dependencies',
    'bootstrap/app.php' => 'Laravel bootstrap',
    'public/index.php' => 'Laravel entry point',
    'storage/logs/' => 'Log directory',
    'bootstrap/cache/' => 'Cache directory'
];

foreach ($files_to_check as $file => $description) {
    $exists = file_exists($file);
    echo $description . " ($file): " . ($exists ? "✅ Exists" : "❌ Missing") . "<br>";
    
    if ($exists && is_dir($file)) {
        echo "  - Writable: " . (is_writable($file) ? "✅ Yes" : "❌ No") . "<br>";
    }
}

echo "<br><h3>Environment Check</h3>";
if (file_exists('.env')) {
    echo "✅ .env file exists<br>";
    $env_content = file_get_contents('.env');
    echo "APP_KEY set: " . (strpos($env_content, 'APP_KEY=base64:') !== false ? "✅ Yes" : "❌ No") . "<br>";
    echo "DB_CONNECTION set: " . (strpos($env_content, 'DB_CONNECTION=') !== false ? "✅ Yes" : "❌ No") . "<br>";
} else {
    echo "❌ .env file missing<br>";
}

echo "<br><h3>Laravel Test</h3>";
try {
    if (file_exists('vendor/autoload.php')) {
        require_once 'vendor/autoload.php';
        echo "✅ Autoloader works<br>";
        
        if (file_exists('bootstrap/app.php')) {
            $app = require_once 'bootstrap/app.php';
            echo "✅ Laravel app loads<br>";
        } else {
            echo "❌ Laravel bootstrap missing<br>";
        }
    } else {
        echo "❌ Composer autoloader missing<br>";
    }
} catch (Exception $e) {
    echo "❌ Laravel error: " . $e->getMessage() . "<br>";
}

echo "<br><h3>Next Steps</h3>";
echo "1. Fix any ❌ issues above<br>";
echo "2. If all looks good, check error logs<br>";
echo "3. Contact support if needed<br>";
?>
