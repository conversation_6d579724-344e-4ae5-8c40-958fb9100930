<?php

namespace App\Http\Controllers;

use App\Models\Car;
use App\Models\GpsInstallationRequest;
use Illuminate\Http\Request;
use Inertia\Inertia;

class GpsRequestController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function create()
    {
        // Get user's cars
        $cars = Car::where('owner_id', auth()->id())
            ->where('is_active', true)
            ->get();

        return Inertia::render('GpsRequest', [
            'cars' => $cars,
        ]);
    }

    public function store(Request $request)
    {
        $request->validate([
            'car_id' => 'required|exists:cars,id',
            'car_make' => 'required|string|max:255',
            'car_model' => 'required|string|max:255',
            'car_year' => 'required|string|max:4',
            'license_plate' => 'nullable|string|max:20',
            'reason' => 'required|string|max:1000',
            'contact_phone' => 'required|string|max:20',
            'preferred_installation_date' => 'nullable|date|after:today',
        ]);

        // Verify the car belongs to the user
        $car = Car::where('id', $request->car_id)
            ->where('owner_id', auth()->id())
            ->firstOrFail();

        // Check if there's already a pending request for this car
        $existingRequest = GpsInstallationRequest::where('car_id', $car->id)
            ->whereIn('status', ['pending', 'approved'])
            ->first();

        if ($existingRequest) {
            return back()->withErrors(['car_id' => 'There is already a pending GPS installation request for this car.']);
        }

        GpsInstallationRequest::create([
            'user_id' => auth()->id(),
            'car_id' => $car->id,
            'car_make' => $request->car_make,
            'car_model' => $request->car_model,
            'car_year' => $request->car_year,
            'license_plate' => $request->license_plate,
            'reason' => $request->reason,
            'contact_phone' => $request->contact_phone,
            'preferred_installation_date' => $request->preferred_installation_date,
            'status' => 'pending',
        ]);

        return redirect()->route('dashboard')
            ->with('message', 'GPS installation request submitted successfully! Our team will review your request and contact you soon.');
    }

    public function index()
    {
        $requests = GpsInstallationRequest::with(['car'])
            ->where('user_id', auth()->id())
            ->latest()
            ->get();

        return Inertia::render('GpsRequests/Index', [
            'requests' => $requests,
        ]);
    }
}
