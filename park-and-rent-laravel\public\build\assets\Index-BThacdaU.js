import{r as n,j as e,$ as f,Y as r,m as g}from"./app-Cj-kSxmT.js";import{C as c}from"./car-CXkXzXL9.js";import{S as v}from"./search-BLfvhpd5.js";import{F as w}from"./funnel-C0C45j41.js";import{H as k}from"./heart-DQdqQOFe.js";import{M as C}from"./map-pin-DpBG7oTH.js";function z({auth:m,cars:i,filters:t}){const[d,x]=n.useState(t.search||""),[o,j]=n.useState(!1),[a,l]=n.useState({location:t.location||"",min_price:t.min_price||"",max_price:t.max_price||"",make:t.make||""}),u=s=>{s.preventDefault(),g.get("/cars",{search:d,...a},{preserveState:!0,preserveScroll:!0})},h=()=>{x(""),l({location:"",min_price:"",max_price:"",make:""}),g.get("/cars")},y=[...new Set(i.map(s=>s.make))];return e.jsxs(e.Fragment,{children:[e.jsx(f,{title:"Browse Cars"}),e.jsxs("div",{className:"min-h-screen bg-gray-50",children:[e.jsx("header",{className:"bg-white shadow-md sticky top-0 z-50",children:e.jsx("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"flex justify-between items-center py-4",children:[e.jsxs(r,{href:"/",className:"flex items-center space-x-2",children:[e.jsx(c,{className:"h-8 w-8 text-primary-600"}),e.jsx("span",{className:"text-xl font-bold text-gray-900",children:"Park & Rent"})]}),e.jsxs("nav",{className:"hidden md:flex space-x-8",children:[e.jsx(r,{href:"/",className:"text-gray-700 hover:text-primary-600",children:"Home"}),e.jsx(r,{href:"/cars",className:"text-primary-600 font-medium",children:"Browse Cars"}),e.jsx(r,{href:"/drivers",className:"text-gray-700 hover:text-primary-600",children:"Hire a Driver"})]}),e.jsx("div",{className:"flex items-center space-x-4",children:m.user?e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("span",{className:"text-gray-700",children:["Welcome, ",m.user.name]}),e.jsx(r,{href:"/dashboard",className:"bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700",children:"Dashboard"})]}):e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx(r,{href:"/login",className:"text-gray-700 hover:text-primary-600",children:"Log In"}),e.jsx(r,{href:"/register",className:"bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700",children:"Sign Up"})]})})]})})}),e.jsx("section",{className:"bg-primary-600 text-white py-12",children:e.jsx("div",{className:"container mx-auto px-4",children:e.jsxs("div",{className:"text-center",children:[e.jsx("h1",{className:"text-4xl font-bold mb-4",children:"Find Your Perfect Car"}),e.jsxs("p",{className:"text-xl text-primary-100 mb-8",children:["Browse ",i.length," available cars from local owners"]}),e.jsx("form",{onSubmit:u,className:"max-w-4xl mx-auto",children:e.jsxs("div",{className:"bg-white rounded-lg p-4 shadow-lg",children:[e.jsxs("div",{className:"flex flex-col md:flex-row gap-4",children:[e.jsx("div",{className:"flex-1",children:e.jsxs("div",{className:"relative",children:[e.jsx(v,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5"}),e.jsx("input",{type:"text",placeholder:"Search by make, model, or description...",value:d,onChange:s=>x(s.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500 text-gray-900"})]})}),e.jsxs("button",{type:"button",onClick:()=>j(!o),className:"flex items-center justify-center px-6 py-3 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50",children:[e.jsx(w,{className:"h-5 w-5 mr-2"}),"Filters"]}),e.jsx("button",{type:"submit",className:"bg-primary-600 text-white px-8 py-3 rounded-md hover:bg-primary-700 font-medium",children:"Search"})]}),o&&e.jsxs("div",{className:"mt-4 pt-4 border-t border-gray-200",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Location"}),e.jsx("input",{type:"text",placeholder:"Enter location",value:a.location,onChange:s=>l({...a,location:s.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500 text-gray-900"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Make"}),e.jsxs("select",{value:a.make,onChange:s=>l({...a,make:s.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500 text-gray-900",children:[e.jsx("option",{value:"",children:"All Makes"}),y.map(s=>e.jsx("option",{value:s,children:s},s))]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Min Price (RWF/hr)"}),e.jsx("input",{type:"number",placeholder:"0",value:a.min_price,onChange:s=>l({...a,min_price:s.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500 text-gray-900"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Max Price (RWF/hr)"}),e.jsx("input",{type:"number",placeholder:"100000",value:a.max_price,onChange:s=>l({...a,max_price:s.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500 text-gray-900"})]})]}),e.jsx("div",{className:"mt-4 flex justify-end",children:e.jsx("button",{type:"button",onClick:h,className:"text-gray-600 hover:text-gray-800 mr-4",children:"Clear Filters"})})]})]})})]})})}),e.jsx("section",{className:"py-12",children:e.jsx("div",{className:"container mx-auto px-4",children:i.length>0?e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:i.map(s=>{var p;return e.jsxs("div",{className:"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow group",children:[e.jsxs("div",{className:"relative h-48 bg-gray-200",children:[s.images&&s.images.length>0?e.jsx("img",{src:s.images[0],alt:`${s.make} ${s.model}`,className:"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"}):e.jsx("div",{className:"w-full h-full flex items-center justify-center",children:e.jsx(c,{size:48,className:"text-gray-400"})}),e.jsx("button",{className:"absolute top-3 right-3 p-2 bg-white rounded-full shadow-md hover:bg-gray-50 transition-colors",children:e.jsx(k,{size:16,className:"text-gray-600"})}),e.jsx("div",{className:"absolute top-3 left-3",children:e.jsx("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${s.is_active?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:s.is_active?"Available":"Unavailable"})})]}),e.jsxs("div",{className:"p-4",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900 mb-1",children:[s.make," ",s.model]}),e.jsxs("p",{className:"text-sm text-gray-600 mb-2",children:[s.year," • ",(p=s.owner)==null?void 0:p.name]}),e.jsxs("div",{className:"flex items-center text-gray-600 mb-2",children:[e.jsx(C,{size:14,className:"mr-1"}),e.jsx("span",{className:"text-sm",children:s.location})]}),e.jsx("p",{className:"text-gray-600 text-sm mb-3 line-clamp-2",children:s.description}),s.features&&s.features.length>0&&e.jsx("div",{className:"mb-3",children:e.jsxs("div",{className:"flex flex-wrap gap-1",children:[s.features.slice(0,3).map((b,N)=>e.jsx("span",{className:"bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded",children:b},N)),s.features.length>3&&e.jsxs("span",{className:"text-xs text-gray-500",children:["+",s.features.length-3," more"]})]})}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsxs("span",{className:"text-2xl font-bold text-primary-600",children:["RWF ",s.price_per_hour.toLocaleString()]}),e.jsx("span",{className:"text-sm text-gray-600",children:"/hr"})]}),e.jsx(r,{href:`/cars/${s.id}`,className:"bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700 transition duration-200 text-sm font-medium",children:"View Details"})]})]})]},s.id)})}):e.jsxs("div",{className:"text-center py-12",children:[e.jsx(c,{size:64,className:"mx-auto text-gray-400 mb-4"}),e.jsx("h3",{className:"text-xl font-medium text-gray-900 mb-2",children:"No cars found"}),e.jsx("p",{className:"text-gray-600 mb-6",children:"Try adjusting your search criteria or browse all available cars."}),e.jsx("button",{onClick:h,className:"bg-primary-600 text-white px-6 py-2 rounded-md hover:bg-primary-700",children:"Clear Filters"})]})})}),e.jsx("footer",{className:"bg-gray-900 text-white py-12",children:e.jsxs("div",{className:"container mx-auto px-4",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center space-x-2 mb-4",children:[e.jsx(c,{className:"h-8 w-8 text-primary-400"}),e.jsx("span",{className:"text-xl font-bold",children:"Park & Rent"})]}),e.jsx("p",{className:"text-gray-400",children:"The trusted peer-to-peer car rental platform connecting car owners with renters."})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-lg font-semibold mb-4",children:"Quick Links"}),e.jsxs("ul",{className:"space-y-2",children:[e.jsx("li",{children:e.jsx(r,{href:"/cars",className:"text-gray-400 hover:text-white",children:"Browse Cars"})}),e.jsx("li",{children:e.jsx(r,{href:"/drivers",className:"text-gray-400 hover:text-white",children:"Find Drivers"})}),e.jsx("li",{children:e.jsx(r,{href:"/register",className:"text-gray-400 hover:text-white",children:"List Your Car"})})]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-lg font-semibold mb-4",children:"Support"}),e.jsxs("ul",{className:"space-y-2",children:[e.jsx("li",{children:e.jsx("a",{href:"#",className:"text-gray-400 hover:text-white",children:"Help Center"})}),e.jsx("li",{children:e.jsx("a",{href:"#",className:"text-gray-400 hover:text-white",children:"Safety"})}),e.jsx("li",{children:e.jsx("a",{href:"#",className:"text-gray-400 hover:text-white",children:"Contact Us"})})]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-lg font-semibold mb-4",children:"Contact"}),e.jsxs("p",{className:"text-gray-400",children:["Email: <EMAIL>",e.jsx("br",{}),"Phone: 0788613669"]})]})]}),e.jsx("div",{className:"border-t border-gray-800 mt-8 pt-8 text-center",children:e.jsx("p",{className:"text-gray-400",children:"© 2025 Park & Rent. All rights reserved."})})]})})]})]})}export{z as default};
