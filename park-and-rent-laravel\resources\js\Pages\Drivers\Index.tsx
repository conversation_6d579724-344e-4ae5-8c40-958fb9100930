import React, { useState } from 'react';
import { Head, Link, router } from '@inertiajs/react';
import { Car, MapPin, Search, Filter, Star, UserRound, Award, Clock } from 'lucide-react';
import { PageProps, Driver } from '@/types';

interface DriversIndexProps extends PageProps {
  drivers: Driver[];
  filters: {
    search?: string;
    location?: string;
    min_price?: number;
    max_price?: number;
    min_experience?: number;
    specialty?: string;
  };
}

export default function DriversIndex({ auth, drivers, filters }: DriversIndexProps) {
  const [searchTerm, setSearchTerm] = useState(filters.search || '');
  const [showFilters, setShowFilters] = useState(false);
  const [localFilters, setLocalFilters] = useState({
    location: filters.location || '',
    min_price: filters.min_price || '',
    max_price: filters.max_price || '',
    min_experience: filters.min_experience || '',
    specialty: filters.specialty || '',
  });

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    router.get('/drivers', { 
      search: searchTerm,
      ...localFilters 
    }, { 
      preserveState: true,
      preserveScroll: true 
    });
  };

  const clearFilters = () => {
    setSearchTerm('');
    setLocalFilters({
      location: '',
      min_price: '',
      max_price: '',
      min_experience: '',
      specialty: '',
    });
    router.get('/drivers');
  };

  const allSpecialties = [...new Set(
    drivers.flatMap(driver => driver.specialties || [])
  )];

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        size={16}
        className={i < Math.floor(rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'}
      />
    ));
  };

  return (
    <>
      <Head title="Hire a Driver" />
      
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <header className="bg-white shadow-md sticky top-0 z-50">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-4">
              <Link href="/" className="flex items-center space-x-2">
                <Car className="h-8 w-8 text-primary-600" />
                <span className="text-xl font-bold text-gray-900">Park & Rent</span>
              </Link>

              <nav className="hidden md:flex space-x-8">
                <Link href="/" className="text-gray-700 hover:text-primary-600">
                  Home
                </Link>
                <Link href="/cars" className="text-gray-700 hover:text-primary-600">
                  Browse Cars
                </Link>
                <Link href="/drivers" className="text-primary-600 font-medium">
                  Hire a Driver
                </Link>
              </nav>

              <div className="flex items-center space-x-4">
                {auth.user ? (
                  <div className="flex items-center space-x-4">
                    <span className="text-gray-700">Welcome, {auth.user.name}</span>
                    <Link
                      href="/dashboard"
                      className="bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700"
                    >
                      Dashboard
                    </Link>
                  </div>
                ) : (
                  <div className="flex items-center space-x-4">
                    <Link
                      href="/login"
                      className="text-gray-700 hover:text-primary-600"
                    >
                      Log In
                    </Link>
                    <Link
                      href="/register"
                      className="bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700"
                    >
                      Sign Up
                    </Link>
                  </div>
                )}
              </div>
            </div>
          </div>
        </header>

        {/* Hero Section */}
        <section className="bg-primary-600 text-white py-12">
          <div className="container mx-auto px-4">
            <div className="text-center">
              <h1 className="text-4xl font-bold mb-4">Hire Professional Drivers</h1>
              <p className="text-xl text-primary-100 mb-8">
                Find experienced drivers from our network of {drivers.length} verified professionals
              </p>

              {/* Search Form */}
              <form onSubmit={handleSearch} className="max-w-4xl mx-auto">
                <div className="bg-white rounded-lg p-4 shadow-lg">
                  <div className="flex flex-col md:flex-row gap-4">
                    <div className="flex-1">
                      <div className="relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                        <input
                          type="text"
                          placeholder="Search by name, location, or specialty..."
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                          className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500 text-gray-900"
                        />
                      </div>
                    </div>
                    <button
                      type="button"
                      onClick={() => setShowFilters(!showFilters)}
                      className="flex items-center justify-center px-6 py-3 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                    >
                      <Filter className="h-5 w-5 mr-2" />
                      Filters
                    </button>
                    <button
                      type="submit"
                      className="bg-primary-600 text-white px-8 py-3 rounded-md hover:bg-primary-700 font-medium"
                    >
                      Search
                    </button>
                  </div>

                  {/* Advanced Filters */}
                  {showFilters && (
                    <div className="mt-4 pt-4 border-t border-gray-200">
                      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Location
                          </label>
                          <input
                            type="text"
                            placeholder="Enter location"
                            value={localFilters.location}
                            onChange={(e) => setLocalFilters({...localFilters, location: e.target.value})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500 text-gray-900"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Specialty
                          </label>
                          <select
                            value={localFilters.specialty}
                            onChange={(e) => setLocalFilters({...localFilters, specialty: e.target.value})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500 text-gray-900"
                          >
                            <option value="">All Specialties</option>
                            {allSpecialties.map(specialty => (
                              <option key={specialty} value={specialty}>{specialty}</option>
                            ))}
                          </select>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Min Experience (years)
                          </label>
                          <input
                            type="number"
                            placeholder="0"
                            value={localFilters.min_experience}
                            onChange={(e) => setLocalFilters({...localFilters, min_experience: e.target.value})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500 text-gray-900"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Min Price (RWF/hr)
                          </label>
                          <input
                            type="number"
                            placeholder="0"
                            value={localFilters.min_price}
                            onChange={(e) => setLocalFilters({...localFilters, min_price: e.target.value})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500 text-gray-900"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Max Price (RWF/hr)
                          </label>
                          <input
                            type="number"
                            placeholder="50000"
                            value={localFilters.max_price}
                            onChange={(e) => setLocalFilters({...localFilters, max_price: e.target.value})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500 text-gray-900"
                          />
                        </div>
                      </div>
                      <div className="mt-4 flex justify-end">
                        <button
                          type="button"
                          onClick={clearFilters}
                          className="text-gray-600 hover:text-gray-800 mr-4"
                        >
                          Clear Filters
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </form>
            </div>
          </div>
        </section>

        {/* Drivers Grid */}
        <section className="py-12">
          <div className="container mx-auto px-4">
            {drivers.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {drivers.map((driver) => (
                  <div key={driver.id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                    <div className="p-6">
                      {/* Driver Header */}
                      <div className="flex items-center mb-4">
                        <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mr-4 overflow-hidden">
                          {driver.profile_image ? (
                            <img
                              src={driver.profile_image}
                              alt={driver.name}
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <UserRound size={32} className="text-gray-400" />
                          )}
                        </div>
                        <div className="flex-1">
                          <h3 className="text-xl font-semibold text-gray-900">{driver.name}</h3>
                          <div className="flex items-center text-gray-600 mb-1">
                            <MapPin size={14} className="mr-1" />
                            <span className="text-sm">{driver.location}</span>
                          </div>
                          <div className="flex items-center">
                            <div className="flex items-center mr-2">
                              {renderStars(driver.rating)}
                            </div>
                            <span className="text-sm text-gray-600">
                              {driver.rating}/5 ({driver.reviews} reviews)
                            </span>
                          </div>
                        </div>
                      </div>

                      {/* Driver Stats */}
                      <div className="grid grid-cols-2 gap-4 mb-4">
                        <div className="text-center p-3 bg-gray-50 rounded-lg">
                          <div className="flex items-center justify-center mb-1">
                            <Award size={16} className="text-primary-600 mr-1" />
                            <span className="text-sm font-medium text-gray-700">Experience</span>
                          </div>
                          <div className="text-lg font-bold text-gray-900">{driver.experience} years</div>
                        </div>
                        <div className="text-center p-3 bg-gray-50 rounded-lg">
                          <div className="flex items-center justify-center mb-1">
                            <Clock size={16} className="text-primary-600 mr-1" />
                            <span className="text-sm font-medium text-gray-700">Age</span>
                          </div>
                          <div className="text-lg font-bold text-gray-900">{driver.age} years</div>
                        </div>
                      </div>

                      {/* Specialties */}
                      {driver.specialties && driver.specialties.length > 0 && (
                        <div className="mb-4">
                          <h4 className="text-sm font-medium text-gray-700 mb-2">Specialties</h4>
                          <div className="flex flex-wrap gap-2">
                            {driver.specialties.slice(0, 4).map((specialty, index) => (
                              <span
                                key={index}
                                className="bg-primary-100 text-primary-800 text-xs px-2 py-1 rounded-full"
                              >
                                {specialty}
                              </span>
                            ))}
                            {driver.specialties.length > 4 && (
                              <span className="text-xs text-gray-500">
                                +{driver.specialties.length - 4} more
                              </span>
                            )}
                          </div>
                        </div>
                      )}

                      {/* Availability */}
                      {driver.availability_notes && (
                        <div className="mb-4">
                          <h4 className="text-sm font-medium text-gray-700 mb-1">Availability</h4>
                          <p className="text-sm text-gray-600">{driver.availability_notes}</p>
                        </div>
                      )}

                      {/* Verification Status */}
                      <div className="mb-4">
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                          driver.license_verification_status === 'verified'
                            ? 'bg-green-100 text-green-800'
                            : driver.license_verification_status === 'pending'
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {driver.license_verification_status === 'verified' && '✓ '}
                          {driver.license_verification_status === 'verified' ? 'Verified Driver' : 
                           driver.license_verification_status === 'pending' ? 'Verification Pending' : 
                           'Not Verified'}
                        </span>
                      </div>

                      {/* Price and Action */}
                      <div className="flex justify-between items-center">
                        <div>
                          <span className="text-2xl font-bold text-primary-600">
                            RWF {driver.price_per_hour.toLocaleString()}
                          </span>
                          <span className="text-sm text-gray-600">/hr</span>
                        </div>
                        <Link
                          href={`/drivers/${driver.id}`}
                          className="bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700 transition duration-200 text-sm font-medium"
                        >
                          View Profile
                        </Link>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <UserRound size={64} className="mx-auto text-gray-400 mb-4" />
                <h3 className="text-xl font-medium text-gray-900 mb-2">No drivers found</h3>
                <p className="text-gray-600 mb-6">
                  Try adjusting your search criteria or browse all available drivers.
                </p>
                <button
                  onClick={clearFilters}
                  className="bg-primary-600 text-white px-6 py-2 rounded-md hover:bg-primary-700"
                >
                  Clear Filters
                </button>
              </div>
            )}
          </div>
        </section>

        {/* Footer */}
        <footer className="bg-gray-900 text-white py-12">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
              <div>
                <div className="flex items-center space-x-2 mb-4">
                  <Car className="h-8 w-8 text-primary-400" />
                  <span className="text-xl font-bold">Park & Rent</span>
                </div>
                <p className="text-gray-400">
                  The trusted peer-to-peer car rental platform connecting car owners with renters.
                </p>
              </div>
              
              <div>
                <h4 className="text-lg font-semibold mb-4">Quick Links</h4>
                <ul className="space-y-2">
                  <li><Link href="/cars" className="text-gray-400 hover:text-white">Browse Cars</Link></li>
                  <li><Link href="/drivers" className="text-gray-400 hover:text-white">Find Drivers</Link></li>
                  <li><Link href="/register" className="text-gray-400 hover:text-white">List Your Car</Link></li>
                </ul>
              </div>
              
              <div>
                <h4 className="text-lg font-semibold mb-4">Support</h4>
                <ul className="space-y-2">
                  <li><a href="#" className="text-gray-400 hover:text-white">Help Center</a></li>
                  <li><a href="#" className="text-gray-400 hover:text-white">Safety</a></li>
                  <li><a href="#" className="text-gray-400 hover:text-white">Contact Us</a></li>
                </ul>
              </div>
              
              <div>
                <h4 className="text-lg font-semibold mb-4">Contact</h4>
                <p className="text-gray-400">
                  Email: <EMAIL><br />
                  Phone: 0788613669
                </p>
              </div>
            </div>
            
            <div className="border-t border-gray-800 mt-8 pt-8 text-center">
              <p className="text-gray-400">
                © 2025 Park & Rent. All rights reserved.
              </p>
            </div>
          </div>
        </footer>
      </div>
    </>
  );
}
