import{r as y,a as jr}from"./vendor-DbAb9B2p.js";import{u as de,L as O,a as Fs,R as vr,b as W,N as ht,B as Nr}from"./router-CmPn04IO.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))r(i);new MutationObserver(i=>{for(const n of i)if(n.type==="childList")for(const l of n.addedNodes)l.tagName==="LINK"&&l.rel==="modulepreload"&&r(l)}).observe(document,{childList:!0,subtree:!0});function a(i){const n={};return i.integrity&&(n.integrity=i.integrity),i.referrerPolicy&&(n.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?n.credentials="include":i.crossOrigin==="anonymous"?n.credentials="omit":n.credentials="same-origin",n}function r(i){if(i.ep)return;i.ep=!0;const n=a(i);fetch(i.href,n)}})();var pt={exports:{}},ns={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var wr=y,kr=Symbol.for("react.element"),Cr=Symbol.for("react.fragment"),Sr=Object.prototype.hasOwnProperty,Ar=wr.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Pr={key:!0,ref:!0,__self:!0,__source:!0};function ft(s,t,a){var r,i={},n=null,l=null;a!==void 0&&(n=""+a),t.key!==void 0&&(n=""+t.key),t.ref!==void 0&&(l=t.ref);for(r in t)Sr.call(t,r)&&!Pr.hasOwnProperty(r)&&(i[r]=t[r]);if(s&&s.defaultProps)for(r in t=s.defaultProps,t)i[r]===void 0&&(i[r]=t[r]);return{$$typeof:kr,type:s,key:n,ref:l,props:i,_owner:Ar.current}}ns.Fragment=Cr;ns.jsx=ft;ns.jsxs=ft;pt.exports=ns;var e=pt.exports,gt,Ws=jr;gt=Ws.createRoot,Ws.hydrateRoot;function yt(s,t){return function(){return s.apply(t,arguments)}}const{toString:Er}=Object.prototype,{getPrototypeOf:Ls}=Object,{iterator:ls,toStringTag:bt}=Symbol,os=(s=>t=>{const a=Er.call(t);return s[a]||(s[a]=a.slice(8,-1).toLowerCase())})(Object.create(null)),pe=s=>(s=s.toLowerCase(),t=>os(t)===s),cs=s=>t=>typeof t===s,{isArray:Le}=Array,qe=cs("undefined");function Rr(s){return s!==null&&!qe(s)&&s.constructor!==null&&!qe(s.constructor)&&se(s.constructor.isBuffer)&&s.constructor.isBuffer(s)}const jt=pe("ArrayBuffer");function Dr(s){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(s):t=s&&s.buffer&&jt(s.buffer),t}const Tr=cs("string"),se=cs("function"),vt=cs("number"),ds=s=>s!==null&&typeof s=="object",Ir=s=>s===!0||s===!1,Qe=s=>{if(os(s)!=="object")return!1;const t=Ls(s);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(bt in s)&&!(ls in s)},Fr=pe("Date"),Lr=pe("File"),_r=pe("Blob"),zr=pe("FileList"),Or=s=>ds(s)&&se(s.pipe),Ur=s=>{let t;return s&&(typeof FormData=="function"&&s instanceof FormData||se(s.append)&&((t=os(s))==="formdata"||t==="object"&&se(s.toString)&&s.toString()==="[object FormData]"))},Mr=pe("URLSearchParams"),[Br,Hr,qr,$r]=["ReadableStream","Request","Response","Headers"].map(pe),Vr=s=>s.trim?s.trim():s.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function Ve(s,t,{allOwnKeys:a=!1}={}){if(s===null||typeof s>"u")return;let r,i;if(typeof s!="object"&&(s=[s]),Le(s))for(r=0,i=s.length;r<i;r++)t.call(null,s[r],r,s);else{const n=a?Object.getOwnPropertyNames(s):Object.keys(s),l=n.length;let u;for(r=0;r<l;r++)u=n[r],t.call(null,s[u],u,s)}}function Nt(s,t){t=t.toLowerCase();const a=Object.keys(s);let r=a.length,i;for(;r-- >0;)if(i=a[r],t===i.toLowerCase())return i;return null}const ke=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,wt=s=>!qe(s)&&s!==ke;function As(){const{caseless:s}=wt(this)&&this||{},t={},a=(r,i)=>{const n=s&&Nt(t,i)||i;Qe(t[n])&&Qe(r)?t[n]=As(t[n],r):Qe(r)?t[n]=As({},r):Le(r)?t[n]=r.slice():t[n]=r};for(let r=0,i=arguments.length;r<i;r++)arguments[r]&&Ve(arguments[r],a);return t}const Gr=(s,t,a,{allOwnKeys:r}={})=>(Ve(t,(i,n)=>{a&&se(i)?s[n]=yt(i,a):s[n]=i},{allOwnKeys:r}),s),Yr=s=>(s.charCodeAt(0)===65279&&(s=s.slice(1)),s),Wr=(s,t,a,r)=>{s.prototype=Object.create(t.prototype,r),s.prototype.constructor=s,Object.defineProperty(s,"super",{value:t.prototype}),a&&Object.assign(s.prototype,a)},Jr=(s,t,a,r)=>{let i,n,l;const u={};if(t=t||{},s==null)return t;do{for(i=Object.getOwnPropertyNames(s),n=i.length;n-- >0;)l=i[n],(!r||r(l,s,t))&&!u[l]&&(t[l]=s[l],u[l]=!0);s=a!==!1&&Ls(s)}while(s&&(!a||a(s,t))&&s!==Object.prototype);return t},Kr=(s,t,a)=>{s=String(s),(a===void 0||a>s.length)&&(a=s.length),a-=t.length;const r=s.indexOf(t,a);return r!==-1&&r===a},Zr=s=>{if(!s)return null;if(Le(s))return s;let t=s.length;if(!vt(t))return null;const a=new Array(t);for(;t-- >0;)a[t]=s[t];return a},Xr=(s=>t=>s&&t instanceof s)(typeof Uint8Array<"u"&&Ls(Uint8Array)),Qr=(s,t)=>{const r=(s&&s[ls]).call(s);let i;for(;(i=r.next())&&!i.done;){const n=i.value;t.call(s,n[0],n[1])}},ea=(s,t)=>{let a;const r=[];for(;(a=s.exec(t))!==null;)r.push(a);return r},sa=pe("HTMLFormElement"),ta=s=>s.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(a,r,i){return r.toUpperCase()+i}),Js=(({hasOwnProperty:s})=>(t,a)=>s.call(t,a))(Object.prototype),ra=pe("RegExp"),kt=(s,t)=>{const a=Object.getOwnPropertyDescriptors(s),r={};Ve(a,(i,n)=>{let l;(l=t(i,n,s))!==!1&&(r[n]=l||i)}),Object.defineProperties(s,r)},aa=s=>{kt(s,(t,a)=>{if(se(s)&&["arguments","caller","callee"].indexOf(a)!==-1)return!1;const r=s[a];if(se(r)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+a+"'")})}})},ia=(s,t)=>{const a={},r=i=>{i.forEach(n=>{a[n]=!0})};return Le(s)?r(s):r(String(s).split(t)),a},na=()=>{},la=(s,t)=>s!=null&&Number.isFinite(s=+s)?s:t;function oa(s){return!!(s&&se(s.append)&&s[bt]==="FormData"&&s[ls])}const ca=s=>{const t=new Array(10),a=(r,i)=>{if(ds(r)){if(t.indexOf(r)>=0)return;if(!("toJSON"in r)){t[i]=r;const n=Le(r)?[]:{};return Ve(r,(l,u)=>{const d=a(l,i+1);!qe(d)&&(n[u]=d)}),t[i]=void 0,n}}return r};return a(s,0)},da=pe("AsyncFunction"),ma=s=>s&&(ds(s)||se(s))&&se(s.then)&&se(s.catch),Ct=((s,t)=>s?setImmediate:t?((a,r)=>(ke.addEventListener("message",({source:i,data:n})=>{i===ke&&n===a&&r.length&&r.shift()()},!1),i=>{r.push(i),ke.postMessage(a,"*")}))(`axios@${Math.random()}`,[]):a=>setTimeout(a))(typeof setImmediate=="function",se(ke.postMessage)),xa=typeof queueMicrotask<"u"?queueMicrotask.bind(ke):typeof process<"u"&&process.nextTick||Ct,ua=s=>s!=null&&se(s[ls]),j={isArray:Le,isArrayBuffer:jt,isBuffer:Rr,isFormData:Ur,isArrayBufferView:Dr,isString:Tr,isNumber:vt,isBoolean:Ir,isObject:ds,isPlainObject:Qe,isReadableStream:Br,isRequest:Hr,isResponse:qr,isHeaders:$r,isUndefined:qe,isDate:Fr,isFile:Lr,isBlob:_r,isRegExp:ra,isFunction:se,isStream:Or,isURLSearchParams:Mr,isTypedArray:Xr,isFileList:zr,forEach:Ve,merge:As,extend:Gr,trim:Vr,stripBOM:Yr,inherits:Wr,toFlatObject:Jr,kindOf:os,kindOfTest:pe,endsWith:Kr,toArray:Zr,forEachEntry:Qr,matchAll:ea,isHTMLForm:sa,hasOwnProperty:Js,hasOwnProp:Js,reduceDescriptors:kt,freezeMethods:aa,toObjectSet:ia,toCamelCase:ta,noop:na,toFiniteNumber:la,findKey:Nt,global:ke,isContextDefined:wt,isSpecCompliantForm:oa,toJSONObject:ca,isAsyncFn:da,isThenable:ma,setImmediate:Ct,asap:xa,isIterable:ua};function _(s,t,a,r,i){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=s,this.name="AxiosError",t&&(this.code=t),a&&(this.config=a),r&&(this.request=r),i&&(this.response=i,this.status=i.status?i.status:null)}j.inherits(_,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:j.toJSONObject(this.config),code:this.code,status:this.status}}});const St=_.prototype,At={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(s=>{At[s]={value:s}});Object.defineProperties(_,At);Object.defineProperty(St,"isAxiosError",{value:!0});_.from=(s,t,a,r,i,n)=>{const l=Object.create(St);return j.toFlatObject(s,l,function(d){return d!==Error.prototype},u=>u!=="isAxiosError"),_.call(l,s.message,t,a,r,i),l.cause=s,l.name=s.name,n&&Object.assign(l,n),l};const ha=null;function Ps(s){return j.isPlainObject(s)||j.isArray(s)}function Pt(s){return j.endsWith(s,"[]")?s.slice(0,-2):s}function Ks(s,t,a){return s?s.concat(t).map(function(i,n){return i=Pt(i),!a&&n?"["+i+"]":i}).join(a?".":""):t}function pa(s){return j.isArray(s)&&!s.some(Ps)}const fa=j.toFlatObject(j,{},null,function(t){return/^is[A-Z]/.test(t)});function ms(s,t,a){if(!j.isObject(s))throw new TypeError("target must be an object");t=t||new FormData,a=j.toFlatObject(a,{metaTokens:!0,dots:!1,indexes:!1},!1,function(p,v){return!j.isUndefined(v[p])});const r=a.metaTokens,i=a.visitor||h,n=a.dots,l=a.indexes,d=(a.Blob||typeof Blob<"u"&&Blob)&&j.isSpecCompliantForm(t);if(!j.isFunction(i))throw new TypeError("visitor must be a function");function m(c){if(c===null)return"";if(j.isDate(c))return c.toISOString();if(!d&&j.isBlob(c))throw new _("Blob is not supported. Use a Buffer instead.");return j.isArrayBuffer(c)||j.isTypedArray(c)?d&&typeof Blob=="function"?new Blob([c]):Buffer.from(c):c}function h(c,p,v){let E=c;if(c&&!v&&typeof c=="object"){if(j.endsWith(p,"{}"))p=r?p:p.slice(0,-2),c=JSON.stringify(c);else if(j.isArray(c)&&pa(c)||(j.isFileList(c)||j.endsWith(p,"[]"))&&(E=j.toArray(c)))return p=Pt(p),E.forEach(function(I,B){!(j.isUndefined(I)||I===null)&&t.append(l===!0?Ks([p],B,n):l===null?p:p+"[]",m(I))}),!1}return Ps(c)?!0:(t.append(Ks(v,p,n),m(c)),!1)}const x=[],w=Object.assign(fa,{defaultVisitor:h,convertValue:m,isVisitable:Ps});function b(c,p){if(!j.isUndefined(c)){if(x.indexOf(c)!==-1)throw Error("Circular reference detected in "+p.join("."));x.push(c),j.forEach(c,function(E,R){(!(j.isUndefined(E)||E===null)&&i.call(t,E,j.isString(R)?R.trim():R,p,w))===!0&&b(E,p?p.concat(R):[R])}),x.pop()}}if(!j.isObject(s))throw new TypeError("data must be an object");return b(s),t}function Zs(s){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(s).replace(/[!'()~]|%20|%00/g,function(r){return t[r]})}function _s(s,t){this._pairs=[],s&&ms(s,this,t)}const Et=_s.prototype;Et.append=function(t,a){this._pairs.push([t,a])};Et.toString=function(t){const a=t?function(r){return t.call(this,r,Zs)}:Zs;return this._pairs.map(function(i){return a(i[0])+"="+a(i[1])},"").join("&")};function ga(s){return encodeURIComponent(s).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Rt(s,t,a){if(!t)return s;const r=a&&a.encode||ga;j.isFunction(a)&&(a={serialize:a});const i=a&&a.serialize;let n;if(i?n=i(t,a):n=j.isURLSearchParams(t)?t.toString():new _s(t,a).toString(r),n){const l=s.indexOf("#");l!==-1&&(s=s.slice(0,l)),s+=(s.indexOf("?")===-1?"?":"&")+n}return s}class Xs{constructor(){this.handlers=[]}use(t,a,r){return this.handlers.push({fulfilled:t,rejected:a,synchronous:r?r.synchronous:!1,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){j.forEach(this.handlers,function(r){r!==null&&t(r)})}}const Dt={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},ya=typeof URLSearchParams<"u"?URLSearchParams:_s,ba=typeof FormData<"u"?FormData:null,ja=typeof Blob<"u"?Blob:null,va={isBrowser:!0,classes:{URLSearchParams:ya,FormData:ba,Blob:ja},protocols:["http","https","file","blob","url","data"]},zs=typeof window<"u"&&typeof document<"u",Es=typeof navigator=="object"&&navigator||void 0,Na=zs&&(!Es||["ReactNative","NativeScript","NS"].indexOf(Es.product)<0),wa=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",ka=zs&&window.location.href||"http://localhost",Ca=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:zs,hasStandardBrowserEnv:Na,hasStandardBrowserWebWorkerEnv:wa,navigator:Es,origin:ka},Symbol.toStringTag,{value:"Module"})),Q={...Ca,...va};function Sa(s,t){return ms(s,new Q.classes.URLSearchParams,Object.assign({visitor:function(a,r,i,n){return Q.isNode&&j.isBuffer(a)?(this.append(r,a.toString("base64")),!1):n.defaultVisitor.apply(this,arguments)}},t))}function Aa(s){return j.matchAll(/\w+|\[(\w*)]/g,s).map(t=>t[0]==="[]"?"":t[1]||t[0])}function Pa(s){const t={},a=Object.keys(s);let r;const i=a.length;let n;for(r=0;r<i;r++)n=a[r],t[n]=s[n];return t}function Tt(s){function t(a,r,i,n){let l=a[n++];if(l==="__proto__")return!0;const u=Number.isFinite(+l),d=n>=a.length;return l=!l&&j.isArray(i)?i.length:l,d?(j.hasOwnProp(i,l)?i[l]=[i[l],r]:i[l]=r,!u):((!i[l]||!j.isObject(i[l]))&&(i[l]=[]),t(a,r,i[l],n)&&j.isArray(i[l])&&(i[l]=Pa(i[l])),!u)}if(j.isFormData(s)&&j.isFunction(s.entries)){const a={};return j.forEachEntry(s,(r,i)=>{t(Aa(r),i,a,0)}),a}return null}function Ea(s,t,a){if(j.isString(s))try{return(t||JSON.parse)(s),j.trim(s)}catch(r){if(r.name!=="SyntaxError")throw r}return(0,JSON.stringify)(s)}const Ge={transitional:Dt,adapter:["xhr","http","fetch"],transformRequest:[function(t,a){const r=a.getContentType()||"",i=r.indexOf("application/json")>-1,n=j.isObject(t);if(n&&j.isHTMLForm(t)&&(t=new FormData(t)),j.isFormData(t))return i?JSON.stringify(Tt(t)):t;if(j.isArrayBuffer(t)||j.isBuffer(t)||j.isStream(t)||j.isFile(t)||j.isBlob(t)||j.isReadableStream(t))return t;if(j.isArrayBufferView(t))return t.buffer;if(j.isURLSearchParams(t))return a.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let u;if(n){if(r.indexOf("application/x-www-form-urlencoded")>-1)return Sa(t,this.formSerializer).toString();if((u=j.isFileList(t))||r.indexOf("multipart/form-data")>-1){const d=this.env&&this.env.FormData;return ms(u?{"files[]":t}:t,d&&new d,this.formSerializer)}}return n||i?(a.setContentType("application/json",!1),Ea(t)):t}],transformResponse:[function(t){const a=this.transitional||Ge.transitional,r=a&&a.forcedJSONParsing,i=this.responseType==="json";if(j.isResponse(t)||j.isReadableStream(t))return t;if(t&&j.isString(t)&&(r&&!this.responseType||i)){const l=!(a&&a.silentJSONParsing)&&i;try{return JSON.parse(t)}catch(u){if(l)throw u.name==="SyntaxError"?_.from(u,_.ERR_BAD_RESPONSE,this,null,this.response):u}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Q.classes.FormData,Blob:Q.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};j.forEach(["delete","get","head","post","put","patch"],s=>{Ge.headers[s]={}});const Ra=j.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Da=s=>{const t={};let a,r,i;return s&&s.split(`
`).forEach(function(l){i=l.indexOf(":"),a=l.substring(0,i).trim().toLowerCase(),r=l.substring(i+1).trim(),!(!a||t[a]&&Ra[a])&&(a==="set-cookie"?t[a]?t[a].push(r):t[a]=[r]:t[a]=t[a]?t[a]+", "+r:r)}),t},Qs=Symbol("internals");function Me(s){return s&&String(s).trim().toLowerCase()}function es(s){return s===!1||s==null?s:j.isArray(s)?s.map(es):String(s)}function Ta(s){const t=Object.create(null),a=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=a.exec(s);)t[r[1]]=r[2];return t}const Ia=s=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(s.trim());function ks(s,t,a,r,i){if(j.isFunction(r))return r.call(this,t,a);if(i&&(t=a),!!j.isString(t)){if(j.isString(r))return t.indexOf(r)!==-1;if(j.isRegExp(r))return r.test(t)}}function Fa(s){return s.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,a,r)=>a.toUpperCase()+r)}function La(s,t){const a=j.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(s,r+a,{value:function(i,n,l){return this[r].call(this,t,i,n,l)},configurable:!0})})}class te{constructor(t){t&&this.set(t)}set(t,a,r){const i=this;function n(u,d,m){const h=Me(d);if(!h)throw new Error("header name must be a non-empty string");const x=j.findKey(i,h);(!x||i[x]===void 0||m===!0||m===void 0&&i[x]!==!1)&&(i[x||d]=es(u))}const l=(u,d)=>j.forEach(u,(m,h)=>n(m,h,d));if(j.isPlainObject(t)||t instanceof this.constructor)l(t,a);else if(j.isString(t)&&(t=t.trim())&&!Ia(t))l(Da(t),a);else if(j.isObject(t)&&j.isIterable(t)){let u={},d,m;for(const h of t){if(!j.isArray(h))throw TypeError("Object iterator must return a key-value pair");u[m=h[0]]=(d=u[m])?j.isArray(d)?[...d,h[1]]:[d,h[1]]:h[1]}l(u,a)}else t!=null&&n(a,t,r);return this}get(t,a){if(t=Me(t),t){const r=j.findKey(this,t);if(r){const i=this[r];if(!a)return i;if(a===!0)return Ta(i);if(j.isFunction(a))return a.call(this,i,r);if(j.isRegExp(a))return a.exec(i);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,a){if(t=Me(t),t){const r=j.findKey(this,t);return!!(r&&this[r]!==void 0&&(!a||ks(this,this[r],r,a)))}return!1}delete(t,a){const r=this;let i=!1;function n(l){if(l=Me(l),l){const u=j.findKey(r,l);u&&(!a||ks(r,r[u],u,a))&&(delete r[u],i=!0)}}return j.isArray(t)?t.forEach(n):n(t),i}clear(t){const a=Object.keys(this);let r=a.length,i=!1;for(;r--;){const n=a[r];(!t||ks(this,this[n],n,t,!0))&&(delete this[n],i=!0)}return i}normalize(t){const a=this,r={};return j.forEach(this,(i,n)=>{const l=j.findKey(r,n);if(l){a[l]=es(i),delete a[n];return}const u=t?Fa(n):String(n).trim();u!==n&&delete a[n],a[u]=es(i),r[u]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const a=Object.create(null);return j.forEach(this,(r,i)=>{r!=null&&r!==!1&&(a[i]=t&&j.isArray(r)?r.join(", "):r)}),a}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,a])=>t+": "+a).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...a){const r=new this(t);return a.forEach(i=>r.set(i)),r}static accessor(t){const r=(this[Qs]=this[Qs]={accessors:{}}).accessors,i=this.prototype;function n(l){const u=Me(l);r[u]||(La(i,l),r[u]=!0)}return j.isArray(t)?t.forEach(n):n(t),this}}te.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);j.reduceDescriptors(te.prototype,({value:s},t)=>{let a=t[0].toUpperCase()+t.slice(1);return{get:()=>s,set(r){this[a]=r}}});j.freezeMethods(te);function Cs(s,t){const a=this||Ge,r=t||a,i=te.from(r.headers);let n=r.data;return j.forEach(s,function(u){n=u.call(a,n,i.normalize(),t?t.status:void 0)}),i.normalize(),n}function It(s){return!!(s&&s.__CANCEL__)}function _e(s,t,a){_.call(this,s??"canceled",_.ERR_CANCELED,t,a),this.name="CanceledError"}j.inherits(_e,_,{__CANCEL__:!0});function Ft(s,t,a){const r=a.config.validateStatus;!a.status||!r||r(a.status)?s(a):t(new _("Request failed with status code "+a.status,[_.ERR_BAD_REQUEST,_.ERR_BAD_RESPONSE][Math.floor(a.status/100)-4],a.config,a.request,a))}function _a(s){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(s);return t&&t[1]||""}function za(s,t){s=s||10;const a=new Array(s),r=new Array(s);let i=0,n=0,l;return t=t!==void 0?t:1e3,function(d){const m=Date.now(),h=r[n];l||(l=m),a[i]=d,r[i]=m;let x=n,w=0;for(;x!==i;)w+=a[x++],x=x%s;if(i=(i+1)%s,i===n&&(n=(n+1)%s),m-l<t)return;const b=h&&m-h;return b?Math.round(w*1e3/b):void 0}}function Oa(s,t){let a=0,r=1e3/t,i,n;const l=(m,h=Date.now())=>{a=h,i=null,n&&(clearTimeout(n),n=null),s.apply(null,m)};return[(...m)=>{const h=Date.now(),x=h-a;x>=r?l(m,h):(i=m,n||(n=setTimeout(()=>{n=null,l(i)},r-x)))},()=>i&&l(i)]}const as=(s,t,a=3)=>{let r=0;const i=za(50,250);return Oa(n=>{const l=n.loaded,u=n.lengthComputable?n.total:void 0,d=l-r,m=i(d),h=l<=u;r=l;const x={loaded:l,total:u,progress:u?l/u:void 0,bytes:d,rate:m||void 0,estimated:m&&u&&h?(u-l)/m:void 0,event:n,lengthComputable:u!=null,[t?"download":"upload"]:!0};s(x)},a)},et=(s,t)=>{const a=s!=null;return[r=>t[0]({lengthComputable:a,total:s,loaded:r}),t[1]]},st=s=>(...t)=>j.asap(()=>s(...t)),Ua=Q.hasStandardBrowserEnv?((s,t)=>a=>(a=new URL(a,Q.origin),s.protocol===a.protocol&&s.host===a.host&&(t||s.port===a.port)))(new URL(Q.origin),Q.navigator&&/(msie|trident)/i.test(Q.navigator.userAgent)):()=>!0,Ma=Q.hasStandardBrowserEnv?{write(s,t,a,r,i,n){const l=[s+"="+encodeURIComponent(t)];j.isNumber(a)&&l.push("expires="+new Date(a).toGMTString()),j.isString(r)&&l.push("path="+r),j.isString(i)&&l.push("domain="+i),n===!0&&l.push("secure"),document.cookie=l.join("; ")},read(s){const t=document.cookie.match(new RegExp("(^|;\\s*)("+s+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(s){this.write(s,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function Ba(s){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(s)}function Ha(s,t){return t?s.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):s}function Lt(s,t,a){let r=!Ba(t);return s&&(r||a==!1)?Ha(s,t):t}const tt=s=>s instanceof te?{...s}:s;function Ae(s,t){t=t||{};const a={};function r(m,h,x,w){return j.isPlainObject(m)&&j.isPlainObject(h)?j.merge.call({caseless:w},m,h):j.isPlainObject(h)?j.merge({},h):j.isArray(h)?h.slice():h}function i(m,h,x,w){if(j.isUndefined(h)){if(!j.isUndefined(m))return r(void 0,m,x,w)}else return r(m,h,x,w)}function n(m,h){if(!j.isUndefined(h))return r(void 0,h)}function l(m,h){if(j.isUndefined(h)){if(!j.isUndefined(m))return r(void 0,m)}else return r(void 0,h)}function u(m,h,x){if(x in t)return r(m,h);if(x in s)return r(void 0,m)}const d={url:n,method:n,data:n,baseURL:l,transformRequest:l,transformResponse:l,paramsSerializer:l,timeout:l,timeoutMessage:l,withCredentials:l,withXSRFToken:l,adapter:l,responseType:l,xsrfCookieName:l,xsrfHeaderName:l,onUploadProgress:l,onDownloadProgress:l,decompress:l,maxContentLength:l,maxBodyLength:l,beforeRedirect:l,transport:l,httpAgent:l,httpsAgent:l,cancelToken:l,socketPath:l,responseEncoding:l,validateStatus:u,headers:(m,h,x)=>i(tt(m),tt(h),x,!0)};return j.forEach(Object.keys(Object.assign({},s,t)),function(h){const x=d[h]||i,w=x(s[h],t[h],h);j.isUndefined(w)&&x!==u||(a[h]=w)}),a}const _t=s=>{const t=Ae({},s);let{data:a,withXSRFToken:r,xsrfHeaderName:i,xsrfCookieName:n,headers:l,auth:u}=t;t.headers=l=te.from(l),t.url=Rt(Lt(t.baseURL,t.url,t.allowAbsoluteUrls),s.params,s.paramsSerializer),u&&l.set("Authorization","Basic "+btoa((u.username||"")+":"+(u.password?unescape(encodeURIComponent(u.password)):"")));let d;if(j.isFormData(a)){if(Q.hasStandardBrowserEnv||Q.hasStandardBrowserWebWorkerEnv)l.setContentType(void 0);else if((d=l.getContentType())!==!1){const[m,...h]=d?d.split(";").map(x=>x.trim()).filter(Boolean):[];l.setContentType([m||"multipart/form-data",...h].join("; "))}}if(Q.hasStandardBrowserEnv&&(r&&j.isFunction(r)&&(r=r(t)),r||r!==!1&&Ua(t.url))){const m=i&&n&&Ma.read(n);m&&l.set(i,m)}return t},qa=typeof XMLHttpRequest<"u",$a=qa&&function(s){return new Promise(function(a,r){const i=_t(s);let n=i.data;const l=te.from(i.headers).normalize();let{responseType:u,onUploadProgress:d,onDownloadProgress:m}=i,h,x,w,b,c;function p(){b&&b(),c&&c(),i.cancelToken&&i.cancelToken.unsubscribe(h),i.signal&&i.signal.removeEventListener("abort",h)}let v=new XMLHttpRequest;v.open(i.method.toUpperCase(),i.url,!0),v.timeout=i.timeout;function E(){if(!v)return;const I=te.from("getAllResponseHeaders"in v&&v.getAllResponseHeaders()),A={data:!u||u==="text"||u==="json"?v.responseText:v.response,status:v.status,statusText:v.statusText,headers:I,config:s,request:v};Ft(function(f){a(f),p()},function(f){r(f),p()},A),v=null}"onloadend"in v?v.onloadend=E:v.onreadystatechange=function(){!v||v.readyState!==4||v.status===0&&!(v.responseURL&&v.responseURL.indexOf("file:")===0)||setTimeout(E)},v.onabort=function(){v&&(r(new _("Request aborted",_.ECONNABORTED,s,v)),v=null)},v.onerror=function(){r(new _("Network Error",_.ERR_NETWORK,s,v)),v=null},v.ontimeout=function(){let B=i.timeout?"timeout of "+i.timeout+"ms exceeded":"timeout exceeded";const A=i.transitional||Dt;i.timeoutErrorMessage&&(B=i.timeoutErrorMessage),r(new _(B,A.clarifyTimeoutError?_.ETIMEDOUT:_.ECONNABORTED,s,v)),v=null},n===void 0&&l.setContentType(null),"setRequestHeader"in v&&j.forEach(l.toJSON(),function(B,A){v.setRequestHeader(A,B)}),j.isUndefined(i.withCredentials)||(v.withCredentials=!!i.withCredentials),u&&u!=="json"&&(v.responseType=i.responseType),m&&([w,c]=as(m,!0),v.addEventListener("progress",w)),d&&v.upload&&([x,b]=as(d),v.upload.addEventListener("progress",x),v.upload.addEventListener("loadend",b)),(i.cancelToken||i.signal)&&(h=I=>{v&&(r(!I||I.type?new _e(null,s,v):I),v.abort(),v=null)},i.cancelToken&&i.cancelToken.subscribe(h),i.signal&&(i.signal.aborted?h():i.signal.addEventListener("abort",h)));const R=_a(i.url);if(R&&Q.protocols.indexOf(R)===-1){r(new _("Unsupported protocol "+R+":",_.ERR_BAD_REQUEST,s));return}v.send(n||null)})},Va=(s,t)=>{const{length:a}=s=s?s.filter(Boolean):[];if(t||a){let r=new AbortController,i;const n=function(m){if(!i){i=!0,u();const h=m instanceof Error?m:this.reason;r.abort(h instanceof _?h:new _e(h instanceof Error?h.message:h))}};let l=t&&setTimeout(()=>{l=null,n(new _(`timeout ${t} of ms exceeded`,_.ETIMEDOUT))},t);const u=()=>{s&&(l&&clearTimeout(l),l=null,s.forEach(m=>{m.unsubscribe?m.unsubscribe(n):m.removeEventListener("abort",n)}),s=null)};s.forEach(m=>m.addEventListener("abort",n));const{signal:d}=r;return d.unsubscribe=()=>j.asap(u),d}},Ga=function*(s,t){let a=s.byteLength;if(a<t){yield s;return}let r=0,i;for(;r<a;)i=r+t,yield s.slice(r,i),r=i},Ya=async function*(s,t){for await(const a of Wa(s))yield*Ga(a,t)},Wa=async function*(s){if(s[Symbol.asyncIterator]){yield*s;return}const t=s.getReader();try{for(;;){const{done:a,value:r}=await t.read();if(a)break;yield r}}finally{await t.cancel()}},rt=(s,t,a,r)=>{const i=Ya(s,t);let n=0,l,u=d=>{l||(l=!0,r&&r(d))};return new ReadableStream({async pull(d){try{const{done:m,value:h}=await i.next();if(m){u(),d.close();return}let x=h.byteLength;if(a){let w=n+=x;a(w)}d.enqueue(new Uint8Array(h))}catch(m){throw u(m),m}},cancel(d){return u(d),i.return()}},{highWaterMark:2})},xs=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",zt=xs&&typeof ReadableStream=="function",Ja=xs&&(typeof TextEncoder=="function"?(s=>t=>s.encode(t))(new TextEncoder):async s=>new Uint8Array(await new Response(s).arrayBuffer())),Ot=(s,...t)=>{try{return!!s(...t)}catch{return!1}},Ka=zt&&Ot(()=>{let s=!1;const t=new Request(Q.origin,{body:new ReadableStream,method:"POST",get duplex(){return s=!0,"half"}}).headers.has("Content-Type");return s&&!t}),at=64*1024,Rs=zt&&Ot(()=>j.isReadableStream(new Response("").body)),is={stream:Rs&&(s=>s.body)};xs&&(s=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!is[t]&&(is[t]=j.isFunction(s[t])?a=>a[t]():(a,r)=>{throw new _(`Response type '${t}' is not supported`,_.ERR_NOT_SUPPORT,r)})})})(new Response);const Za=async s=>{if(s==null)return 0;if(j.isBlob(s))return s.size;if(j.isSpecCompliantForm(s))return(await new Request(Q.origin,{method:"POST",body:s}).arrayBuffer()).byteLength;if(j.isArrayBufferView(s)||j.isArrayBuffer(s))return s.byteLength;if(j.isURLSearchParams(s)&&(s=s+""),j.isString(s))return(await Ja(s)).byteLength},Xa=async(s,t)=>{const a=j.toFiniteNumber(s.getContentLength());return a??Za(t)},Qa=xs&&(async s=>{let{url:t,method:a,data:r,signal:i,cancelToken:n,timeout:l,onDownloadProgress:u,onUploadProgress:d,responseType:m,headers:h,withCredentials:x="same-origin",fetchOptions:w}=_t(s);m=m?(m+"").toLowerCase():"text";let b=Va([i,n&&n.toAbortSignal()],l),c;const p=b&&b.unsubscribe&&(()=>{b.unsubscribe()});let v;try{if(d&&Ka&&a!=="get"&&a!=="head"&&(v=await Xa(h,r))!==0){let A=new Request(t,{method:"POST",body:r,duplex:"half"}),D;if(j.isFormData(r)&&(D=A.headers.get("content-type"))&&h.setContentType(D),A.body){const[f,C]=et(v,as(st(d)));r=rt(A.body,at,f,C)}}j.isString(x)||(x=x?"include":"omit");const E="credentials"in Request.prototype;c=new Request(t,{...w,signal:b,method:a.toUpperCase(),headers:h.normalize().toJSON(),body:r,duplex:"half",credentials:E?x:void 0});let R=await fetch(c);const I=Rs&&(m==="stream"||m==="response");if(Rs&&(u||I&&p)){const A={};["status","statusText","headers"].forEach(g=>{A[g]=R[g]});const D=j.toFiniteNumber(R.headers.get("content-length")),[f,C]=u&&et(D,as(st(u),!0))||[];R=new Response(rt(R.body,at,f,()=>{C&&C(),p&&p()}),A)}m=m||"text";let B=await is[j.findKey(is,m)||"text"](R,s);return!I&&p&&p(),await new Promise((A,D)=>{Ft(A,D,{data:B,headers:te.from(R.headers),status:R.status,statusText:R.statusText,config:s,request:c})})}catch(E){throw p&&p(),E&&E.name==="TypeError"&&/Load failed|fetch/i.test(E.message)?Object.assign(new _("Network Error",_.ERR_NETWORK,s,c),{cause:E.cause||E}):_.from(E,E&&E.code,s,c)}}),Ds={http:ha,xhr:$a,fetch:Qa};j.forEach(Ds,(s,t)=>{if(s){try{Object.defineProperty(s,"name",{value:t})}catch{}Object.defineProperty(s,"adapterName",{value:t})}});const it=s=>`- ${s}`,ei=s=>j.isFunction(s)||s===null||s===!1,Ut={getAdapter:s=>{s=j.isArray(s)?s:[s];const{length:t}=s;let a,r;const i={};for(let n=0;n<t;n++){a=s[n];let l;if(r=a,!ei(a)&&(r=Ds[(l=String(a)).toLowerCase()],r===void 0))throw new _(`Unknown adapter '${l}'`);if(r)break;i[l||"#"+n]=r}if(!r){const n=Object.entries(i).map(([u,d])=>`adapter ${u} `+(d===!1?"is not supported by the environment":"is not available in the build"));let l=t?n.length>1?`since :
`+n.map(it).join(`
`):" "+it(n[0]):"as no adapter specified";throw new _("There is no suitable adapter to dispatch the request "+l,"ERR_NOT_SUPPORT")}return r},adapters:Ds};function Ss(s){if(s.cancelToken&&s.cancelToken.throwIfRequested(),s.signal&&s.signal.aborted)throw new _e(null,s)}function nt(s){return Ss(s),s.headers=te.from(s.headers),s.data=Cs.call(s,s.transformRequest),["post","put","patch"].indexOf(s.method)!==-1&&s.headers.setContentType("application/x-www-form-urlencoded",!1),Ut.getAdapter(s.adapter||Ge.adapter)(s).then(function(r){return Ss(s),r.data=Cs.call(s,s.transformResponse,r),r.headers=te.from(r.headers),r},function(r){return It(r)||(Ss(s),r&&r.response&&(r.response.data=Cs.call(s,s.transformResponse,r.response),r.response.headers=te.from(r.response.headers))),Promise.reject(r)})}const Mt="1.9.0",us={};["object","boolean","number","function","string","symbol"].forEach((s,t)=>{us[s]=function(r){return typeof r===s||"a"+(t<1?"n ":" ")+s}});const lt={};us.transitional=function(t,a,r){function i(n,l){return"[Axios v"+Mt+"] Transitional option '"+n+"'"+l+(r?". "+r:"")}return(n,l,u)=>{if(t===!1)throw new _(i(l," has been removed"+(a?" in "+a:"")),_.ERR_DEPRECATED);return a&&!lt[l]&&(lt[l]=!0,console.warn(i(l," has been deprecated since v"+a+" and will be removed in the near future"))),t?t(n,l,u):!0}};us.spelling=function(t){return(a,r)=>(console.warn(`${r} is likely a misspelling of ${t}`),!0)};function si(s,t,a){if(typeof s!="object")throw new _("options must be an object",_.ERR_BAD_OPTION_VALUE);const r=Object.keys(s);let i=r.length;for(;i-- >0;){const n=r[i],l=t[n];if(l){const u=s[n],d=u===void 0||l(u,n,s);if(d!==!0)throw new _("option "+n+" must be "+d,_.ERR_BAD_OPTION_VALUE);continue}if(a!==!0)throw new _("Unknown option "+n,_.ERR_BAD_OPTION)}}const ss={assertOptions:si,validators:us},ge=ss.validators;class Ce{constructor(t){this.defaults=t||{},this.interceptors={request:new Xs,response:new Xs}}async request(t,a){try{return await this._request(t,a)}catch(r){if(r instanceof Error){let i={};Error.captureStackTrace?Error.captureStackTrace(i):i=new Error;const n=i.stack?i.stack.replace(/^.+\n/,""):"";try{r.stack?n&&!String(r.stack).endsWith(n.replace(/^.+\n.+\n/,""))&&(r.stack+=`
`+n):r.stack=n}catch{}}throw r}}_request(t,a){typeof t=="string"?(a=a||{},a.url=t):a=t||{},a=Ae(this.defaults,a);const{transitional:r,paramsSerializer:i,headers:n}=a;r!==void 0&&ss.assertOptions(r,{silentJSONParsing:ge.transitional(ge.boolean),forcedJSONParsing:ge.transitional(ge.boolean),clarifyTimeoutError:ge.transitional(ge.boolean)},!1),i!=null&&(j.isFunction(i)?a.paramsSerializer={serialize:i}:ss.assertOptions(i,{encode:ge.function,serialize:ge.function},!0)),a.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?a.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:a.allowAbsoluteUrls=!0),ss.assertOptions(a,{baseUrl:ge.spelling("baseURL"),withXsrfToken:ge.spelling("withXSRFToken")},!0),a.method=(a.method||this.defaults.method||"get").toLowerCase();let l=n&&j.merge(n.common,n[a.method]);n&&j.forEach(["delete","get","head","post","put","patch","common"],c=>{delete n[c]}),a.headers=te.concat(l,n);const u=[];let d=!0;this.interceptors.request.forEach(function(p){typeof p.runWhen=="function"&&p.runWhen(a)===!1||(d=d&&p.synchronous,u.unshift(p.fulfilled,p.rejected))});const m=[];this.interceptors.response.forEach(function(p){m.push(p.fulfilled,p.rejected)});let h,x=0,w;if(!d){const c=[nt.bind(this),void 0];for(c.unshift.apply(c,u),c.push.apply(c,m),w=c.length,h=Promise.resolve(a);x<w;)h=h.then(c[x++],c[x++]);return h}w=u.length;let b=a;for(x=0;x<w;){const c=u[x++],p=u[x++];try{b=c(b)}catch(v){p.call(this,v);break}}try{h=nt.call(this,b)}catch(c){return Promise.reject(c)}for(x=0,w=m.length;x<w;)h=h.then(m[x++],m[x++]);return h}getUri(t){t=Ae(this.defaults,t);const a=Lt(t.baseURL,t.url,t.allowAbsoluteUrls);return Rt(a,t.params,t.paramsSerializer)}}j.forEach(["delete","get","head","options"],function(t){Ce.prototype[t]=function(a,r){return this.request(Ae(r||{},{method:t,url:a,data:(r||{}).data}))}});j.forEach(["post","put","patch"],function(t){function a(r){return function(n,l,u){return this.request(Ae(u||{},{method:t,headers:r?{"Content-Type":"multipart/form-data"}:{},url:n,data:l}))}}Ce.prototype[t]=a(),Ce.prototype[t+"Form"]=a(!0)});class Os{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let a;this.promise=new Promise(function(n){a=n});const r=this;this.promise.then(i=>{if(!r._listeners)return;let n=r._listeners.length;for(;n-- >0;)r._listeners[n](i);r._listeners=null}),this.promise.then=i=>{let n;const l=new Promise(u=>{r.subscribe(u),n=u}).then(i);return l.cancel=function(){r.unsubscribe(n)},l},t(function(n,l,u){r.reason||(r.reason=new _e(n,l,u),a(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const a=this._listeners.indexOf(t);a!==-1&&this._listeners.splice(a,1)}toAbortSignal(){const t=new AbortController,a=r=>{t.abort(r)};return this.subscribe(a),t.signal.unsubscribe=()=>this.unsubscribe(a),t.signal}static source(){let t;return{token:new Os(function(i){t=i}),cancel:t}}}function ti(s){return function(a){return s.apply(null,a)}}function ri(s){return j.isObject(s)&&s.isAxiosError===!0}const Ts={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Ts).forEach(([s,t])=>{Ts[t]=s});function Bt(s){const t=new Ce(s),a=yt(Ce.prototype.request,t);return j.extend(a,Ce.prototype,t,{allOwnKeys:!0}),j.extend(a,t,null,{allOwnKeys:!0}),a.create=function(i){return Bt(Ae(s,i))},a}const $=Bt(Ge);$.Axios=Ce;$.CanceledError=_e;$.CancelToken=Os;$.isCancel=It;$.VERSION=Mt;$.toFormData=ms;$.AxiosError=_;$.Cancel=$.CanceledError;$.all=function(t){return Promise.all(t)};$.spread=ti;$.isAxiosError=ri;$.mergeConfig=Ae;$.AxiosHeaders=te;$.formToJSON=s=>Tt(j.isHTMLForm(s)?new FormData(s):s);$.getAdapter=Ut.getAdapter;$.HttpStatusCode=Ts;$.default=$;const ai="/api",H=$.create({baseURL:ai,timeout:1e4,headers:{"Content-Type":"application/json",Accept:"application/json"}});H.interceptors.request.use(s=>s,s=>(console.error("Request error:",s),Promise.reject(s)));H.interceptors.request.use(s=>{const t=localStorage.getItem("auth_token");return t&&(s.headers.Authorization=`Bearer ${t}`),s},s=>Promise.reject(s));H.interceptors.response.use(s=>s,s=>{var t;return console.error("API error:",s.message),((t=s.response)==null?void 0:t.status)===401&&(localStorage.removeItem("auth_token"),localStorage.removeItem("user"),window.location.href="/login"),Promise.reject(s)});const K={REGISTER:"/register",LOGIN:"/login",LOGOUT:"/logout",USER:"/user",USERS:"/users",UPDATE_PASSWORD:"/users/update-password",CARS:"/cars",MY_CARS:"/my-cars",DRIVERS:"/drivers",MY_DRIVER_PROFILE:"/my-driver-profile",BOOKINGS:"/bookings",MY_BOOKINGS:"/my-bookings",CHATS:"/chats",MY_CHATS:"/my-chats",UNREAD_COUNT:"/unread-messages-count",MESSAGES:"/messages",MARK_AS_READ:"/messages/mark-as-read",ADMIN:{DASHBOARD:"/admin/dashboard",USERS:"/admin/users",CARS:"/admin/cars",DRIVERS:"/admin/drivers",BOOKINGS:"/admin/bookings",REVENUE:"/admin/revenue"}},Ht=y.createContext(void 0),ee=()=>{const s=y.useContext(Ht);if(s===void 0)throw new Error("useAuth must be used within an AuthProvider");return s},ii=({children:s})=>{const[t,a]=y.useState(null),[r,i]=y.useState(!0),[n,l]=y.useState(null);y.useEffect(()=>{const w=localStorage.getItem("user");if(w)try{a(JSON.parse(w))}catch(b){console.error("Error parsing stored user:",b),localStorage.removeItem("user")}i(!1)},[]);const x={user:t,isAuthenticated:!!t,isLoading:r,error:n,login:async(w,b)=>{i(!0),l(null);try{const c=await H.post(K.LOGIN,{email:w,password:b});if(c.data&&c.data.user){const p={id:c.data.user.id.toString(),email:c.data.user.email,name:c.data.user.name,role:c.data.user.role,phoneNumber:c.data.user.phone_number,isPhoneVerified:c.data.user.is_phone_verified,licenseImageUrl:c.data.user.license_image_url,licenseVerificationStatus:c.data.user.license_verification_status,createdAt:c.data.user.created_at};localStorage.setItem("auth_token",c.data.token),a(p),localStorage.setItem("user",JSON.stringify(p))}else throw new Error("Login failed: Invalid response from server")}catch(c){throw $.isAxiosError(c)&&c.response?c.response.data&&c.response.data.message?l(c.response.data.message):l(`Login failed: ${c.response.statusText}`):l(c instanceof Error?c.message:"An error occurred during login"),c}finally{i(!1)}},register:async(w,b)=>{i(!0),l(null);try{const c={name:w.name,email:w.email,password:b,password_confirmation:b,role:w.role||"client",phone_number:w.phoneNumber,license_image_url:w.licenseImageUrl},p=await H.post(K.REGISTER,c);if(p.data&&p.data.user){const v={id:p.data.user.id.toString(),email:p.data.user.email,name:p.data.user.name,role:p.data.user.role,phoneNumber:p.data.user.phone_number,isPhoneVerified:p.data.user.is_phone_verified,licenseImageUrl:p.data.user.license_image_url,licenseVerificationStatus:p.data.user.license_verification_status,createdAt:p.data.user.created_at};localStorage.setItem("auth_token",p.data.token),a(v),localStorage.setItem("user",JSON.stringify(v))}else throw new Error("Registration failed: Invalid response from server")}catch(c){if(console.error("Registration error:",c),$.isAxiosError(c)&&c.response)if(console.error("Response data:",c.response.data),console.error("Response status:",c.response.status),c.response.data&&c.response.data.errors){const p=c.response.data.errors;let v="";p.email?v="This email address is already registered. Please use a different email or try logging in.":v=Object.values(p).flat().join(", "),l(v)}else c.response.data&&c.response.data.message?l(c.response.data.message):l(`Registration failed: ${c.response.statusText}`);else l(c instanceof Error?c.message:"An error occurred during registration");throw c}finally{i(!1)}},logout:async()=>{try{localStorage.getItem("auth_token")&&await H.post(K.LOGOUT)}catch(w){console.error("Error during logout:",w)}finally{a(null),localStorage.removeItem("user"),localStorage.removeItem("auth_token")}},updateUser:async w=>{i(!0),l(null);try{if(!t)throw new Error("No user is logged in");const b={name:w.name,email:w.email,phone_number:w.phoneNumber,license_image_url:w.licenseImageUrl},c=await H.put(`${K.USERS}/${t.id}`,b);if(c.data&&c.data.user){const p={...t,name:c.data.user.name||t.name,email:c.data.user.email||t.email,phoneNumber:c.data.user.phone_number,licenseImageUrl:c.data.user.license_image_url};a(p),localStorage.setItem("user",JSON.stringify(p))}else throw new Error("Update failed: Invalid response from server")}catch(b){if($.isAxiosError(b)&&b.response)if(b.response.data&&b.response.data.errors){const c=Object.values(b.response.data.errors).flat().join(", ");l(c)}else b.response.data&&b.response.data.message?l(b.response.data.message):l(`Update failed: ${b.response.statusText}`);else l(b instanceof Error?b.message:"An error occurred while updating user data");throw b}finally{i(!1)}}};return e.jsx(Ht.Provider,{value:x,children:s})},qt=y.createContext(void 0),Ne=()=>{const s=y.useContext(qt);if(s===void 0)throw new Error("useCars must be used within a CarProvider");return s},ni=({children:s})=>{const[t,a]=y.useState([]),[r,i]=y.useState([]),[n,l]=y.useState([]),[u,d]=y.useState([]),[m,h]=y.useState(null),[x,w]=y.useState(!0),[b,c]=y.useState(null),p=async()=>{w(!0),c(null);try{const f=await H.get(K.CARS);if(f.data&&Array.isArray(f.data.cars)){const C=f.data.cars.map(N=>({id:N.id.toString(),ownerId:N.owner_id.toString(),make:N.make,model:N.model,year:N.year,images:N.images||[],description:N.description,features:N.features||[],location:N.location,pricePerHour:N.price_per_hour,availabilityNotes:N.availability_notes||"",isActive:N.is_active,createdAt:N.created_at})),g=C.filter(N=>N.isActive);a(C),i(g),l(g)}else console.warn("API did not return expected data format"),a([]),i([]),l([]),c("API did not return expected data format")}catch(f){console.error("Error fetching cars from API:",f),a([]),i([]),l([]),c("Failed to fetch cars from server")}finally{w(!1)}},v=async()=>{w(!0),c(null);try{if(!localStorage.getItem("auth_token"))throw new Error("Authentication token not found");const C=await H.get(K.MY_CARS);if(C.data&&Array.isArray(C.data)){const g=C.data.map(N=>({id:N.id.toString(),ownerId:N.owner_id.toString(),make:N.make,model:N.model,year:N.year,images:N.images||[],description:N.description,features:N.features||[],location:N.location,pricePerHour:N.price_per_hour,availabilityNotes:N.availability_notes||"",isActive:N.is_active,createdAt:N.created_at}));d(g)}else console.warn("API did not return expected data format for owner cars"),d([]),c("API did not return expected data format")}catch(f){console.error("Error fetching owner cars from API:",f),d([]),c("Failed to fetch owner cars from server")}finally{w(!1)}},E=async f=>{w(!0),c(null);try{const C=t.find(N=>N.id===f);if(C)return h(C),C;const g=await H.get(`${K.CARS}/${f}`);if(g.data&&g.data.car){const N={id:g.data.car.id.toString(),ownerId:g.data.car.owner_id.toString(),make:g.data.car.make,model:g.data.car.model,year:g.data.car.year,images:g.data.car.images||[],description:g.data.car.description,features:g.data.car.features||[],location:g.data.car.location,pricePerHour:g.data.car.price_per_hour,availabilityNotes:g.data.car.availability_notes||"",isActive:g.data.car.is_active,createdAt:g.data.car.created_at};return h(N),N}else return c("Car not found"),null}catch(C){return c("Failed to fetch car details"),console.error(C),null}finally{w(!1)}},R=async(f,C)=>{w(!0),c(null);try{if(!localStorage.getItem("auth_token"))throw new Error("Authentication token not found");const N=new FormData;N.append("make",f.make),N.append("model",f.model),N.append("year",f.year.toString()),N.append("description",f.description),N.append("location",f.location),N.append("price_per_hour",f.pricePerHour.toString()),N.append("availability_notes",f.availabilityNotes||""),N.append("is_active",f.isActive?"1":"0"),N.append("features",JSON.stringify(f.features)),C&&C.length>0?C.forEach((T,U)=>{N.append(`images[${U}]`,T)}):f.images&&f.images.length>0&&N.append("image_urls",JSON.stringify(f.images));const k=localStorage.getItem("auth_token"),P=await H.post(K.CARS,N,{headers:{"Content-Type":"multipart/form-data",Authorization:`Bearer ${k}`}});if(P.data&&P.data.car){const T={id:P.data.car.id.toString(),ownerId:P.data.car.owner_id.toString(),make:P.data.car.make,model:P.data.car.model,year:P.data.car.year,images:P.data.car.images||[],description:P.data.car.description,features:P.data.car.features||[],location:P.data.car.location,pricePerHour:P.data.car.price_per_hour,availabilityNotes:P.data.car.availability_notes||"",isActive:P.data.car.is_active,createdAt:P.data.car.created_at};return a(U=>[...U,T]),d(U=>[...U,T]),T.isActive&&(i(U=>[...U,T]),l(U=>[...U,T])),T}else throw new Error("Failed to add car: Invalid response from server")}catch(g){if(console.error("Add car error:",g),$.isAxiosError(g)&&g.response)if(console.error("Response data:",g.response.data),console.error("Response status:",g.response.status),g.response.data&&g.response.data.errors){const N=Object.values(g.response.data.errors).flat().join(", ");c(N)}else g.response.data&&g.response.data.message?c(g.response.data.message):c(`Failed to add car: ${g.response.statusText}`);else c(g instanceof Error?g.message:"Failed to add car");throw console.error(g),g}finally{w(!1)}},I=async(f,C)=>{w(!0),c(null);try{if(!localStorage.getItem("auth_token"))throw new Error("Authentication token not found");const N={};C.make&&(N.make=C.make),C.model&&(N.model=C.model),C.year&&(N.year=C.year),C.description&&(N.description=C.description),C.location&&(N.location=C.location),C.pricePerHour&&(N.price_per_hour=C.pricePerHour),C.availabilityNotes!==void 0&&(N.availability_notes=C.availabilityNotes),C.isActive!==void 0&&(N.is_active=C.isActive?1:0),C.features&&(N.features=JSON.stringify(C.features));const k=await H.put(`${K.CARS}/${f}`,N);if(k.data&&k.data.car){const P={id:k.data.car.id.toString(),ownerId:k.data.car.owner_id.toString(),make:k.data.car.make,model:k.data.car.model,year:k.data.car.year,images:k.data.car.images||[],description:k.data.car.description,features:k.data.car.features||[],location:k.data.car.location,pricePerHour:k.data.car.price_per_hour,availabilityNotes:k.data.car.availability_notes||"",isActive:k.data.car.is_active,createdAt:k.data.car.created_at};return a(T=>{const U=[...T],F=U.findIndex(q=>q.id===f);return F!==-1&&(U[F]=P),U}),d(T=>{const U=[...T],F=U.findIndex(q=>q.id===f);return F!==-1&&(U[F]=P),U}),i(T=>{const U=[...T],F=U.findIndex(q=>q.id===f);return P.isActive?F===-1?U.push(P):U[F]=P:F!==-1&&U.splice(F,1),U}),l(T=>{const U=[...T],F=U.findIndex(q=>q.id===f);return P.isActive?F===-1?U.push(P):U[F]=P:F!==-1&&U.splice(F,1),U}),(m==null?void 0:m.id)===f&&h(P),P}else throw new Error("Failed to update car: Invalid response from server")}catch(g){if($.isAxiosError(g)&&g.response)if(g.response.data&&g.response.data.errors){const N=Object.values(g.response.data.errors).flat().join(", ");c(N)}else g.response.data&&g.response.data.message?c(g.response.data.message):c(`Failed to update car: ${g.response.statusText}`);else c(g instanceof Error?g.message:"Failed to update car");throw console.error(g),g}finally{w(!1)}},B=async f=>{w(!0),c(null);try{if(!localStorage.getItem("auth_token"))throw new Error("Authentication token not found");await H.delete(`${K.CARS}/${f}`),a(g=>g.filter(N=>N.id!==f)),d(g=>g.filter(N=>N.id!==f)),i(g=>g.filter(N=>N.id!==f)),l(g=>g.filter(N=>N.id!==f)),(m==null?void 0:m.id)===f&&h(null)}catch(C){throw $.isAxiosError(C)&&C.response?C.response.data&&C.response.data.message?c(C.response.data.message):c(`Failed to delete car: ${C.response.statusText}`):c(C instanceof Error?C.message:"Failed to delete car"),console.error(C),C}finally{w(!1)}},A=f=>{const C=n;if(!f.trim()){i(C);return}const g=f.toLowerCase(),N=C.filter(k=>k.make.toLowerCase().includes(g)||k.model.toLowerCase().includes(g)||k.location.toLowerCase().includes(g)||k.description.toLowerCase().includes(g));i(N)};y.useEffect(()=>{p()},[]);const D={cars:t,filteredCars:r,activeCars:n,ownerCars:u,selectedCar:m,isLoading:x,error:b,fetchCars:p,fetchOwnerCars:v,getCarById:E,setSelectedCar:h,addCar:R,updateCar:I,deleteCar:B,filterCars:A};return e.jsx(qt.Provider,{value:D,children:s})},li=[{id:"driver-1",userId:"user-3",name:"Michael Johnson",age:32,experience:10,profileImage:"https://images.pexels.com/photos/1300402/pexels-photo-1300402.jpeg",licenseNumber:"**********",licenseVerificationStatus:"verified",location:"Downtown Tech District",pricePerHour:25,rating:4.8,reviews:124,specialties:["City Driving","Long Distance","Airport Transfers"],availabilityNotes:"Available weekdays from 8 AM to 6 PM",isAvailable:!0,createdAt:"2024-02-01T00:00:00.000Z"},{id:"driver-2",userId:"user-4",name:"Sarah Williams",age:29,experience:7,profileImage:"https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg",licenseNumber:"**********",licenseVerificationStatus:"verified",location:"Westside Shopping Center",pricePerHour:22,rating:4.9,reviews:98,specialties:["City Driving","Event Transportation","Tourist Guide"],availabilityNotes:"Available all days from 10 AM to 8 PM",isAvailable:!0,createdAt:"2024-02-05T00:00:00.000Z"},{id:"driver-3",userId:"user-5",name:"David Brown",age:35,experience:12,profileImage:"https://images.pexels.com/photos/1681010/pexels-photo-1681010.jpeg",licenseNumber:"**********",licenseVerificationStatus:"verified",location:"North Tech Park",pricePerHour:28,rating:4.7,reviews:156,specialties:["Luxury Vehicles","Corporate Events","Airport Transfers"],availabilityNotes:"Available weekends and evenings after 6 PM",isAvailable:!0,createdAt:"2024-02-10T00:00:00.000Z"}],oi=[{id:"booking-1",userId:"user-1",itemType:"car",itemId:"car-1",startTime:"2024-03-15T10:00:00.000Z",endTime:"2024-03-15T14:00:00.000Z",totalPrice:60,status:"completed",createdAt:"2024-03-10T00:00:00.000Z"},{id:"booking-2",userId:"user-1",itemType:"driver",itemId:"driver-1",startTime:"2024-03-20T09:00:00.000Z",endTime:"2024-03-20T13:00:00.000Z",totalPrice:100,status:"confirmed",createdAt:"2024-03-15T00:00:00.000Z"}],$t=y.createContext(void 0),Ee=()=>{const s=y.useContext($t);if(!s)throw new Error("useDrivers must be used within a DriverProvider");return s},ci=({children:s})=>{const[t,a]=y.useState([]),[r,i]=y.useState([]),[n,l]=y.useState(null),[u,d]=y.useState(!1),[m,h]=y.useState(null),[x,w]=y.useState(!1),b=y.useCallback(async()=>{console.log("DriverContext: Starting to fetch drivers..."),d(!0),h(null);try{console.log("DriverContext: Making API request to",K.DRIVERS);const A=await H.get(K.DRIVERS);if(console.log("DriverContext: API response received:",A.status,A.data),A.data&&Array.isArray(A.data)){const D=A.data.map(f=>({id:f.id.toString(),userId:f.user_id.toString(),name:f.name,age:f.age,experience:f.experience,profileImage:f.profile_image||"",licenseNumber:f.license_number,licenseVerificationStatus:f.license_verification_status,location:f.location,pricePerHour:f.price_per_hour,rating:Number(f.rating||0),reviews:Number(f.reviews||0),specialties:f.specialties||[],availabilityNotes:f.availability_notes||"",isAvailable:f.is_available,createdAt:f.created_at}));console.log("DriverContext: Successfully transformed",D.length,"drivers"),a(D),i(D),w(!0)}else console.warn("API did not return expected data format for drivers"),a([]),i([]),h("API did not return expected data format")}catch(A){console.error("Error fetching drivers from API:",A),a([]),i([]),h("Failed to fetch drivers from server")}finally{d(!1),w(!0)}},[]),c=async A=>{d(!0),h(null);try{let D=t.find(f=>f.id===A)||null;if(!D)try{const f=await H.get(`/drivers/${A}`);f.data&&(D={id:f.data.id.toString(),userId:f.data.user_id.toString(),name:f.data.name,age:f.data.age,experience:f.data.experience,profileImage:f.data.profile_image||"",licenseNumber:f.data.license_number,licenseVerificationStatus:f.data.license_verification_status,location:f.data.location,pricePerHour:f.data.price_per_hour,rating:Number(f.data.rating||0),reviews:Number(f.data.reviews||0),specialties:f.data.specialties||[],availabilityNotes:f.data.availability_notes||"",isAvailable:f.data.is_available,isActive:f.data.is_active,createdAt:f.data.created_at})}catch(f){console.error("Error fetching driver from API:",f),D=li.find(C=>C.id===A)||null}return l(D),D}catch(D){return h("Failed to fetch driver"),console.error(D),null}finally{d(!1)}},p=async(A,D)=>{d(!0),h(null);try{const f=new FormData;f.append("name",A.name),f.append("age",A.age.toString()),f.append("experience",A.experience.toString()),f.append("license_number",A.licenseNumber),f.append("location",A.location),f.append("price_per_hour",A.pricePerHour.toString()),f.append("specialties",JSON.stringify(A.specialties)),A.availabilityNotes&&f.append("availability_notes",A.availabilityNotes),D&&f.append("profile_image",D);const C=localStorage.getItem("auth_token"),g=await H.post(K.DRIVERS,f,{headers:{"Content-Type":"multipart/form-data",Authorization:`Bearer ${C}`}}),N={id:g.data.driver.id.toString(),userId:g.data.driver.user_id.toString(),name:g.data.driver.name,age:g.data.driver.age,experience:g.data.driver.experience,profileImage:g.data.driver.profile_image||"",licenseNumber:g.data.driver.license_number,licenseVerificationStatus:g.data.driver.license_verification_status,location:g.data.driver.location,pricePerHour:g.data.driver.price_per_hour,rating:Number(g.data.driver.rating||0),reviews:Number(g.data.driver.reviews||0),specialties:g.data.driver.specialties||[],availabilityNotes:g.data.driver.availability_notes||"",isAvailable:g.data.driver.is_available,createdAt:g.data.driver.created_at};return a(k=>[...k,N]),i(k=>[...k,N]),N}catch(f){if(console.error("Add driver error:",f),$.isAxiosError(f)&&f.response)if(console.error("Response data:",f.response.data),console.error("Response status:",f.response.status),f.response.data&&f.response.data.errors){const C=Object.values(f.response.data.errors).flat().join(", ");h(C)}else f.response.data&&f.response.data.message?h(f.response.data.message):h(`Failed to add driver: ${f.response.statusText}`);else h(f instanceof Error?f.message:"Failed to add driver");throw f}finally{d(!1)}},v=async(A,D)=>{d(!0),h(null);try{const f=t.findIndex(N=>N.id===A);if(f===-1)throw new Error("Driver not found");const C={...t[f],...D},g=[...t];return g[f]=C,a(g),i(g),(n==null?void 0:n.id)===A&&l(C),C}catch(f){throw h("Failed to update driver"),console.error(f),f}finally{d(!1)}},E=async A=>{d(!0),h(null);try{const D=t.filter(f=>f.id!==A);a(D),i(D),(n==null?void 0:n.id)===A&&l(null)}catch(D){throw h("Failed to delete driver"),console.error(D),D}finally{d(!1)}},R=y.useCallback(A=>{if(!A.trim()){i(t);return}const D=A.toLowerCase(),f=t.filter(C=>C.name.toLowerCase().includes(D)||C.location.toLowerCase().includes(D)||C.specialties.some(g=>g.toLowerCase().includes(D)));i(f)},[t]),I=async A=>{const D=t.find(f=>f.id===A);if(!D)throw new Error("Driver not found");return v(A,{isAvailable:!D.isAvailable})};y.useEffect(()=>{x||b()},[x,b]);const B={drivers:t,filteredDrivers:r,selectedDriver:n,isLoading:u,error:m,fetchDrivers:b,getDriverById:c,setSelectedDriver:l,addDriver:p,updateDriver:v,deleteDriver:E,filterDrivers:R,toggleDriverAvailability:I};return e.jsx($t.Provider,{value:B,children:s})},Vt=y.createContext(void 0),Ye=()=>{const s=y.useContext(Vt);if(!s)throw new Error("useBookings must be used within a BookingProvider");return s},di=({children:s})=>{const[t,a]=y.useState([]),[r,i]=y.useState([]),[n,l]=y.useState(!0),[u,d]=y.useState(null),m=async()=>{l(!0),d(null);try{a(oi)}catch(v){d("Failed to fetch bookings"),console.error(v)}finally{l(!1)}},h=async v=>{l(!0),d(null);try{return t.find(R=>R.id===v)||null}catch(E){return d("Failed to fetch booking"),console.error(E),null}finally{l(!1)}},x=async v=>{l(!0),d(null);try{const E={...v,id:`booking-${Date.now()}`,createdAt:new Date().toISOString()};return a(R=>[...R,E]),i(R=>R.some(I=>I.userId===v.userId)?[...R,E]:R),E}catch(E){throw d("Failed to create booking"),console.error(E),E}finally{l(!1)}},w=async(v,E)=>{l(!0),d(null);try{const R=t.findIndex(A=>A.id===v);if(R===-1)throw new Error("Booking not found");const I={...t[R],status:E},B=[...t];return B[R]=I,a(B),i(A=>{const D=A.findIndex(f=>f.id===v);if(D!==-1){const f=[...A];return f[D]=I,f}return A}),I}catch(R){throw d("Failed to update booking status"),console.error(R),R}finally{l(!1)}},b=async v=>w(v,"cancelled"),c=v=>t.filter(E=>E.userId===v);y.useEffect(()=>{m()},[]);const p={bookings:t,userBookings:r,isLoading:n,error:u,fetchBookings:m,getBookingById:h,createBooking:x,updateBookingStatus:w,cancelBooking:b,getUserBookings:c};return e.jsx(Vt.Provider,{value:p,children:s})},Gt=y.createContext(void 0),mi=()=>{const s=y.useContext(Gt);if(!s)throw new Error("useAdmin must be used within an AdminProvider");return s},xi=({children:s})=>{const[t,a]=y.useState(null),[r,i]=y.useState([]),[n,l]=y.useState([]),[u,d]=y.useState([]),[m,h]=y.useState([]),[x,w]=y.useState([]),[b,c]=y.useState(!1),[p,v]=y.useState(null),E=async()=>{var k;c(!0),v(null);try{const P=await H.get(K.ADMIN.DASHBOARD);a(P.data)}catch(P){console.error("Error fetching dashboard stats:",P),v(`Failed to fetch dashboard statistics: ${((k=P.response)==null?void 0:k.status)||P.message}`)}finally{c(!1)}},R=async()=>{var k;c(!0),v(null);try{const P=await H.get(K.ADMIN.USERS);i(P.data)}catch(P){console.error("Error fetching users:",P),v(`Failed to fetch users: ${((k=P.response)==null?void 0:k.status)||P.message}`)}finally{c(!1)}},I=async()=>{c(!0),v(null);try{const P=(await H.get(K.ADMIN.CARS)).data.map(T=>({...T,isActive:T.is_active,pricePerHour:T.price_per_hour,ownerId:T.owner_id,createdAt:T.created_at,updatedAt:T.updated_at}));l(P)}catch(k){console.error("Error fetching cars:",k),v("Failed to fetch cars")}finally{c(!1)}},B=async()=>{c(!0),v(null);try{const P=(await H.get(K.ADMIN.DRIVERS)).data.map(T=>({id:T.id.toString(),userId:T.user_id.toString(),name:T.name,age:T.age,experience:T.experience,profileImage:T.profile_image||"",licenseNumber:T.license_number,licenseVerificationStatus:T.license_verification_status,location:T.location,pricePerHour:T.price_per_hour,rating:Number(T.rating||0),reviews:Number(T.reviews||0),specialties:T.specialties||[],availabilityNotes:T.availability_notes||"",isAvailable:T.is_available,isActive:T.is_active,createdAt:T.created_at}));d(P)}catch(k){console.error("Error fetching drivers:",k),v("Failed to fetch drivers")}finally{c(!1)}},A=async()=>{c(!0),v(null);try{const k=await H.get(K.ADMIN.BOOKINGS);h(k.data)}catch(k){console.error("Error fetching bookings:",k),v("Failed to fetch bookings")}finally{c(!1)}},D=async(k,P)=>{try{await H.post(`/admin/users/${k}/update-role`,{role:P}),await R()}catch(T){throw console.error("Error updating user role:",T),new Error("Failed to update user role")}},f=async(k,P)=>{try{await H.post(`/admin/drivers/${k}/verify-license`,{status:P}),await B()}catch(T){throw console.error("Error verifying driver license:",T),new Error("Failed to verify driver license")}},C=async()=>{try{const k=await H.get("/admin/gps-requests");w(k.data)}catch(k){throw console.error("Error fetching GPS requests:",k),new Error("Failed to fetch GPS requests")}},N={stats:t,users:r,cars:n,drivers:u,bookings:m,gpsRequests:x,isLoading:b,error:p,fetchDashboardStats:E,fetchUsers:R,fetchCars:I,fetchDrivers:B,fetchBookings:A,fetchGpsRequests:C,updateUserRole:D,verifyDriverLicense:f,updateGpsRequestStatus:async(k,P,T)=>{try{await H.post(`/admin/gps-requests/${k}/update-status`,{status:P,admin_notes:T}),await C()}catch(U){throw console.error("Error updating GPS request status:",U),new Error("Failed to update GPS request status")}}};return e.jsx(Gt.Provider,{value:N,children:s})};/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var ui={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hi=s=>s.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),z=(s,t)=>{const a=y.forwardRef(({color:r="currentColor",size:i=24,strokeWidth:n=2,absoluteStrokeWidth:l,className:u="",children:d,...m},h)=>y.createElement("svg",{ref:h,...ui,width:i,height:i,stroke:r,strokeWidth:l?Number(n)*24/Number(i):n,className:["lucide",`lucide-${hi(s)}`,u].join(" "),...m},[...t.map(([x,w])=>y.createElement(x,w)),...Array.isArray(d)?d:[d]]));return a.displayName=`${s}`,a};/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const oe=z("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pi=z("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ve=z("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yt=z("Award",[["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}],["path",{d:"M15.477 12.89 17 22l-5-3-5 3 1.523-9.11",key:"em7aur"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fi=z("BarChart2",[["line",{x1:"18",x2:"18",y1:"20",y2:"10",key:"1xfpm4"}],["line",{x1:"12",x2:"12",y1:"20",y2:"4",key:"be30l9"}],["line",{x1:"6",x2:"6",y1:"20",y2:"14",key:"1r4le6"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const he=z("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Z=z("Car",[["path",{d:"M19 17h2c.6 0 1-.4 1-1v-3c0-.9-.7-1.7-1.5-1.9C18.7 10.6 16 10 16 10s-1.3-1.4-2.2-2.3c-.5-.4-1.1-.7-1.8-.7H5c-.6 0-1.1.4-1.4.9l-1.4 2.9A3.7 3.7 0 0 0 2 12v4c0 .6.4 1 1 1h2",key:"5owen"}],["circle",{cx:"7",cy:"17",r:"2",key:"u2ysq9"}],["path",{d:"M9 17h6",key:"r8uit2"}],["circle",{cx:"17",cy:"17",r:"2",key:"axvx0g"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Te=z("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Is=z("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gi=z("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yi=z("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const We=z("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hs=z("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Be=z("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bi=z("Facebook",[["path",{d:"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z",key:"1jg4f8"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wt=z("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ji=z("Home",[["path",{d:"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"y5dka4"}],["polyline",{points:"9 22 9 12 15 12 15 22",key:"e2us08"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $e=z("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vi=z("Instagram",[["rect",{width:"20",height:"20",x:"2",y:"2",rx:"5",ry:"5",key:"2e1cvw"}],["path",{d:"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z",key:"9exkf1"}],["line",{x1:"17.5",x2:"17.51",y1:"6.5",y2:"6.5",key:"r4j83e"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ot=z("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jt=z("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const re=z("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ni=z("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Kt=z("MessageCircle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Se=z("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Us=z("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ct=z("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ps=z("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wi=z("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ki=z("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ms=z("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ie=z("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a2.121 2.121 0 1 1 3 3L12 15l-4 1 1-4Z",key:"1lpok0"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bs=z("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zt=z("ToggleLeft",[["rect",{width:"20",height:"12",x:"2",y:"6",rx:"6",ry:"6",key:"f2vt7d"}],["circle",{cx:"8",cy:"12",r:"2",key:"1nvbw3"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ts=z("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ci=z("Twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fs=z("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const le=z("UserRound",[["circle",{cx:"12",cy:"8",r:"5",key:"1hypcn"}],["path",{d:"M20 21a8 8 0 0 0-16 0",key:"rfgkzh"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const je=z("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rs=z("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const He=z("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pe=z("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),S=({variant:s="primary",size:t="md",className:a="",onClick:r,disabled:i=!1,type:n="button",children:l,fullWidth:u=!1})=>{const d="inline-flex items-center justify-center font-medium transition-colors duration-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2",m={primary:"bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500",secondary:"bg-secondary-600 text-white hover:bg-secondary-700 focus:ring-secondary-500",accent:"bg-accent-600 text-white hover:bg-accent-700 focus:ring-accent-500",outline:"bg-transparent border border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-gray-500",ghost:"bg-transparent text-gray-700 hover:bg-gray-100 focus:ring-gray-500"},h={sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-base",lg:"px-6 py-3 text-lg"},x=u?"w-full":"",w=i?"opacity-50 cursor-not-allowed":"cursor-pointer",b=`${d} ${m[s]} ${h[t]} ${x} ${w} ${a}`;return e.jsx("button",{type:n,className:b,onClick:r,disabled:i,children:l})},Si=()=>{const{user:s,isAuthenticated:t,logout:a}=ee(),r=de(),[i,n]=y.useState(!1),l=()=>{n(!i)},u=()=>{a(),r("/"),n(!1)},d=[{name:"Home",path:"/",icon:e.jsx(ji,{size:18})},{name:"Browse Cars",path:"/cars",icon:e.jsx(Z,{size:18})},{name:"Hire a Driver",path:"/drivers",icon:e.jsx(le,{size:18})}],m=()=>{if(!s)return{name:"My Account",path:"/account",icon:e.jsx(je,{size:18})};switch(s.role){case"admin":return{name:"Admin Dashboard",path:"/admin",icon:e.jsx(je,{size:18})};case"owner":return{name:"Owner Dashboard",path:"/owner/dashboard",icon:e.jsx(Z,{size:18})};case"driver":return{name:"Driver Dashboard",path:"/driver/dashboard",icon:e.jsx(le,{size:18})};case"client":return{name:"My Account",path:"/account",icon:e.jsx(je,{size:18})};default:return{name:"My Account",path:"/account",icon:e.jsx(je,{size:18})}}};s==null||s.role;const h=t?[m()]:[{name:"Log In",path:"/login",icon:e.jsx(je,{size:18})},{name:"Sign Up",path:"/signup",icon:e.jsx(je,{size:18})}];return e.jsxs("header",{className:"bg-white shadow-md sticky top-0 z-50",children:[e.jsx("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"flex justify-between items-center py-4",children:[e.jsxs(O,{to:"/",className:"flex items-center space-x-2",children:[e.jsx(Z,{className:"h-8 w-8 text-primary-600"}),e.jsx("span",{className:"text-xl font-bold text-gray-900",children:"Park & Rent"})]}),e.jsxs("nav",{className:"hidden md:flex items-center space-x-1",children:[d.map(x=>e.jsx(O,{to:x.path,className:"px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-100 hover:text-primary-600 transition-colors",children:x.name},x.path)),t?e.jsxs(e.Fragment,{children:[e.jsx(O,{to:m().path,className:"px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-100 hover:text-primary-600 transition-colors",children:m().name}),e.jsxs(S,{variant:"outline",size:"sm",onClick:u,className:"ml-2",children:[e.jsx(ot,{size:16,className:"mr-1"}),"Log Out"]})]}):e.jsxs(e.Fragment,{children:[e.jsx(O,{to:"/login",children:e.jsx(S,{variant:"outline",size:"sm",className:"ml-2",children:"Log In"})}),e.jsx(O,{to:"/signup",children:e.jsx(S,{size:"sm",className:"ml-2",children:"Sign Up"})})]})]}),e.jsx("button",{className:"md:hidden rounded-md p-2 text-gray-700 hover:bg-gray-100 focus:outline-none",onClick:l,children:i?e.jsx(Pe,{size:24}):e.jsx(Ni,{size:24})})]})}),i&&e.jsx("div",{className:"md:hidden animate-fade-in",children:e.jsxs("div",{className:"px-2 pt-2 pb-4 space-y-1 sm:px-3 border-t border-gray-200",children:[d.map(x=>e.jsxs(O,{to:x.path,className:"flex items-center px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:bg-gray-100 hover:text-primary-600",onClick:()=>n(!1),children:[x.icon,e.jsx("span",{className:"ml-2",children:x.name})]},x.path)),h.map(x=>e.jsxs(O,{to:x.path,className:"flex items-center px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:bg-gray-100 hover:text-primary-600",onClick:()=>n(!1),children:[x.icon,e.jsx("span",{className:"ml-2",children:x.name})]},x.path)),t&&e.jsxs("button",{className:"flex items-center w-full px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:bg-gray-100 hover:text-primary-600",onClick:u,children:[e.jsx(ot,{size:18}),e.jsx("span",{className:"ml-2",children:"Log Out"})]})]})})]})},Ai=()=>e.jsx("footer",{className:"bg-gray-900 text-gray-300",children:e.jsxs("div",{className:"container mx-auto px-4 py-12",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center space-x-2 mb-4",children:[e.jsx(Z,{className:"h-6 w-6 text-primary-500"}),e.jsx("span",{className:"text-lg font-bold text-white",children:"Park & Rent"})]}),e.jsx("p",{className:"mb-4 text-sm",children:"Connecting car owners with people who need short-term rentals. Our platform facilitates discovery and contact, while letting you handle the details directly."}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsx("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors","aria-label":"Facebook",children:e.jsx(bi,{size:20})}),e.jsx("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors","aria-label":"Twitter",children:e.jsx(Ci,{size:20})}),e.jsx("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors","aria-label":"Instagram",children:e.jsx(vi,{size:20})})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Quick Links"}),e.jsxs("ul",{className:"space-y-2",children:[e.jsx("li",{children:e.jsx(O,{to:"/",className:"text-gray-400 hover:text-white transition-colors",children:"Home"})}),e.jsx("li",{children:e.jsx(O,{to:"/cars",className:"text-gray-400 hover:text-white transition-colors",children:"Browse Cars"})}),e.jsx("li",{children:e.jsx(O,{to:"/login",className:"text-gray-400 hover:text-white transition-colors",children:"Log In"})}),e.jsx("li",{children:e.jsx(O,{to:"/signup",className:"text-gray-400 hover:text-white transition-colors",children:"Sign Up"})})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Legal"}),e.jsxs("ul",{className:"space-y-2",children:[e.jsx("li",{children:e.jsx(O,{to:"/terms",className:"text-gray-400 hover:text-white transition-colors",children:"Terms of Service"})}),e.jsx("li",{children:e.jsx(O,{to:"/privacy",className:"text-gray-400 hover:text-white transition-colors",children:"Privacy Policy"})}),e.jsx("li",{children:e.jsx(O,{to:"/faq",className:"text-gray-400 hover:text-white transition-colors",children:"FAQ"})}),e.jsx("li",{children:e.jsx(O,{to:"/help",className:"text-gray-400 hover:text-white transition-colors",children:"Help Center"})})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Contact Us"}),e.jsxs("ul",{className:"space-y-3",children:[e.jsxs("li",{className:"flex items-start",children:[e.jsx(Se,{size:18,className:"mr-2 mt-0.5 flex-shrink-0"}),e.jsx("span",{children:"**********"})]}),e.jsxs("li",{className:"flex items-start",children:[e.jsx(Jt,{size:18,className:"mr-2 mt-0.5 flex-shrink-0"}),e.jsx("span",{children:"<EMAIL>"})]}),e.jsxs("li",{className:"flex items-start",children:[e.jsx($e,{size:18,className:"mr-2 mt-0.5 flex-shrink-0"}),e.jsx("span",{children:"Park & Rent facilitates connections only. All bookings, payments, and arrangements are handled directly between users."})]})]})]})]}),e.jsxs("div",{className:"border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center",children:[e.jsxs("p",{className:"text-sm text-gray-500",children:["© ",new Date().getFullYear()," Park & Rent. All rights reserved."]}),e.jsxs("div",{className:"flex items-center mt-4 md:mt-0",children:[e.jsx(Ms,{size:16,className:"text-gray-400 mr-1"}),e.jsx("span",{className:"text-sm text-gray-500",children:"Secure Connection"})]})]})]})}),M=({children:s})=>e.jsxs("div",{className:"flex flex-col min-h-screen",children:[e.jsx(Si,{}),e.jsx("main",{className:"flex-grow",children:s}),e.jsx(Ai,{})]}),Pi=({className:s="",children:t,onClick:a,hover:r=!0})=>{const u=`bg-white rounded-lg shadow-md overflow-hidden transition-all duration-200 ${r?"hover:shadow-lg transform hover:-translate-y-1":""} ${a?"cursor-pointer":""} ${s}`;return e.jsx("div",{className:u,onClick:a,children:t})},Ei=({src:s,alt:t,className:a=""})=>e.jsx("div",{className:`aspect-video w-full overflow-hidden ${a}`,children:e.jsx("img",{src:s,alt:t,className:"w-full h-full object-cover",onError:r=>{const i=r.target;i.style.display="none";const n=i.parentElement;if(n&&!n.querySelector(".image-fallback")){const l=document.createElement("div");l.className="image-fallback w-full h-full bg-gray-200 flex items-center justify-center text-gray-500 text-sm",l.textContent="Image Not Available",n.appendChild(l)}}})}),Ri=({className:s="",children:t})=>e.jsx("div",{className:`p-4 ${s}`,children:t}),Di=({className:s="",children:t})=>e.jsx("h3",{className:`font-bold text-lg mb-2 ${s}`,children:t}),Ti=({className:s="",children:t})=>e.jsx("div",{className:`px-4 py-3 bg-gray-50 border-t border-gray-100 ${s}`,children:t}),ye=Object.assign(Pi,{Image:Ei,Content:Ri,Title:Di,Footer:Ti}),Fe=(s,t=!0)=>s.toLocaleString("en-RW",{maximumFractionDigits:0,minimumFractionDigits:0}),Hs=(s,t)=>`${Fe(s)}/${t.toLowerCase()}`,qs=({size:s=16,className:t=""})=>e.jsx("div",{className:`inline-flex items-center justify-center ${t}`,children:e.jsx("span",{className:"font-medium",style:{fontSize:`${s*.8}px`,lineHeight:1},children:"₣"})}),Xt=({car:s})=>e.jsxs(ye,{className:"h-full flex flex-col",children:[e.jsx(ye.Image,{src:s.images[0],alt:`${s.make} ${s.model}`}),e.jsxs(ye.Content,{className:"flex-grow",children:[e.jsxs(ye.Title,{children:[s.year," ",s.make," ",s.model]}),e.jsxs("div",{className:"mb-4 space-y-2",children:[e.jsxs("div",{className:"flex items-center text-gray-600",children:[e.jsx(re,{size:16,className:"mr-1 flex-shrink-0"}),e.jsx("span",{className:"text-sm truncate",children:s.location})]}),e.jsxs("div",{className:"flex items-center text-gray-600",children:[e.jsx(We,{size:16,className:"mr-1 flex-shrink-0"}),e.jsx("span",{className:"text-sm truncate",children:s.availabilityNotes})]}),e.jsxs("div",{className:"flex items-center font-medium text-primary-700",children:[e.jsx(qs,{size:16,className:"mr-1 flex-shrink-0"}),e.jsx("span",{children:Hs(s.pricePerHour,"hour")})]})]}),e.jsx("div",{className:"text-sm text-gray-600 line-clamp-2 mb-4",children:s.description})]}),e.jsx(ye.Footer,{className:"mt-auto",children:e.jsx(O,{to:`/cars/${s.id}`,className:"w-full",children:e.jsx(S,{variant:"primary",fullWidth:!0,children:"Book Now"})})})]}),Qt=({driver:s})=>e.jsxs(ye,{className:"h-full flex flex-col",children:[e.jsxs("div",{className:"relative",children:[e.jsx(ye.Image,{src:s.profileImage,alt:s.name,className:"h-64 object-cover"}),e.jsxs("div",{className:"absolute bottom-2 right-2 bg-white px-2 py-1 rounded-full flex items-center shadow-md",children:[e.jsx(Bs,{size:16,className:"text-yellow-500 mr-1"}),e.jsx("span",{className:"font-medium",children:Number(s.rating||0).toFixed(1)}),e.jsxs("span",{className:"text-xs text-gray-500 ml-1",children:["(",s.reviews||0,")"]})]})]}),e.jsxs(ye.Content,{className:"flex-grow",children:[e.jsx(ye.Title,{children:s.name}),e.jsxs("div",{className:"flex items-center mb-2",children:[e.jsx(Yt,{size:16,className:"text-primary-600 mr-1"}),e.jsxs("span",{className:"text-sm",children:[s.experience," years experience"]})]}),e.jsxs("div",{className:"mb-4 space-y-2",children:[e.jsxs("div",{className:"flex items-center text-gray-600",children:[e.jsx(re,{size:16,className:"mr-1 flex-shrink-0"}),e.jsx("span",{className:"text-sm truncate",children:s.location})]}),e.jsxs("div",{className:"flex items-center text-gray-600",children:[e.jsx(We,{size:16,className:"mr-1 flex-shrink-0"}),e.jsx("span",{className:"text-sm truncate",children:s.availabilityNotes})]}),e.jsxs("div",{className:"flex items-center font-medium text-primary-700",children:[e.jsx(qs,{size:16,className:"mr-1 flex-shrink-0"}),e.jsx("span",{children:Hs(s.pricePerHour,"hour")})]})]}),e.jsx("div",{className:"flex flex-wrap gap-1 mb-4",children:s.specialties.map((t,a)=>e.jsx("span",{className:"inline-block bg-gray-100 rounded-full px-2 py-1 text-xs font-medium text-gray-700",children:t},a))})]}),e.jsx(ye.Footer,{className:"mt-auto",children:e.jsx(O,{to:`/drivers/${s.id}`,className:"w-full",children:e.jsx(S,{variant:"primary",fullWidth:!0,children:"Book Now"})})})]}),Ii=()=>{const{activeCars:s}=Ne(),{filteredDrivers:t}=Ee(),a=s.slice(0,3),r=t.slice(0,3);return e.jsxs(M,{children:[e.jsxs("section",{className:"relative bg-gradient-to-r from-primary-900 to-primary-700 text-white",children:[e.jsxs("div",{className:"absolute inset-0 overflow-hidden",children:[e.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-50"}),e.jsx("div",{className:"absolute inset-0 opacity-20",style:{backgroundImage:"url('https://images.pexels.com/photos/170811/pexels-photo-170811.jpeg')",backgroundSize:"cover",backgroundPosition:"center"}})]}),e.jsx("div",{className:"container mx-auto px-4 py-16 md:py-24 relative z-10",children:e.jsxs("div",{className:"max-w-3xl",children:[e.jsx("h1",{className:"text-4xl md:text-5xl font-bold mb-4 leading-tight",children:"Rent Cars Directly From Local Owners"}),e.jsx("p",{className:"text-xl mb-8 text-gray-100",children:"Find affordable rentals from car owners in your area. No middlemen, no hidden fees — just direct peer-to-peer car rentals."}),e.jsxs("div",{className:"flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-4",children:[e.jsx(O,{to:"/cars",children:e.jsx(S,{size:"lg",className:"w-full sm:w-auto",children:"Browse Available Cars"})}),e.jsx(O,{to:"/signup",children:e.jsx(S,{variant:"outline",size:"lg",className:"w-full sm:w-auto !bg-white !bg-opacity-10 hover:!bg-opacity-20 !border-white !text-white hover:!text-gray-900",children:"List Your Car"})})]})]})})]}),e.jsx("section",{className:"py-16 bg-white",children:e.jsxs("div",{className:"container mx-auto px-4",children:[e.jsxs("div",{className:"text-center mb-12",children:[e.jsx("h2",{className:"text-3xl font-bold mb-3",children:"How It Works"}),e.jsx("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto",children:"Park & Rent connects car owners with people who need short-term rentals in a simple, transparent way."})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[e.jsxs("div",{className:"text-center p-6 rounded-lg",children:[e.jsx(ps,{className:"w-12 h-12 text-primary-600 mx-auto mb-4"}),e.jsx("h3",{className:"text-xl font-semibold mb-3",children:"Find a Car"}),e.jsx("p",{className:"text-gray-600",children:"Browse our selection of available cars in your area. Filter by location, price, and features to find your perfect match."})]}),e.jsxs("div",{className:"text-center p-6 rounded-lg",children:[e.jsx(rs,{className:"w-12 h-12 text-primary-600 mx-auto mb-4"}),e.jsx("h3",{className:"text-xl font-semibold mb-3",children:"Connect Directly"}),e.jsx("p",{className:"text-gray-600",children:"Once verified, contact car owners directly through our platform. Arrange pickup times and locations that work for both of you."})]}),e.jsxs("div",{className:"text-center p-6 rounded-lg",children:[e.jsx(Ms,{className:"w-12 h-12 text-primary-600 mx-auto mb-4"}),e.jsx("h3",{className:"text-xl font-semibold mb-3",children:"Safe & Simple"}),e.jsx("p",{className:"text-gray-600",children:"Handle payments directly with the owner. We verify drivers and owners to ensure a safe and trustworthy experience."})]})]})]})}),e.jsx("section",{className:"py-16 bg-gray-50",children:e.jsxs("div",{className:"container mx-auto px-4",children:[e.jsxs("div",{className:"flex justify-between items-center mb-8",children:[e.jsx("h2",{className:"text-3xl font-bold",children:"Featured Cars"}),e.jsx(O,{to:"/cars",children:e.jsx(S,{variant:"outline",children:"View All Cars"})})]}),a.length>0?e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:a.map(i=>e.jsx(Xt,{car:i},i.id))}):e.jsxs("div",{className:"text-center py-12 bg-white rounded-lg shadow-sm border border-gray-200",children:[e.jsx(Z,{size:48,className:"mx-auto text-gray-400 mb-4"}),e.jsx("h3",{className:"text-xl font-medium text-gray-900 mb-2",children:"No cars available right now"}),e.jsx("p",{className:"text-gray-600 mb-6",children:"Check back soon or be the first to list your car!"}),e.jsx(O,{to:"/signup",children:e.jsx(S,{variant:"primary",children:"List Your Car"})})]})]})}),e.jsx("section",{className:"py-16 bg-secondary-700 text-white",children:e.jsx("div",{className:"container mx-auto px-4",children:e.jsxs("div",{className:"max-w-4xl mx-auto text-center",children:[e.jsx(Z,{className:"h-16 w-16 mx-auto mb-6 text-white opacity-80"}),e.jsx("h2",{className:"text-3xl font-bold mb-4",children:"Have a Car Sitting Idle?"}),e.jsx("p",{className:"text-xl mb-8",children:"Turn your parked car into extra income. List your vehicle and connect with people who need short-term rentals in your area."}),e.jsx(O,{to:"/signup",children:e.jsx(S,{size:"lg",className:"!bg-white !text-gray-900 hover:!bg-gray-100 hover:!text-gray-800 !font-semibold border border-gray-300",children:"Start Listing Today"})})]})})}),e.jsx("section",{className:"py-16 bg-white",children:e.jsxs("div",{className:"container mx-auto px-4",children:[e.jsxs("div",{className:"text-center mb-12",children:[e.jsx("h2",{className:"text-3xl font-bold mb-3",children:"Available Across Rwanda"}),e.jsx("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto",children:"Find cars in these popular locations and many more across Rwanda."})]}),e.jsx("div",{className:"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4",children:["Kigali","Butare","Gisenyi","Ruhengeri","Cyangugu","Kibuye","Byumba","Kibungo","Gitarama","Nyanza"].map(i=>e.jsxs("div",{className:"p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors text-center",children:[e.jsx(re,{className:"h-5 w-5 text-primary-600 mx-auto mb-2"}),e.jsx("span",{className:"font-medium",children:i})]},i))})]})}),e.jsx("section",{className:"py-12 bg-gray-50",children:e.jsxs("div",{className:"container mx-auto px-4",children:[e.jsxs("div",{className:"flex justify-between items-center mb-8",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"Featured Drivers"}),e.jsx("p",{className:"text-gray-600",children:"Professional drivers ready to help you get around"})]}),e.jsx(O,{to:"/drivers",children:e.jsx(S,{variant:"outline",children:"View All Drivers"})})]}),r.length>0?e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:r.map(i=>e.jsx(Qt,{driver:i},i.id))}):e.jsxs("div",{className:"text-center py-12 bg-white rounded-lg shadow-sm border border-gray-200",children:[e.jsx(le,{size:48,className:"mx-auto text-gray-400 mb-4"}),e.jsx("h3",{className:"text-xl font-medium text-gray-900 mb-2",children:"No drivers available right now"}),e.jsx("p",{className:"text-gray-600 mb-6",children:"Check back soon or register as a driver yourself!"}),e.jsx(O,{to:"/driver/register",children:e.jsx(S,{variant:"primary",children:"Register as Driver"})})]})]})})]})},Fi=({onSearch:s})=>{const[t,a]=y.useState(""),[r,i]=y.useState(!1),n=u=>{u.preventDefault(),s(t)},l=()=>{i(!r)};return e.jsxs("div",{className:"mb-6",children:[e.jsxs("form",{onSubmit:n,className:"flex items-center mb-4",children:[e.jsxs("div",{className:"relative flex-grow",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:e.jsx(ps,{size:18,className:"text-gray-400"})}),e.jsx("input",{type:"text",placeholder:"Search cars by make, model, or location...",value:t,onChange:u=>a(u.target.value),className:"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"})]}),e.jsx(S,{type:"submit",className:"rounded-l-none",children:"Search"}),e.jsxs(S,{type:"button",variant:"outline",className:"ml-2 flex items-center md:hidden",onClick:l,children:[e.jsx(Wt,{size:18,className:"mr-1"}),"Filters"]})]}),e.jsxs("div",{className:`md:block ${r?"block":"hidden"} bg-white p-4 rounded-lg shadow-sm mb-4 border border-gray-200`,children:[e.jsx("h3",{className:"font-medium text-gray-900 mb-3",children:"Filter Cars"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"price-range",className:"block text-sm font-medium text-gray-700 mb-1",children:"Price Range (per hour)"}),e.jsxs("select",{id:"price-range",className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500",children:[e.jsx("option",{value:"",children:"Any Price"}),e.jsx("option",{value:"0-5000",children:"₣ 0 - 5,000"}),e.jsx("option",{value:"5000-10000",children:"₣ 5,000 - 10,000"}),e.jsx("option",{value:"10000-20000",children:"₣ 10,000 - 20,000"}),e.jsx("option",{value:"20000+",children:"₣ 20,000+"})]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"car-type",className:"block text-sm font-medium text-gray-700 mb-1",children:"Car Type"}),e.jsxs("select",{id:"car-type",className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500",children:[e.jsx("option",{value:"",children:"All Types"}),e.jsx("option",{value:"sedan",children:"Sedan"}),e.jsx("option",{value:"suv",children:"SUV"}),e.jsx("option",{value:"truck",children:"Truck"}),e.jsx("option",{value:"luxury",children:"Luxury"}),e.jsx("option",{value:"electric",children:"Electric"})]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"availability",className:"block text-sm font-medium text-gray-700 mb-1",children:"Availability"}),e.jsxs("select",{id:"availability",className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500",children:[e.jsx("option",{value:"",children:"Any Time"}),e.jsx("option",{value:"weekdays",children:"Weekdays"}),e.jsx("option",{value:"weekends",children:"Weekends"}),e.jsx("option",{value:"all-day",children:"All Day"})]})]})]}),e.jsxs("div",{className:"mt-4 flex justify-end",children:[e.jsx(S,{type:"button",variant:"outline",size:"sm",className:"mr-2",onClick:()=>{i(!1)},children:"Reset"}),e.jsx(S,{type:"button",size:"sm",onClick:()=>{i(!1)},children:"Apply Filters"})]})]})]})},ce=({size:s="md",className:t="",color:a="text-primary-600"})=>{const r={sm:"w-4 h-4",md:"w-6 h-6",lg:"w-8 h-8"};return e.jsx("div",{className:`${r[s]} ${a} ${t}`,children:e.jsxs("svg",{className:"animate-spin -ml-1 mr-3 w-full h-full",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[e.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),e.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]})})},Li=()=>{const{filteredCars:s,isLoading:t,error:a,fetchCars:r,filterCars:i}=Ne();y.useEffect(()=>{window.scrollTo(0,0)},[]);const n=l=>{i(l)};return t?e.jsx(M,{children:e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx(ce,{size:"lg"})})})}):a?e.jsx(M,{children:e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs("div",{className:"bg-error-50 border border-error-200 rounded-lg p-4 text-center",children:[e.jsx("h2",{className:"text-xl font-medium text-error-800 mb-2",children:"Error Loading Cars"}),e.jsx("p",{className:"text-error-600",children:a})]})})}):e.jsx(M,{children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Available Cars"}),e.jsx("p",{className:"text-gray-600",children:"Browse our selection of available cars for rent from local owners."})]}),e.jsx(Fi,{onSearch:n}),s.length>0?e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6",children:s.map(l=>e.jsx(Xt,{car:l},l.id))}):e.jsxs("div",{className:"text-center py-16 bg-white rounded-lg shadow-sm border border-gray-200",children:[e.jsx(Z,{size:48,className:"mx-auto text-gray-400 mb-4"}),e.jsx("h3",{className:"text-xl font-medium text-gray-900 mb-2",children:"No cars match your search"}),e.jsx("p",{className:"text-gray-600",children:"Try adjusting your filters or search term to find available cars."})]})]})})},_i=({images:s,alt:t})=>{const[a,r]=y.useState(0),i=()=>{const d=a===0?s.length-1:a-1;r(d)},n=()=>{const d=a===s.length-1?0:a+1;r(d)},l=u=>{r(u)};return s.length===0?e.jsx("div",{className:"w-full h-96 bg-gray-200 flex items-center justify-center rounded-lg",children:e.jsx("span",{className:"text-gray-500",children:"No images available"})}):s.length===1?e.jsx("div",{className:"w-full rounded-lg overflow-hidden",children:e.jsx("img",{src:s[0],alt:t,className:"w-full h-96 object-cover",onError:u=>{const d=u.target;d.style.display="none";const m=d.parentElement;if(m&&!m.querySelector(".image-fallback")){const h=document.createElement("div");h.className="image-fallback w-full h-96 bg-gray-200 flex items-center justify-center text-gray-500 text-lg",h.textContent="Image Not Available",m.appendChild(h)}}})}):e.jsxs("div",{className:"relative w-full h-96",children:[e.jsx("div",{className:"absolute top-0 left-0 right-0 bottom-0 w-full h-full rounded-lg overflow-hidden",children:e.jsx("img",{src:s[a],alt:`${t} - Image ${a+1}`,className:"absolute w-full h-full object-cover transition-opacity duration-300",onError:u=>{const d=u.target;d.style.display="none";const m=d.parentElement;if(m&&!m.querySelector(".image-fallback")){const h=document.createElement("div");h.className="image-fallback w-full h-full bg-gray-200 flex items-center justify-center text-gray-500 text-lg",h.textContent="Image Not Available",m.appendChild(h)}}})}),e.jsx("div",{className:"absolute top-1/2 left-4 -translate-y-1/2",children:e.jsx("button",{onClick:i,className:"p-2 rounded-full bg-white bg-opacity-70 hover:bg-opacity-100 text-gray-800 transition-all","aria-label":"Previous image",children:e.jsx(gi,{size:24})})}),e.jsx("div",{className:"absolute top-1/2 right-4 -translate-y-1/2",children:e.jsx("button",{onClick:n,className:"p-2 rounded-full bg-white bg-opacity-70 hover:bg-opacity-100 text-gray-800 transition-all","aria-label":"Next image",children:e.jsx(yi,{size:24})})}),e.jsx("div",{className:"absolute bottom-4 left-1/2 -translate-x-1/2 flex space-x-2",children:s.map((u,d)=>e.jsx("button",{onClick:()=>l(d),className:`h-2.5 w-2.5 rounded-full transition-all ${d===a?"bg-white w-5":"bg-white bg-opacity-50"}`,"aria-label":`Go to image ${d+1}`},d))})]})},zi=({hourlyRate:s})=>{const t=s*8,a=t*5,r=a*4,i=[{period:"Hour",rate:s,icon:e.jsx(We,{size:18})},{period:"Day",rate:t,icon:e.jsx(he,{size:18})},{period:"Week",rate:a,icon:e.jsx(he,{size:18})},{period:"Month",rate:r,icon:e.jsx(he,{size:18})}];return e.jsxs("div",{className:"mt-4",children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-3",children:"Pricing"}),e.jsx("div",{className:"grid grid-cols-2 gap-3 sm:grid-cols-4",children:i.map(n=>e.jsxs("div",{className:"bg-white p-3 rounded-lg border border-gray-200 shadow-sm",children:[e.jsxs("div",{className:"flex items-center text-sm text-gray-500 mb-1",children:[n.icon,e.jsxs("span",{className:"ml-1",children:["Per ",n.period]})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(qs,{size:18,className:"text-primary-600"}),e.jsx("span",{className:"text-xl font-semibold text-gray-900",children:Fe(n.rate)})]})]},n.period))}),e.jsx("p",{className:"text-sm text-gray-500 mt-2",children:"Actual rates may vary. Discuss with the owner for exact pricing."})]})},Oi=({ownerPhone:s="**********",ownerId:t,buttonStyle:a="full"})=>{const{user:r,isAuthenticated:i}=ee(),[n,l]=y.useState(!1),u=()=>{i&&(r==null?void 0:r.role)==="client"&&(r==null?void 0:r.licenseVerificationStatus)==="verified"&&l(!0)};return i?(r==null?void 0:r.role)!=="client"?e.jsx("div",{className:"mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg",children:e.jsxs("div",{className:"flex items-start",children:[e.jsx(oe,{size:20,className:"text-yellow-500 mt-0.5 flex-shrink-0"}),e.jsxs("div",{className:"ml-3",children:[e.jsx("h3",{className:"font-medium text-yellow-800",children:"Owner account detected"}),e.jsx("p",{className:"text-sm text-yellow-700 mt-1",children:"You need a client account to contact car owners."})]})]})}):(r==null?void 0:r.licenseVerificationStatus)!=="verified"?e.jsx("div",{className:"mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg",children:e.jsxs("div",{className:"flex items-start",children:[e.jsx(oe,{size:20,className:"text-yellow-500 mt-0.5 flex-shrink-0"}),e.jsxs("div",{className:"ml-3",children:[e.jsx("h3",{className:"font-medium text-yellow-800",children:"License verification required"}),e.jsx("p",{className:"text-sm text-yellow-700 mt-1",children:"Your driving license needs to be verified before you can contact car owners."}),e.jsx("div",{className:"mt-3",children:e.jsx(S,{variant:"primary",onClick:()=>window.location.href="/account/verification",children:"Verify License"})})]})]})}):a==="compact"?e.jsxs(S,{variant:"outline",onClick:u,className:"flex-1 flex items-center justify-center",disabled:n,children:[e.jsx(Se,{size:18,className:"mr-2"}),n?s:"Show Contact"]}):e.jsxs("div",{className:"mt-4",children:[n?e.jsx("div",{className:"p-4 bg-success-50 border border-success-200 rounded-lg",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(Se,{size:20,className:"text-success-500 flex-shrink-0"}),e.jsxs("div",{className:"ml-3",children:[e.jsx("h3",{className:"font-medium text-success-800",children:"Contact the owner"}),e.jsx("p",{className:"text-lg font-medium text-success-700 mt-1",children:s}),e.jsx("p",{className:"text-sm text-success-600 mt-2",children:"Call or text the owner to arrange rental details directly."})]})]})}):e.jsxs(S,{variant:"primary",onClick:u,className:"w-full flex items-center justify-center",children:[e.jsx(Se,{size:18,className:"mr-2"}),"Show Phone Number"]}),e.jsxs("p",{className:"text-sm text-gray-500 mt-2",children:[e.jsx(oe,{size:14,className:"inline mr-1"}),"We only connect you with the owner. All arrangements and payments are handled directly between you and the owner."]})]}):e.jsx("div",{className:"mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg",children:e.jsxs("div",{className:"flex items-start",children:[e.jsx(oe,{size:20,className:"text-yellow-500 mt-0.5 flex-shrink-0"}),e.jsxs("div",{className:"ml-3",children:[e.jsx("h3",{className:"font-medium text-yellow-800",children:"Authentication required"}),e.jsx("p",{className:"text-sm text-yellow-700 mt-1",children:"Please log in to contact the car owner."}),e.jsx("div",{className:"mt-3",children:e.jsx(S,{variant:"primary",onClick:()=>window.location.href="/login",children:"Log In"})})]})]})})},er=({recipientId:s,recipientName:t,itemType:a,itemId:r})=>{const{user:i,isAuthenticated:n}=ee(),[l,u]=y.useState([]),[d,m]=y.useState(""),[h,x]=y.useState(!1);y.useEffect(()=>{const b=[{id:`msg-${Date.now()}-1`,senderId:s,receiverId:(i==null?void 0:i.id)||"guest",text:`Hello! I'm ${t}. How can I help you?`,timestamp:new Date(Date.now()-36e5).toISOString(),isRead:!0}];u(b)},[s,t,i==null?void 0:i.id]);const w=b=>{b.preventDefault(),!(!d.trim()||!n)&&(x(!0),setTimeout(()=>{const c={id:`msg-${Date.now()}`,senderId:(i==null?void 0:i.id)||"guest",receiverId:s,text:d,timestamp:new Date().toISOString(),isRead:!1};u(p=>[...p,c]),m(""),x(!1),l.length<2&&setTimeout(()=>{const p={id:`msg-${Date.now()}-response`,senderId:s,receiverId:(i==null?void 0:i.id)||"guest",text:`Thanks for your message! I'll get back to you soon. Feel free to book ${a==="car"?"my car":"my services"} when you're ready.`,timestamp:new Date().toISOString(),isRead:!0};u(v=>[...v,p])},3e3)},500))};return e.jsxs("div",{className:"bg-white rounded-lg shadow-md overflow-hidden border border-gray-200",children:[e.jsxs("div",{className:"bg-primary-50 p-4 border-b border-gray-200",children:[e.jsxs("h3",{className:"font-medium text-primary-800",children:["Chat with ",t]}),e.jsx("p",{className:"text-sm text-gray-600",children:"Ask questions before booking"})]}),e.jsxs("div",{className:"h-64 overflow-y-auto p-4 space-y-3",children:[l.map(b=>e.jsx("div",{className:`flex ${b.senderId===(i==null?void 0:i.id)?"justify-end":"justify-start"}`,children:e.jsxs("div",{className:`max-w-[80%] rounded-lg px-3 py-2 ${b.senderId===(i==null?void 0:i.id)?"bg-primary-100 text-primary-800":"bg-gray-100 text-gray-800"}`,children:[e.jsx("div",{className:"text-sm",children:b.text}),e.jsx("div",{className:"text-xs text-gray-500 mt-1",children:new Date(b.timestamp).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})})]})},b.id)),l.length===0&&e.jsx("div",{className:"flex items-center justify-center h-full text-gray-500",children:"No messages yet. Start the conversation!"})]}),e.jsx("form",{onSubmit:w,className:"p-3 border-t border-gray-200",children:n?e.jsxs("div",{className:"flex",children:[e.jsx("input",{type:"text",placeholder:"Type your message...",className:"flex-grow px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500",value:d,onChange:b=>m(b.target.value),disabled:h}),e.jsx(S,{type:"submit",className:"rounded-l-none",disabled:h||!d.trim(),children:e.jsx(wi,{size:18})})]}):e.jsxs("div",{className:"text-center py-2 bg-gray-50 rounded-md",children:[e.jsx(je,{size:18,className:"inline-block mr-2 text-gray-500"}),e.jsx("span",{className:"text-sm text-gray-600",children:"Please log in to send messages"})]})})]})},Ui=()=>{const{id:s}=Fs(),t=de(),{getCarById:a,selectedCar:r,isLoading:i,error:n}=Ne(),{createBooking:l}=Ye(),{user:u,isAuthenticated:d}=ee(),[m,h]=y.useState(!1),[x,w]=y.useState(1),[b,c]=y.useState(""),[p,v]=y.useState(""),[E,R]=y.useState(!1),[I,B]=y.useState(null),[A,D]=y.useState(!1),f=()=>{t(-1)};return y.useEffect(()=>{window.scrollTo(0,0),s&&a(s)},[s,a]),d?i?e.jsx(M,{children:e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx(ce,{size:"lg"})})})}):n||!r?e.jsx(M,{children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs(S,{variant:"outline",className:"mb-4 flex items-center",onClick:f,children:[e.jsx(ve,{size:18,className:"mr-2"}),"Back to listings"]}),e.jsxs("div",{className:"bg-error-50 border border-error-200 rounded-lg p-4 text-center",children:[e.jsx("h2",{className:"text-xl font-medium text-error-800 mb-2",children:"Car Not Found"}),e.jsx("p",{className:"text-error-600",children:"The car you're looking for doesn't exist or has been removed."})]})]})}):e.jsx(M,{children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs(S,{variant:"outline",className:"mb-4 flex items-center",onClick:f,children:[e.jsx(ve,{size:18,className:"mr-2"}),"Back to listings"]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[e.jsxs("div",{className:"md:flex",children:[e.jsx("div",{className:"md:w-2/3",children:e.jsx(_i,{images:r.images,alt:`${r.year} ${r.make} ${r.model}`})}),e.jsxs("div",{className:"md:w-1/3 p-6 border-l border-gray-200",children:[e.jsxs("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:[r.year," ",r.make," ",r.model]}),e.jsxs("div",{className:"flex items-center text-gray-600 mb-4",children:[e.jsx(re,{size:18,className:"flex-shrink-0 mr-2"}),e.jsx("span",{children:r.location})]}),e.jsx("div",{className:"mb-4 pb-4 border-b border-gray-200",children:e.jsxs("div",{className:"flex items-center text-gray-600 mb-2",children:[e.jsx(he,{size:18,className:"mr-2 flex-shrink-0"}),e.jsxs("span",{children:["Availability: ",r.availabilityNotes]})]})}),e.jsx(zi,{hourlyRate:r.pricePerHour}),e.jsx("div",{className:"flex space-x-2 mt-4",children:e.jsx(Oi,{ownerId:r.ownerId})})]})]}),e.jsxs("div",{className:"p-6 border-t border-gray-200",children:[e.jsx("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"About This Car"}),e.jsx("p",{className:"text-gray-700 mb-6",children:r.description}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-3",children:"Features"}),e.jsx("ul",{className:"grid grid-cols-1 md:grid-cols-2 gap-2 mb-6",children:r.features.map((C,g)=>e.jsxs("li",{className:"flex items-center text-gray-700",children:[e.jsx($e,{size:16,className:"text-primary-600 mr-2 flex-shrink-0"}),C]},g))}),e.jsxs("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6",children:[e.jsx("h3",{className:"text-lg font-medium text-yellow-800 mb-2",children:"Important Information"}),e.jsx("p",{className:"text-yellow-700",children:"Park & Rent only facilitates the connection between car owners and renters. All arrangements, payments, and transactions are handled directly between users. We recommend chatting with the owner first, verifying insurance coverage, and discussing details thoroughly before booking."})]}),e.jsxs("div",{className:"mb-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"Chat with Owner"}),e.jsx(S,{variant:"outline",size:"sm",onClick:()=>h(!m),children:m?"Hide Chat":"Show Chat"})]}),m?e.jsx(er,{recipientId:r.ownerId,recipientName:"Car Owner",itemType:"car",itemId:r.id}):e.jsxs("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-6 text-center",children:[e.jsx(Kt,{size:32,className:"mx-auto text-gray-400 mb-2"}),e.jsx("p",{className:"text-gray-600",children:"Chat with the car owner to ask questions or discuss details before booking."})]})]}),e.jsxs("div",{className:"mt-6 pt-6 border-t border-gray-200",children:[e.jsx("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"Book this Car"}),A?e.jsxs("div",{className:"bg-success-50 border border-success-200 rounded-lg p-4 text-center",children:[e.jsx("h3",{className:"text-lg font-medium text-success-800 mb-2",children:"Booking Request Sent!"}),e.jsx("p",{className:"text-success-600 mb-4",children:"Your booking request has been submitted. The car owner will contact you soon to arrange payment and details."}),e.jsx(S,{onClick:()=>t("/account"),variant:"primary",children:"View My Bookings"})]}):e.jsxs("form",{onSubmit:C=>{if(C.preventDefault(),!d||!u){t("/login");return}R(!0),B(null);try{const g=new Date(`${b}T${p}`),N=new Date(g.getTime()+x*60*60*1e3),k=r.pricePerHour*x;l({userId:u.id,itemType:"car",itemId:r.id,startTime:g.toISOString(),endTime:N.toISOString(),totalPrice:k,status:"pending"}).then(()=>{D(!0),R(!1)})}catch(g){B("Failed to create booking. Please try again."),console.error(g),R(!1)}},className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"booking-date",className:"block text-sm font-medium text-gray-700 mb-1",children:"Date"}),e.jsx("input",{type:"date",id:"booking-date",className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500",required:!0,min:new Date().toISOString().split("T")[0],value:b,onChange:C=>c(C.target.value)})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"booking-time",className:"block text-sm font-medium text-gray-700 mb-1",children:"Start Time"}),e.jsx("input",{type:"time",id:"booking-time",className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500",required:!0,value:p,onChange:C=>v(C.target.value)})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"booking-hours",className:"block text-sm font-medium text-gray-700 mb-1",children:"Hours"}),e.jsx("select",{id:"booking-hours",className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500",value:x,onChange:C=>w(Number(C.target.value)),children:[1,2,3,4,5,6,7,8].map(C=>e.jsxs("option",{value:C,children:[C," ",C===1?"hour":"hours"]},C))})]})]}),e.jsxs("div",{className:"pt-4 border-t border-gray-200",children:[e.jsxs("div",{className:"flex justify-between mb-2",children:[e.jsx("span",{children:"Rate per hour"}),e.jsx("span",{children:Fe(r.pricePerHour)})]}),e.jsxs("div",{className:"flex justify-between mb-2",children:[e.jsx("span",{children:"Hours"}),e.jsx("span",{children:x})]}),e.jsxs("div",{className:"flex justify-between font-bold text-lg",children:[e.jsx("span",{children:"Total"}),e.jsx("span",{children:Fe(r.pricePerHour*x)})]})]}),I&&e.jsx("div",{className:"bg-error-50 border border-error-200 rounded-lg p-3 text-error-700 text-sm",children:I}),e.jsx("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-3 text-yellow-700 text-sm mb-4",children:e.jsxs("p",{children:[e.jsx("strong",{children:"Note:"})," This is just a booking request. No payment is required on the platform. You'll arrange payment directly with the car owner."]})}),e.jsxs(S,{type:"submit",fullWidth:!0,disabled:E||!b||!p,children:[E?e.jsx(ce,{size:"sm",className:"mr-2"}):null,"Request Booking"]}),!d&&e.jsx("div",{className:"text-sm text-center text-gray-500",children:"You'll need to log in to complete your booking."})]})]})]})]})]})}):e.jsx(M,{children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs(S,{variant:"outline",className:"mb-4 flex items-center",onClick:f,children:[e.jsx(ve,{size:18,className:"mr-2"}),"Back to listings"]}),e.jsxs("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-6 text-center",children:[e.jsx("h2",{className:"text-xl font-medium text-yellow-800 mb-2",children:"Login Required"}),e.jsx("p",{className:"text-yellow-700 mb-6",children:"You must be logged in to view car details and make bookings."}),e.jsxs("div",{className:"flex justify-center space-x-4",children:[e.jsx(S,{onClick:()=>t("/login"),children:"Log In"}),e.jsx(S,{variant:"outline",onClick:()=>t("/signup"),children:"Sign Up"})]})]})]})})},Mi=({onSearch:s})=>{const[t,a]=y.useState(""),[r,i]=y.useState(!1),n=u=>{u.preventDefault(),s(t)},l=()=>{i(!r)};return e.jsx("div",{className:"bg-white rounded-lg shadow-md p-4 mb-6",children:e.jsxs("form",{onSubmit:n,children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsxs("div",{className:"relative flex-grow",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:e.jsx(ps,{size:18,className:"text-gray-400"})}),e.jsx("input",{type:"text",placeholder:"Search by name, location, or specialty...",className:"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500",value:t,onChange:u=>a(u.target.value)})]}),e.jsx(S,{type:"submit",className:"ml-3",children:"Search"}),e.jsxs(S,{type:"button",variant:"outline",className:"ml-2 flex items-center",onClick:l,children:[e.jsx(Wt,{size:18,className:"mr-1"}),"Filters"]})]}),r&&e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 pt-4 border-t border-gray-200",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"min-experience",className:"block text-sm font-medium text-gray-700 mb-1",children:"Min. Experience (years)"}),e.jsxs("select",{id:"min-experience",className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500",children:[e.jsx("option",{value:"",children:"Any"}),e.jsx("option",{value:"1",children:"1+ years"}),e.jsx("option",{value:"3",children:"3+ years"}),e.jsx("option",{value:"5",children:"5+ years"}),e.jsx("option",{value:"10",children:"10+ years"})]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"specialty",className:"block text-sm font-medium text-gray-700 mb-1",children:"Specialty"}),e.jsxs("select",{id:"specialty",className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500",children:[e.jsx("option",{value:"",children:"All Specialties"}),e.jsx("option",{value:"city-driving",children:"City Driving"}),e.jsx("option",{value:"long-distance",children:"Long Distance"}),e.jsx("option",{value:"airport-transfers",children:"Airport Transfers"}),e.jsx("option",{value:"event-transportation",children:"Event Transportation"}),e.jsx("option",{value:"luxury-vehicles",children:"Luxury Vehicles"})]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"rating",className:"block text-sm font-medium text-gray-700 mb-1",children:"Min. Rating"}),e.jsxs("select",{id:"rating",className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500",children:[e.jsx("option",{value:"",children:"Any Rating"}),e.jsx("option",{value:"3",children:"3+ Stars"}),e.jsx("option",{value:"4",children:"4+ Stars"}),e.jsx("option",{value:"4.5",children:"4.5+ Stars"})]})]})]})]})})},Bi=()=>{const{filteredDrivers:s,isLoading:t,error:a,fetchDrivers:r,filterDrivers:i}=Ee();y.useEffect(()=>{window.scrollTo(0,0)},[]);const n=()=>{r()},l=u=>{i(u)};return t?e.jsx(M,{children:e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx(ce,{size:"lg"})})})}):a?e.jsx(M,{children:e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs("div",{className:"bg-error-50 border border-error-200 rounded-lg p-6 text-center",children:[e.jsx("h2",{className:"text-xl font-medium text-error-800 mb-2",children:"Error Loading Drivers"}),e.jsx("p",{className:"text-error-600 mb-4",children:a}),e.jsxs(S,{onClick:n,disabled:t,className:"inline-flex items-center gap-2",children:[e.jsx(ct,{className:`h-4 w-4 ${t?"animate-spin":""}`}),t?"Retrying...":"Try Again"]})]})})}):e.jsx(M,{children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"mb-6 flex justify-between items-start",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Hire a Driver"}),e.jsx("p",{className:"text-gray-600",children:"Find professional drivers to help you get around safely and comfortably."})]}),e.jsxs(S,{onClick:n,disabled:t,variant:"outline",className:"inline-flex items-center gap-2",children:[e.jsx(ct,{className:`h-4 w-4 ${t?"animate-spin":""}`}),"Refresh"]})]}),e.jsx(Mi,{onSearch:l}),s.length>0?e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6",children:s.map(u=>e.jsx(Qt,{driver:u},u.id))}):e.jsxs("div",{className:"text-center py-16 bg-white rounded-lg shadow-sm border border-gray-200",children:[e.jsx(le,{size:48,className:"mx-auto text-gray-400 mb-4"}),e.jsx("h3",{className:"text-xl font-medium text-gray-900 mb-2",children:"No drivers match your search"}),e.jsx("p",{className:"text-gray-600",children:"Try adjusting your filters or search term to find available drivers."})]})]})})},Hi=()=>{const{id:s}=Fs(),t=de(),{getDriverById:a,selectedDriver:r,isLoading:i,error:n}=Ee(),{createBooking:l}=Ye(),{user:u,isAuthenticated:d}=ee(),[m,h]=y.useState(!1),[x,w]=y.useState(!1),[b,c]=y.useState(1),[p,v]=y.useState(""),[E,R]=y.useState(""),[I,B]=y.useState(!1),[A,D]=y.useState(null),[f,C]=y.useState(!1),g=()=>{t(-1)};if(y.useEffect(()=>{window.scrollTo(0,0),s&&a(s)},[s,a]),!d)return e.jsx(M,{children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs(S,{variant:"outline",className:"mb-4 flex items-center",onClick:g,children:[e.jsx(ve,{size:18,className:"mr-2"}),"Back to drivers"]}),e.jsxs("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-6 text-center",children:[e.jsx("h2",{className:"text-xl font-medium text-yellow-800 mb-2",children:"Login Required"}),e.jsx("p",{className:"text-yellow-700 mb-6",children:"You must be logged in to view driver details and make bookings."}),e.jsxs("div",{className:"flex justify-center space-x-4",children:[e.jsx(S,{onClick:()=>t("/login"),children:"Log In"}),e.jsx(S,{variant:"outline",onClick:()=>t("/signup"),children:"Sign Up"})]})]})]})});const N=async k=>{if(k.preventDefault(),!d||!u){t("/login");return}if(r){B(!0),D(null);try{const P=new Date(`${p}T${E}`),T=new Date(P.getTime()+b*60*60*1e3),U=r.pricePerHour*b;await l({userId:u.id,itemType:"driver",itemId:r.id,startTime:P.toISOString(),endTime:T.toISOString(),totalPrice:U,status:"pending"}),C(!0)}catch(P){D("Failed to create booking. Please try again."),console.error(P)}finally{B(!1)}}};return i?e.jsx(M,{children:e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx(ce,{size:"lg"})})})}):n||!r?e.jsx(M,{children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs(S,{variant:"outline",className:"mb-4 flex items-center",onClick:g,children:[e.jsx(ve,{size:18,className:"mr-2"}),"Back to drivers"]}),e.jsxs("div",{className:"bg-error-50 border border-error-200 rounded-lg p-4 text-center",children:[e.jsx("h2",{className:"text-xl font-medium text-error-800 mb-2",children:"Driver Not Found"}),e.jsx("p",{className:"text-error-600",children:"The driver you're looking for doesn't exist or has been removed."})]})]})}):e.jsx(M,{children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs(S,{variant:"outline",className:"mb-4 flex items-center",onClick:g,children:[e.jsx(ve,{size:18,className:"mr-2"}),"Back to drivers"]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[e.jsxs("div",{className:"md:flex",children:[e.jsxs("div",{className:"md:w-2/3",children:[e.jsxs("div",{className:"relative h-80",children:[e.jsx("img",{src:r.profileImage,alt:r.name,className:"w-full h-full object-cover"}),e.jsxs("div",{className:"absolute bottom-4 right-4 bg-white px-3 py-1 rounded-full flex items-center shadow-md",children:[e.jsx(Bs,{size:18,className:"text-yellow-500 mr-1"}),e.jsx("span",{className:"font-medium",children:Number(r.rating||0).toFixed(1)}),e.jsxs("span",{className:"text-sm text-gray-500 ml-1",children:["(",r.reviews||0," reviews)"]})]})]}),e.jsxs("div",{className:"p-6",children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:r.name}),e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx(Yt,{size:18,className:"text-primary-600 mr-2"}),e.jsxs("span",{children:[r.experience," years of professional driving experience"]})]}),e.jsxs("div",{className:"mb-4 space-y-3",children:[e.jsxs("div",{className:"flex items-center text-gray-600",children:[e.jsx(re,{size:18,className:"flex-shrink-0 mr-2"}),e.jsx("span",{children:r.location})]}),e.jsxs("div",{className:"flex items-center text-gray-600",children:[e.jsx(We,{size:18,className:"flex-shrink-0 mr-2"}),e.jsx("span",{children:r.availabilityNotes})]})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("h2",{className:"text-lg font-semibold mb-2",children:"Specialties"}),e.jsx("div",{className:"flex flex-wrap gap-2",children:r.specialties.map((k,P)=>e.jsx("span",{className:"inline-block bg-gray-100 rounded-full px-3 py-1 text-sm font-medium text-gray-700",children:k},P))})]})]})]}),e.jsxs("div",{className:"md:w-1/3 p-6 border-l border-gray-200",children:[e.jsxs("div",{className:"mb-4 pb-4 border-b border-gray-200",children:[e.jsx("div",{className:"text-2xl font-bold text-primary-600 mb-1",children:Hs(r.pricePerHour,"hour")}),e.jsx("div",{className:"text-sm text-gray-500",children:"Book this driver by the hour"})]}),e.jsxs("div",{className:"flex space-x-2 mb-4",children:[e.jsxs(S,{variant:"primary",className:"flex-1 flex items-center justify-center",onClick:()=>h(!m),children:[e.jsx(Kt,{size:18,className:"mr-2"}),m?"Hide Chat":"Chat with Driver"]}),x?e.jsxs(S,{variant:"outline",className:"flex-1 flex items-center justify-center",disabled:!0,children:[e.jsx(Se,{size:18,className:"mr-2"}),u!=null&&u.isPhoneVerified?"**********":"Verify account first"]}):e.jsxs(S,{variant:"outline",className:"flex-1 flex items-center justify-center",onClick:()=>w(!0),children:[e.jsx(Se,{size:18,className:"mr-2"}),"Show Contact"]})]}),m&&e.jsx("div",{className:"mb-4",children:e.jsx(er,{recipientId:r.userId,recipientName:r.name,itemType:"driver",itemId:r.id})})]})]}),e.jsxs("div",{className:"mt-6 p-6 border-t border-gray-200",children:[e.jsx("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"Book this Driver"}),f?e.jsxs("div",{className:"bg-success-50 border border-success-200 rounded-lg p-4 text-center mb-4",children:[e.jsx("h3",{className:"text-lg font-medium text-success-800 mb-2",children:"Booking Request Sent!"}),e.jsx("p",{className:"text-success-600 mb-4",children:"Your booking request has been submitted. The driver will contact you soon to arrange payment and details."}),e.jsx(S,{onClick:()=>t("/account"),fullWidth:!0,children:"View My Bookings"})]}):e.jsxs("form",{onSubmit:N,className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"booking-date",className:"block text-sm font-medium text-gray-700 mb-1",children:"Date"}),e.jsx("input",{type:"date",id:"booking-date",className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500",required:!0,min:new Date().toISOString().split("T")[0],value:p,onChange:k=>v(k.target.value)})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"booking-time",className:"block text-sm font-medium text-gray-700 mb-1",children:"Start Time"}),e.jsx("input",{type:"time",id:"booking-time",className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500",required:!0,value:E,onChange:k=>R(k.target.value)})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"booking-hours",className:"block text-sm font-medium text-gray-700 mb-1",children:"Hours"}),e.jsx("select",{id:"booking-hours",className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500",value:b,onChange:k=>c(Number(k.target.value)),children:[1,2,3,4,5,6,7,8].map(k=>e.jsxs("option",{value:k,children:[k," ",k===1?"hour":"hours"]},k))})]})]}),e.jsxs("div",{className:"pt-4 border-t border-gray-200",children:[e.jsxs("div",{className:"flex justify-between mb-2",children:[e.jsx("span",{children:"Rate per hour"}),e.jsx("span",{children:Fe(r.pricePerHour)})]}),e.jsxs("div",{className:"flex justify-between mb-2",children:[e.jsx("span",{children:"Hours"}),e.jsx("span",{children:b})]}),e.jsxs("div",{className:"flex justify-between font-bold text-lg",children:[e.jsx("span",{children:"Total"}),e.jsx("span",{children:Fe(r.pricePerHour*b)})]})]}),A&&e.jsx("div",{className:"bg-error-50 border border-error-200 rounded-lg p-3 text-error-700 text-sm",children:A}),e.jsx("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-3 text-yellow-700 text-sm mb-4",children:e.jsxs("p",{children:[e.jsx("strong",{children:"Note:"})," This is just a booking request. No payment is required on the platform. You'll arrange payment directly with the driver."]})}),e.jsxs(S,{type:"submit",fullWidth:!0,disabled:I||!p||!E,children:[I?e.jsx(ce,{size:"sm",className:"mr-2"}):null,"Request Booking"]}),!d&&e.jsx("div",{className:"text-sm text-center text-gray-500",children:"You'll need to log in to complete your booking."})]})]})]})]})})},qi=()=>{const s=de(),{addDriver:t}=Ee(),{user:a,isAuthenticated:r}=ee(),[i,n]=y.useState({name:(a==null?void 0:a.name)||"",age:"",experience:"",licenseNumber:"",location:"",pricePerHour:"",specialties:[],availabilityNotes:""}),[l,u]=y.useState(""),[d,m]=y.useState(null),[h,x]=y.useState(!1),[w,b]=y.useState(null),[c,p]=y.useState(!1),v=["City Driving","Long Distance","Airport Transfers","Event Transportation","Tourist Guide","Luxury Vehicles","Corporate Events"],E=A=>{const{name:D,value:f}=A.target;n(C=>({...C,[D]:f}))},R=A=>{n(D=>{const f=[...D.specialties];return f.includes(A)?{...D,specialties:f.filter(C=>C!==A)}:{...D,specialties:[...f,A]}})},I=A=>{var f;const D=(f=A.target.files)==null?void 0:f[0];if(D){m(D);const C=new FileReader;C.onload=g=>{var N;u((N=g.target)==null?void 0:N.result)},C.readAsDataURL(D)}},B=async A=>{if(A.preventDefault(),!r||!a){s("/login");return}x(!0),b(null);try{if(i.specialties.length===0)throw new Error("Please select at least one specialty");await t({userId:a.id,name:i.name,age:parseInt(i.age),experience:parseInt(i.experience),profileImage:"",licenseNumber:i.licenseNumber,licenseVerificationStatus:"pending",location:i.location,pricePerHour:parseFloat(i.pricePerHour),rating:0,reviews:0,specialties:i.specialties,availabilityNotes:i.availabilityNotes,isAvailable:!0},d||void 0),p(!0),n({name:"",age:"",experience:"",licenseNumber:"",location:"",pricePerHour:"",specialties:[],availabilityNotes:""})}catch(D){b(D instanceof Error?D.message:"Failed to register as a driver"),console.error(D)}finally{x(!1)}};return e.jsx(M,{children:e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs("div",{className:"max-w-2xl mx-auto",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Register as a Driver"}),e.jsx("p",{className:"text-gray-600 mb-6",children:"Fill out the form below to register as a driver on our platform."}),c?e.jsxs("div",{className:"bg-success-50 border border-success-200 rounded-lg p-6 text-center",children:[e.jsx("div",{className:"inline-flex items-center justify-center w-12 h-12 rounded-full bg-success-100 text-success-600 mb-4",children:e.jsx(Is,{size:24})}),e.jsx("h2",{className:"text-xl font-medium text-success-800 mb-2",children:"Registration Successful!"}),e.jsx("p",{className:"text-success-600 mb-6",children:"Your driver profile has been created. Your license verification is pending."}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[e.jsx(S,{onClick:()=>s("/drivers"),children:"Browse Drivers"}),e.jsx(S,{variant:"outline",onClick:()=>s("/account"),children:"Go to My Account"})]})]}):e.jsxs("form",{onSubmit:B,className:"bg-white rounded-lg shadow-md p-6",children:[w&&e.jsxs("div",{className:"mb-6 bg-error-50 border border-error-200 rounded-lg p-4 flex items-start",children:[e.jsx(oe,{size:20,className:"text-error-600 mr-2 flex-shrink-0 mt-0.5"}),e.jsx("div",{className:"text-error-700",children:w})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Profile Image"}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-20 h-20 rounded-full overflow-hidden mr-4 bg-gray-100",children:l?e.jsx("img",{src:l,alt:"Profile",className:"w-full h-full object-cover"}):e.jsx("div",{className:"w-full h-full flex items-center justify-center text-gray-400",children:e.jsx(fs,{size:24})})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500 mb-2",children:"Upload a professional photo of yourself."}),e.jsxs(S,{type:"button",variant:"outline",size:"sm",className:"relative overflow-hidden",children:["Upload Photo",e.jsx("input",{type:"file",className:"absolute inset-0 opacity-0 cursor-pointer",accept:"image/*",onChange:I})]})]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-1",children:"Full Name"}),e.jsx("input",{type:"text",id:"name",name:"name",required:!0,className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500",value:i.name,onChange:E})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"age",className:"block text-sm font-medium text-gray-700 mb-1",children:"Age"}),e.jsx("input",{type:"number",id:"age",name:"age",min:"18",required:!0,className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500",value:i.age,onChange:E})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"experience",className:"block text-sm font-medium text-gray-700 mb-1",children:"Driving Experience (years)"}),e.jsx("input",{type:"number",id:"experience",name:"experience",min:"1",required:!0,className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500",value:i.experience,onChange:E})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"licenseNumber",className:"block text-sm font-medium text-gray-700 mb-1",children:"Driver's License Number"}),e.jsx("input",{type:"text",id:"licenseNumber",name:"licenseNumber",required:!0,className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500",value:i.licenseNumber,onChange:E})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"location",className:"block text-sm font-medium text-gray-700 mb-1",children:"Location"}),e.jsx("input",{type:"text",id:"location",name:"location",required:!0,className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500",value:i.location,onChange:E})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"pricePerHour",className:"block text-sm font-medium text-gray-700 mb-1",children:"Hourly Rate ($)"}),e.jsx("input",{type:"number",id:"pricePerHour",name:"pricePerHour",min:"10",step:"0.01",required:!0,className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500",value:i.pricePerHour,onChange:E})]})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Specialties"}),e.jsx("div",{className:"grid grid-cols-2 sm:grid-cols-3 gap-2",children:v.map(A=>e.jsxs("div",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",id:`specialty-${A}`,className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded",checked:i.specialties.includes(A),onChange:()=>R(A)}),e.jsx("label",{htmlFor:`specialty-${A}`,className:"ml-2 text-sm text-gray-700",children:A})]},A))})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{htmlFor:"availabilityNotes",className:"block text-sm font-medium text-gray-700 mb-1",children:"Availability Notes"}),e.jsx("textarea",{id:"availabilityNotes",name:"availabilityNotes",rows:3,required:!0,className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500",placeholder:"e.g., Available weekdays from 9 AM to 5 PM",value:i.availabilityNotes,onChange:E})]}),e.jsx("div",{className:"flex justify-end",children:e.jsxs(S,{type:"submit",disabled:h,children:[h?e.jsx(ce,{size:"sm",className:"mr-2"}):null,"Register as Driver"]})})]})]})})})},dt=()=>{const s=de(),{user:t}=ee(),{drivers:a,toggleDriverAvailability:r,updateDriver:i}=Ee(),{bookings:n,updateBookingStatus:l}=Ye(),[u,d]=y.useState(!1),[m,h]=y.useState("bookings"),x=a.find(p=>p.userId===(t==null?void 0:t.id)),w=n.filter(p=>p.itemType==="driver"&&p.itemId===(x==null?void 0:x.id)),b=async()=>{if(x){d(!0);try{await r(x.id)}catch(p){console.error("Failed to toggle availability:",p)}finally{d(!1)}}},c=async(p,v)=>{d(!0);try{await l(p,v)}catch(E){console.error("Failed to update booking status:",E)}finally{d(!1)}};return!t||t.role!=="driver"?e.jsx(M,{children:e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs("div",{className:"bg-error-50 border border-error-200 rounded-lg p-4 text-center",children:[e.jsx("h2",{className:"text-xl font-medium text-error-800 mb-2",children:"Access Denied"}),e.jsx("p",{className:"text-error-600",children:"You must be logged in as a driver to access this page."}),e.jsx(S,{className:"mt-4",onClick:()=>s("/login"),children:"Log In"})]})})}):x?e.jsx(M,{children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-8",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Driver Dashboard"}),e.jsx("p",{className:"text-gray-600",children:"Manage your driver profile and booking requests."})]}),e.jsxs("div",{className:"mt-4 md:mt-0 flex items-center",children:[e.jsxs("div",{className:`mr-4 px-4 py-2 rounded-full flex items-center ${x.isAvailable?"bg-success-100 text-success-800":"bg-gray-100 text-gray-800"}`,children:[e.jsx("span",{className:"mr-2",children:"Status:"}),e.jsx("span",{className:"font-medium",children:x.isAvailable?"Available":"Unavailable"})]}),e.jsxs(S,{variant:x.isAvailable?"outline":"primary",onClick:b,disabled:u,children:[u?e.jsx(ce,{size:"sm",className:"mr-2"}):e.jsx(Zt,{size:18,className:"mr-2"}),x.isAvailable?"Go Offline":"Go Online"]})]})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-md overflow-hidden mb-8",children:[e.jsxs("div",{className:"flex border-b border-gray-200",children:[e.jsx("button",{className:`px-6 py-3 font-medium text-sm focus:outline-none ${m==="bookings"?"text-primary-600 border-b-2 border-primary-600":"text-gray-500 hover:text-gray-700"}`,onClick:()=>h("bookings"),children:"Booking Requests"}),e.jsx("button",{className:`px-6 py-3 font-medium text-sm focus:outline-none ${m==="profile"?"text-primary-600 border-b-2 border-primary-600":"text-gray-500 hover:text-gray-700"}`,onClick:()=>h("profile"),children:"Driver Profile"})]}),m==="bookings"&&e.jsxs("div",{className:"p-6",children:[e.jsx("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"Your Booking Requests"}),w.length===0?e.jsxs("div",{className:"text-center py-8 border border-dashed border-gray-300 rounded-lg",children:[e.jsx(he,{size:48,className:"mx-auto text-gray-400 mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No booking requests yet"}),e.jsx("p",{className:"text-gray-600",children:"When clients book your services, their requests will appear here."})]}):e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Client"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date & Time"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Duration"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Price"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:w.map(p=>{const v=new Date(p.startTime),R=(new Date(p.endTime).getTime()-v.getTime())/(1e3*60*60);return e.jsxs("tr",{children:[e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(le,{size:24,className:"text-gray-400 mr-2"}),e.jsxs("div",{className:"text-sm font-medium text-gray-900",children:["Client #",p.userId.slice(-4)]})]})}),e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap",children:[e.jsx("div",{className:"text-sm text-gray-900",children:v.toLocaleDateString()}),e.jsx("div",{className:"text-sm text-gray-500",children:v.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})})]}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("div",{className:"text-sm text-gray-900",children:[R," ",R===1?"hour":"hours"]})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("div",{className:"text-sm font-medium text-gray-900",children:["$",p.totalPrice.toFixed(2)]})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("span",{className:`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${p.status==="pending"?"bg-yellow-100 text-yellow-800":p.status==="confirmed"?"bg-blue-100 text-blue-800":p.status==="completed"?"bg-success-100 text-success-800":"bg-error-100 text-error-800"}`,children:p.status.charAt(0).toUpperCase()+p.status.slice(1)})}),e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:[p.status==="pending"&&e.jsxs("div",{className:"flex space-x-2",children:[e.jsx("button",{className:"text-success-600 hover:text-success-800",onClick:()=>c(p.id,"confirmed"),children:e.jsx(Te,{size:18})}),e.jsx("button",{className:"text-error-600 hover:text-error-800",onClick:()=>c(p.id,"cancelled"),children:e.jsx(He,{size:18})})]}),p.status==="confirmed"&&e.jsx("button",{className:"text-success-600 hover:text-success-800",onClick:()=>c(p.id,"completed"),children:"Complete"})]})]},p.id)})})]})})]}),m==="profile"&&e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:"flex flex-col md:flex-row",children:[e.jsx("div",{className:"md:w-1/3 mb-6 md:mb-0 md:pr-6",children:e.jsxs("div",{className:"bg-gray-100 rounded-lg p-6 text-center",children:[e.jsx("div",{className:"w-32 h-32 mx-auto rounded-full overflow-hidden mb-4",children:e.jsx("img",{src:x.profileImage,alt:x.name,className:"w-full h-full object-cover"})}),e.jsx("h3",{className:"text-xl font-bold text-gray-900 mb-1",children:x.name}),e.jsxs("div",{className:"flex items-center justify-center mb-4",children:[e.jsx(Bs,{size:16,className:"text-yellow-500 mr-1"}),e.jsx("span",{className:"font-medium",children:Number(x.rating||0).toFixed(1)}),e.jsxs("span",{className:"text-xs text-gray-500 ml-1",children:["(",x.reviews||0," reviews)"]})]}),e.jsx(O,{to:"/driver/edit-profile",children:e.jsxs(S,{variant:"outline",className:"w-full flex items-center justify-center",children:[e.jsx(Ie,{size:16,className:"mr-2"}),"Edit Profile"]})})]})}),e.jsxs("div",{className:"md:w-2/3",children:[e.jsx("h3",{className:"text-lg font-bold text-gray-900 mb-4",children:"Driver Information"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6",children:[e.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[e.jsx("div",{className:"text-sm text-gray-500 mb-1",children:"Age"}),e.jsxs("div",{className:"font-medium",children:[x.age," years"]})]}),e.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[e.jsx("div",{className:"text-sm text-gray-500 mb-1",children:"Experience"}),e.jsxs("div",{className:"font-medium",children:[x.experience," years"]})]}),e.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[e.jsx("div",{className:"text-sm text-gray-500 mb-1",children:"License Number"}),e.jsx("div",{className:"font-medium",children:x.licenseNumber})]}),e.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[e.jsx("div",{className:"text-sm text-gray-500 mb-1",children:"License Status"}),e.jsx("div",{className:`font-medium ${x.licenseVerificationStatus==="verified"?"text-success-600":x.licenseVerificationStatus==="pending"?"text-yellow-600":"text-error-600"}`,children:x.licenseVerificationStatus.charAt(0).toUpperCase()+x.licenseVerificationStatus.slice(1)})]})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("h4",{className:"text-md font-bold text-gray-900 mb-2",children:"Location"}),e.jsxs("div",{className:"flex items-center text-gray-700",children:[e.jsx(re,{size:18,className:"mr-2 text-gray-500"}),x.location]})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("h4",{className:"text-md font-bold text-gray-900 mb-2",children:"Availability"}),e.jsxs("div",{className:"flex items-center text-gray-700",children:[e.jsx(We,{size:18,className:"mr-2 text-gray-500"}),x.availabilityNotes]})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("h4",{className:"text-md font-bold text-gray-900 mb-2",children:"Hourly Rate"}),e.jsxs("div",{className:"flex items-center text-gray-700",children:[e.jsx(hs,{size:18,className:"mr-2 text-gray-500"}),"$",x.pricePerHour,"/hour"]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-md font-bold text-gray-900 mb-2",children:"Specialties"}),e.jsx("div",{className:"flex flex-wrap gap-2",children:x.specialties.map((p,v)=>e.jsx("span",{className:"inline-block bg-gray-100 rounded-full px-3 py-1 text-sm font-medium text-gray-700",children:p},v))})]})]})]})})]})]})}):e.jsx(M,{children:e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs("div",{className:"bg-warning-50 border border-warning-200 rounded-lg p-4 text-center",children:[e.jsx("h2",{className:"text-xl font-medium text-warning-800 mb-2",children:"Driver Profile Not Found"}),e.jsx("p",{className:"text-warning-600",children:"You need to complete your driver registration first."}),e.jsx(S,{className:"mt-4",onClick:()=>s("/driver/register"),children:"Complete Registration"})]})})})},$i=()=>{const s=de(),{user:t}=ee(),{cars:a}=Ne(),{drivers:r}=Ee(),{bookings:i,cancelBooking:n}=Ye(),[l,u]=y.useState(!1),[d,m]=y.useState("bookings"),h=t?i.filter(b=>b.userId===t.id):[],x=async b=>{u(!0);try{await n(b)}catch(c){console.error("Failed to cancel booking:",c)}finally{u(!1)}},w=b=>{if(b.itemType==="car"){const c=a.find(p=>p.id===b.itemId);return c?{name:`${c.year} ${c.make} ${c.model}`,image:c.images[0],location:c.location,price:c.pricePerHour}:null}else{const c=r.find(p=>p.id===b.itemId);return c?{name:c.name,image:c.profileImage,location:c.location,price:c.pricePerHour}:null}};return!t||t.role!=="client"?e.jsx(M,{children:e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs("div",{className:"bg-error-50 border border-error-200 rounded-lg p-4 text-center",children:[e.jsx("h2",{className:"text-xl font-medium text-error-800 mb-2",children:"Access Denied"}),e.jsx("p",{className:"text-error-600",children:"You must be logged in as a client to access this page."}),e.jsx(S,{className:"mt-4",onClick:()=>s("/login"),children:"Log In"})]})})}):e.jsx(M,{children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-8",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"My Account"}),e.jsx("p",{className:"text-gray-600",children:"Manage your bookings and account information."})]}),e.jsxs("div",{className:"mt-4 md:mt-0 flex space-x-3",children:[e.jsx(O,{to:"/cars",children:e.jsxs(S,{variant:"outline",children:[e.jsx(Z,{size:18,className:"mr-2"}),"Browse Cars"]})}),e.jsx(O,{to:"/drivers",children:e.jsxs(S,{variant:"outline",children:[e.jsx(le,{size:18,className:"mr-2"}),"Hire Drivers"]})})]})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-md overflow-hidden mb-8",children:[e.jsxs("div",{className:"flex border-b border-gray-200",children:[e.jsx("button",{className:`px-6 py-3 font-medium text-sm focus:outline-none ${d==="bookings"?"text-primary-600 border-b-2 border-primary-600":"text-gray-500 hover:text-gray-700"}`,onClick:()=>m("bookings"),children:"My Bookings"}),e.jsx("button",{className:`px-6 py-3 font-medium text-sm focus:outline-none ${d==="profile"?"text-primary-600 border-b-2 border-primary-600":"text-gray-500 hover:text-gray-700"}`,onClick:()=>m("profile"),children:"Profile"})]}),d==="bookings"&&e.jsxs("div",{className:"p-6",children:[e.jsx("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"Your Bookings"}),h.length===0?e.jsxs("div",{className:"text-center py-8 border border-dashed border-gray-300 rounded-lg",children:[e.jsx(he,{size:48,className:"mx-auto text-gray-400 mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No bookings yet"}),e.jsx("p",{className:"text-gray-600 mb-6",children:"You haven't made any bookings yet. Browse our cars and drivers to get started."}),e.jsxs("div",{className:"flex flex-col sm:flex-row justify-center gap-3",children:[e.jsx(O,{to:"/cars",children:e.jsx(S,{children:"Browse Cars"})}),e.jsx(O,{to:"/drivers",children:e.jsx(S,{variant:"outline",children:"Hire Drivers"})})]})]}):e.jsx("div",{className:"grid grid-cols-1 gap-6",children:h.map(b=>{const c=w(b),p=new Date(b.startTime),v=new Date(b.endTime),E=(v.getTime()-p.getTime())/(1e3*60*60);return c?e.jsx("div",{className:"border border-gray-200 rounded-lg overflow-hidden",children:e.jsxs("div",{className:"flex flex-col md:flex-row",children:[e.jsx("div",{className:"md:w-1/4 h-48 md:h-auto",children:e.jsx("img",{src:c.image,alt:c.name,className:"w-full h-full object-cover"})}),e.jsxs("div",{className:"p-6 md:w-3/4",children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between mb-4",children:[e.jsxs("div",{children:[e.jsxs("h3",{className:"text-lg font-bold text-gray-900 mb-1",children:[b.itemType==="car"?"Car Rental: ":"Driver Hire: ",c.name]}),e.jsxs("div",{className:"flex items-center text-gray-600 mb-2",children:[e.jsx(re,{size:16,className:"mr-1"}),e.jsx("span",{className:"text-sm",children:c.location})]})]}),e.jsx("div",{className:"mt-2 md:mt-0",children:e.jsx("span",{className:`px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${b.status==="pending"?"bg-yellow-100 text-yellow-800":b.status==="confirmed"?"bg-blue-100 text-blue-800":b.status==="completed"?"bg-success-100 text-success-800":"bg-error-100 text-error-800"}`,children:b.status.charAt(0).toUpperCase()+b.status.slice(1)})})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-gray-500 mb-1",children:"Date"}),e.jsx("div",{className:"font-medium",children:p.toLocaleDateString()})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-gray-500 mb-1",children:"Time"}),e.jsxs("div",{className:"font-medium",children:[p.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})," -",v.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})]})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-gray-500 mb-1",children:"Duration"}),e.jsxs("div",{className:"font-medium",children:[E," ",E===1?"hour":"hours"]})]})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{className:"font-bold text-lg text-primary-600",children:["$",b.totalPrice.toFixed(2)]}),(b.status==="pending"||b.status==="confirmed")&&e.jsxs(S,{variant:"outline",className:"text-error-600 border-error-600 hover:bg-error-50",onClick:()=>x(b.id),disabled:l,children:[l?e.jsx(ce,{size:"sm",className:"mr-2"}):e.jsx(He,{size:16,className:"mr-2"}),"Cancel Booking"]})]})]})]})},b.id):null})})]}),d==="profile"&&e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:"flex flex-col md:flex-row",children:[e.jsx("div",{className:"md:w-1/3 mb-6 md:mb-0 md:pr-6",children:e.jsxs("div",{className:"bg-gray-100 rounded-lg p-6 text-center",children:[e.jsx("div",{className:"w-32 h-32 mx-auto rounded-full overflow-hidden mb-4 bg-gray-300 flex items-center justify-center",children:t.licenseImageUrl?e.jsx("img",{src:t.licenseImageUrl,alt:t.name,className:"w-full h-full object-cover"}):e.jsx(le,{size:64,className:"text-gray-400"})}),e.jsx("h3",{className:"text-xl font-bold text-gray-900 mb-1",children:t.name}),e.jsx("p",{className:"text-gray-500 mb-4",children:t.email}),e.jsx(O,{to:"/account/edit-profile",children:e.jsxs(S,{variant:"outline",className:"w-full flex items-center justify-center",children:[e.jsx(Ie,{size:16,className:"mr-2"}),"Edit Profile"]})})]})}),e.jsxs("div",{className:"md:w-2/3",children:[e.jsx("h3",{className:"text-lg font-bold text-gray-900 mb-4",children:"Account Information"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6",children:[e.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[e.jsx("div",{className:"text-sm text-gray-500 mb-1",children:"Account Type"}),e.jsx("div",{className:"font-medium",children:"Client"})]}),e.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[e.jsx("div",{className:"text-sm text-gray-500 mb-1",children:"Member Since"}),e.jsx("div",{className:"font-medium",children:new Date(t.createdAt).toLocaleDateString()})]}),e.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[e.jsx("div",{className:"text-sm text-gray-500 mb-1",children:"Phone Number"}),e.jsxs("div",{className:"font-medium",children:[t.phoneNumber||"Not provided",t.phoneNumber&&t.isPhoneVerified&&e.jsx("span",{className:"ml-2 text-xs bg-success-100 text-success-800 px-2 py-0.5 rounded-full",children:"Verified"})]})]}),e.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[e.jsx("div",{className:"text-sm text-gray-500 mb-1",children:"License Verification"}),e.jsx("div",{className:"font-medium",children:t.licenseVerificationStatus?e.jsx("span",{className:`${t.licenseVerificationStatus==="verified"?"text-success-600":t.licenseVerificationStatus==="pending"?"text-yellow-600":"text-error-600"}`,children:t.licenseVerificationStatus.charAt(0).toUpperCase()+t.licenseVerificationStatus.slice(1)}):"Not submitted"})]})]}),!t.licenseVerificationStatus&&e.jsx("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6",children:e.jsxs("div",{className:"flex",children:[e.jsx(oe,{size:20,className:"text-yellow-600 mr-2 flex-shrink-0"}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-yellow-800 mb-1",children:"Verify Your License"}),e.jsx("p",{className:"text-yellow-700 text-sm mb-3",children:"To rent cars, you need to verify your driver's license. This helps ensure safety and trust in our community."}),e.jsx(O,{to:"/account/verification",children:e.jsx(S,{size:"sm",children:"Verify License"})})]})]})}),e.jsxs("div",{className:"bg-gray-50 rounded-lg p-4",children:[e.jsx("h4",{className:"font-medium text-gray-900 mb-3",children:"Payment Methods"}),e.jsx("p",{className:"text-gray-600 mb-3",children:"Park & Rent doesn't handle payments directly. All payments are arranged between you and the car owner or driver."}),e.jsx("p",{className:"text-sm text-gray-500",children:"We recommend discussing payment methods and terms before confirming any booking."})]})]})]})})]})]})})},V=({id:s,label:t,type:a="text",placeholder:r,value:i,onChange:n,error:l,className:u="",required:d=!1,disabled:m=!1})=>e.jsxs("div",{className:`mb-4 ${u}`,children:[t&&e.jsxs("label",{htmlFor:s,className:"block text-sm font-medium text-gray-700 mb-1",children:[t," ",d&&e.jsx("span",{className:"text-error-600",children:"*"})]}),e.jsx("input",{id:s,type:a,placeholder:r,value:i,onChange:n,disabled:m,required:d,className:`w-full px-3 py-2 border ${l?"border-error-500":"border-gray-300"} rounded-md shadow-sm focus:outline-none focus:ring-2 ${l?"focus:ring-error-500 focus:border-error-500":"focus:ring-primary-500 focus:border-primary-500"} disabled:bg-gray-100 disabled:text-gray-500`}),l&&e.jsx("p",{className:"mt-1 text-sm text-error-600",children:l})]}),mt=s=>(s==null?void 0:s.role)==="admin",xt=()=>{const s=de(),{user:t}=ee(),{cars:a,updateCar:r}=Ne(),{drivers:i,updateDriver:n}=Ee(),{bookings:l,updateBookingStatus:u}=Ye(),{stats:d,users:m,cars:h,drivers:x,bookings:w,gpsRequests:b,isLoading:c,error:p,fetchDashboardStats:v,fetchUsers:E,fetchCars:R,fetchDrivers:I,fetchBookings:B,fetchGpsRequests:A,updateUserRole:D,verifyDriverLicense:f,updateGpsRequestStatus:C}=mi(),[g,N]=y.useState("overview"),[k,P]=y.useState(!1),[T,U]=y.useState(""),[F,q]=y.useState("all"),[X,ae]=y.useState(!1),[be,ie]=y.useState(!1),[sr,Je]=y.useState(!1),[tr,Re]=y.useState(!1),[rr,gs]=y.useState(!1);y.useState(!1);const[ar,ys]=y.useState(!1),[fe,ze]=y.useState(null),[xe,Ke]=y.useState(null),[bs,De]=y.useState(null),[$s,we]=y.useState(!1),[js,vs]=y.useState(""),[Oe,Ns]=y.useState(""),[Ze,ws]=y.useState(""),[Vs,Ue]=y.useState(""),[G,ne]=y.useState({name:"",email:"",role:"client",password:""}),[L,J]=y.useState({make:"",model:"",year:"",pricePerHour:"",location:"",description:"",ownerId:"",images:[]});y.useState({name:"",age:"",experience:"",location:"",pricePerHour:"",specialties:""});const ir=m.filter(o=>o.role==="owner");y.useEffect(()=>{mt(t)&&(v(),E(),R(),I(),B(),A())},[t]);const Xe=async(o,Y)=>{try{P(!0),await f(o,Y),await I()}catch(ue){console.error("Failed to verify driver:",ue)}finally{P(!1)}},nr=async(o,Y)=>{try{P(!0),await D(o,Y),await E()}catch(ue){console.error("Failed to update user role:",ue)}finally{P(!1)}},lr=async()=>{var o,Y;if(Ue(""),Oe!==Ze){Ue("Passwords do not match");return}if(Oe.length<6){Ue("Password must be at least 6 characters");return}try{P(!0),await H.post("/users/update-password",{current_password:js,new_password:Oe,new_password_confirmation:Ze}),vs(""),Ns(""),ws(""),ae(!1),alert("Password changed successfully!")}catch(ue){Ue(((Y=(o=ue.response)==null?void 0:o.data)==null?void 0:Y.message)||"Failed to change password")}finally{P(!1)}},or=async o=>{if(window.confirm("Are you sure you want to delete this user?"))try{P(!0),await H.delete(`/admin/users/${o}`),await E()}catch(Y){console.error("Failed to delete user:",Y)}finally{P(!1)}},Gs=async o=>{if(window.confirm("Are you sure you want to delete this car?"))try{P(!0),await H.delete(`/admin/cars/${o}`),await R()}catch(Y){console.error("Failed to delete car:",Y)}finally{P(!1)}},cr=async()=>{try{P(!0),await H.post("/admin/users",G),await E(),gs(!1),ne({name:"",email:"",role:"client",password:""})}catch(o){console.error("Failed to create user:",o)}finally{P(!1)}},dr=async()=>{if(fe)try{P(!0),await H.put(`/admin/users/${fe.id}`,G),await E(),ie(!1),we(!1),ze(null),ne({name:"",email:"",role:"client",password:""})}catch(o){console.error("Failed to update user:",o)}finally{P(!1)}},mr=async()=>{try{P(!0);const o=new FormData;o.append("make",L.make),o.append("model",L.model),o.append("year",L.year),o.append("pricePerHour",L.pricePerHour),o.append("location",L.location),o.append("description",L.description),o.append("ownerId",L.ownerId),L.images.forEach((Y,ue)=>{o.append(`images[${ue}]`,Y)}),await H.post("/admin/cars",o,{headers:{"Content-Type":"multipart/form-data"}}),await R(),ys(!1),J({make:"",model:"",year:"",pricePerHour:"",location:"",description:"",ownerId:"",images:[]})}catch(o){console.error("Failed to create car:",o)}finally{P(!1)}},xr=async()=>{if(bs)try{P(!0),await H.put(`/admin/cars/${bs.id}`,L),await R(),Re(!1),we(!1),De(null),J({make:"",model:"",year:"",pricePerHour:"",location:"",description:""})}catch(o){console.error("Failed to update car:",o)}finally{P(!1)}},ur=async(o,Y)=>{try{P(!0),await H.post(`/admin/cars/${o}/toggle-status`,{is_active:!Y}),await R()}catch(ue){console.error("Failed to toggle car status:",ue)}finally{P(!1)}},hr=async(o,Y)=>{try{P(!0),await H.post(`/admin/drivers/${o}/toggle-status`,{is_active:!Y}),await I()}catch(ue){console.error("Failed to toggle driver status:",ue)}finally{P(!1)}},pr=(d==null?void 0:d.users_count)||0,fr=(d==null?void 0:d.cars_count)||0,gr=(d==null?void 0:d.drivers_count)||0,yr=(d==null?void 0:d.bookings_count)||0,Ys=(d==null?void 0:d.pending_driver_verifications)||0,br=m.filter(o=>o.name.toLowerCase().includes(T.toLowerCase())||o.email.toLowerCase().includes(T.toLowerCase()));return a.filter(o=>(o.make.toLowerCase().includes(T.toLowerCase())||o.model.toLowerCase().includes(T.toLowerCase())||o.location.toLowerCase().includes(T.toLowerCase()))&&(F==="all"||F==="active"&&o.isActive||F==="inactive"&&!o.isActive)),i.filter(o=>(o.name.toLowerCase().includes(T.toLowerCase())||o.location.toLowerCase().includes(T.toLowerCase()))&&(F==="all"||F==="active"&&o.isAvailable||F==="inactive"&&!o.isAvailable||F==="pending"&&o.licenseVerificationStatus==="pending")),l.filter(o=>F==="all"||o.status===F),!t||!mt(t)?e.jsx(M,{children:e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs("div",{className:"bg-error-50 border border-error-200 rounded-lg p-4 text-center",children:[e.jsx("h2",{className:"text-xl font-medium text-error-800 mb-2",children:"Access Denied"}),e.jsx("p",{className:"text-error-600",children:"You must be logged in as an administrator to access this page."}),e.jsxs("div",{className:"mt-4 text-sm text-gray-600",children:[e.jsxs("p",{children:["Current user: ",(t==null?void 0:t.name)||"Not logged in"]}),e.jsxs("p",{children:["Role: ",(t==null?void 0:t.role)||"No role"]}),e.jsx("p",{className:"mt-2",children:"Please login with admin credentials:"}),e.jsx("p",{children:"Email: <EMAIL>"}),e.jsx("p",{children:"Password: password"})]}),e.jsx(S,{className:"mt-4",onClick:()=>s("/login"),children:"Log In"})]})})}):e.jsx(M,{children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-8",children:e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Admin Dashboard"}),e.jsx("p",{className:"text-gray-600",children:"Manage users, cars, drivers, and bookings."})]})}),e.jsxs("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[e.jsxs("div",{className:"flex flex-wrap border-b border-gray-200 overflow-x-auto",children:[e.jsxs("button",{className:`px-3 md:px-6 py-3 font-medium text-xs md:text-sm focus:outline-none whitespace-nowrap ${g==="overview"?"text-primary-600 border-b-2 border-primary-600 bg-primary-50":"text-gray-500 hover:text-gray-700 hover:bg-gray-50"}`,onClick:()=>N("overview"),children:[e.jsx(fi,{size:16,className:"inline-block mr-1"}),"Overview"]}),e.jsxs("button",{className:`px-3 md:px-6 py-3 font-medium text-xs md:text-sm focus:outline-none whitespace-nowrap ${g==="users"?"text-primary-600 border-b-2 border-primary-600 bg-primary-50":"text-gray-500 hover:text-gray-700 hover:bg-gray-50"}`,onClick:()=>N("users"),children:[e.jsx(rs,{size:16,className:"inline-block mr-1"}),"Users"]}),e.jsxs("button",{className:`px-3 md:px-6 py-3 font-medium text-xs md:text-sm focus:outline-none whitespace-nowrap ${g==="cars"?"text-primary-600 border-b-2 border-primary-600 bg-primary-50":"text-gray-500 hover:text-gray-700 hover:bg-gray-50"}`,onClick:()=>N("cars"),children:[e.jsx(Z,{size:16,className:"inline-block mr-1"}),"Cars"]}),e.jsxs("button",{className:`px-3 md:px-6 py-3 font-medium text-xs md:text-sm focus:outline-none whitespace-nowrap ${g==="drivers"?"text-primary-600 border-b-2 border-primary-600 bg-primary-50":"text-gray-500 hover:text-gray-700 hover:bg-gray-50"}`,onClick:()=>N("drivers"),children:[e.jsx(le,{size:16,className:"inline-block mr-1"}),"Drivers"]}),e.jsxs("button",{className:`px-3 md:px-6 py-3 font-medium text-xs md:text-sm focus:outline-none whitespace-nowrap ${g==="bookings"?"text-primary-600 border-b-2 border-primary-600 bg-primary-50":"text-gray-500 hover:text-gray-700 hover:bg-gray-50"}`,onClick:()=>N("bookings"),children:[e.jsx(he,{size:16,className:"inline-block mr-1"}),"Bookings"]}),e.jsxs("button",{className:`px-3 md:px-6 py-3 font-medium text-xs md:text-sm focus:outline-none whitespace-nowrap ${g==="gps-requests"?"text-primary-600 border-b-2 border-primary-600 bg-primary-50":"text-gray-500 hover:text-gray-700 hover:bg-gray-50"}`,onClick:()=>N("gps-requests"),children:[e.jsx(re,{size:16,className:"inline-block mr-1"}),"GPS Requests"]}),e.jsxs("button",{className:`px-3 md:px-6 py-3 font-medium text-xs md:text-sm focus:outline-none whitespace-nowrap ${g==="settings"?"text-primary-600 border-b-2 border-primary-600 bg-primary-50":"text-gray-500 hover:text-gray-700 hover:bg-gray-50"}`,onClick:()=>N("settings"),children:[e.jsx(ki,{size:16,className:"inline-block mr-1"}),"Settings"]})]}),g==="overview"&&e.jsxs("div",{className:"p-6",children:[e.jsx("h2",{className:"text-xl font-bold text-gray-900 mb-6",children:"Platform Overview"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[e.jsxs("div",{className:"bg-blue-50 rounded-lg p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h3",{className:"text-lg font-medium text-blue-800",children:"Total Users"}),e.jsx(rs,{size:24,className:"text-blue-500"})]}),e.jsx("p",{className:"text-3xl font-bold text-blue-600",children:pr}),e.jsxs("div",{className:"mt-2 text-sm text-blue-600",children:[e.jsx("span",{className:"font-medium",children:m.filter(o=>o.role==="client").length})," Clients,",e.jsx("span",{className:"font-medium ml-1",children:m.filter(o=>o.role==="owner").length})," Owners,",e.jsx("span",{className:"font-medium ml-1",children:m.filter(o=>o.role==="driver").length})," Drivers"]})]}),e.jsxs("div",{className:"bg-green-50 rounded-lg p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h3",{className:"text-lg font-medium text-green-800",children:"Total Cars"}),e.jsx(Z,{size:24,className:"text-green-500"})]}),e.jsx("p",{className:"text-3xl font-bold text-green-600",children:fr}),e.jsxs("div",{className:"mt-2 text-sm text-green-600",children:[e.jsx("span",{className:"font-medium",children:h.filter(o=>o.isActive).length})," Active,",e.jsx("span",{className:"font-medium ml-1",children:h.filter(o=>!o.isActive).length})," Inactive"]})]}),e.jsxs("div",{className:"bg-purple-50 rounded-lg p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h3",{className:"text-lg font-medium text-purple-800",children:"Total Drivers"}),e.jsx(le,{size:24,className:"text-purple-500"})]}),e.jsx("p",{className:"text-3xl font-bold text-purple-600",children:gr}),e.jsxs("div",{className:"mt-2 text-sm text-purple-600",children:[e.jsx("span",{className:"font-medium",children:x.filter(o=>o.isAvailable).length})," Available,",e.jsx("span",{className:"font-medium ml-1",children:x.filter(o=>!o.isAvailable).length})," Unavailable"]})]}),e.jsxs("div",{className:"bg-yellow-50 rounded-lg p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h3",{className:"text-lg font-medium text-yellow-800",children:"Total Bookings"}),e.jsx(he,{size:24,className:"text-yellow-500"})]}),e.jsx("p",{className:"text-3xl font-bold text-yellow-600",children:yr}),e.jsxs("div",{className:"mt-2 text-sm text-yellow-600",children:[e.jsx("span",{className:"font-medium",children:(d==null?void 0:d.completed_bookings)||0})," Completed,",e.jsx("span",{className:"font-medium ml-1",children:(d==null?void 0:d.pending_bookings)||0})," Pending"]})]})]}),e.jsxs("div",{className:"bg-orange-50 rounded-lg p-6 mb-8",children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx(pi,{size:24,className:"text-orange-500 mr-2"}),e.jsx("h3",{className:"text-lg font-medium text-orange-800",children:"Pending Verifications"})]}),e.jsx("p",{className:"text-3xl font-bold text-orange-600 mb-2",children:Ys}),e.jsxs("p",{className:"text-sm text-orange-700",children:[Ys," users are waiting for license verification. Review these to maintain platform safety."]}),e.jsx(S,{variant:"outline",className:"mt-4 border-orange-500 text-orange-700 hover:bg-orange-100",onClick:()=>N("users"),children:"Review Verifications"})]}),e.jsxs("div",{className:"bg-gray-50 rounded-lg p-6",children:[e.jsx("h3",{className:"text-lg font-medium text-gray-800 mb-4",children:"Recent Activity"}),e.jsx("div",{className:"space-y-4",children:((d==null?void 0:d.recent_bookings)||[]).slice(0,5).map((o,Y)=>e.jsxs("div",{className:"flex items-start",children:[e.jsx("div",{className:`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${o.status==="completed"?"bg-success-100":o.status==="pending"?"bg-yellow-100":o.status==="confirmed"?"bg-blue-100":"bg-error-100"}`,children:e.jsx(he,{size:16,className:`${o.status==="completed"?"text-success-600":o.status==="pending"?"text-yellow-600":o.status==="confirmed"?"text-blue-600":"text-error-600"}`})}),e.jsxs("div",{className:"ml-3",children:[e.jsxs("p",{className:"text-sm font-medium text-gray-900",children:["New ",o.itemType," booking (",o.status,")"]}),e.jsx("p",{className:"text-xs text-gray-500",children:new Date(o.createdAt).toLocaleString()})]})]},Y))})]})]}),g==="users"&&e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("h2",{className:"text-xl font-bold text-gray-900",children:"Users Management"}),e.jsxs("div",{className:"text-sm text-gray-600",children:["Total: ",m.length]})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(S,{onClick:()=>gs(!0),size:"sm",className:"flex items-center gap-2",children:[e.jsx(rs,{size:16}),"Create User"]}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:e.jsx(ps,{size:16,className:"text-gray-400"})}),e.jsx("input",{type:"text",placeholder:"Search users...",className:"pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500",value:T,onChange:o=>U(o.target.value)})]})]})]}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Role"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Joined"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"License Status"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:br.map(o=>e.jsxs("tr",{children:[e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0 h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center",children:o.licenseImageUrl?e.jsx("img",{src:o.licenseImageUrl,alt:o.name,className:"h-10 w-10 rounded-full object-cover"}):e.jsx(le,{size:20,className:"text-gray-500"})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("div",{className:"text-sm font-medium text-gray-900",children:o.name}),e.jsx("div",{className:"text-sm text-gray-500",children:o.email})]})]})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("span",{className:`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${o.role==="client"?"bg-blue-100 text-blue-800":o.role==="owner"?"bg-green-100 text-green-800":"bg-purple-100 text-purple-800"}`,children:o.role.charAt(0).toUpperCase()+o.role.slice(1)})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(o.createdAt).toLocaleDateString()}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:o.licenseVerificationStatus?e.jsx("span",{className:`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${o.licenseVerificationStatus==="verified"?"bg-success-100 text-success-800":o.licenseVerificationStatus==="pending"?"bg-yellow-100 text-yellow-800":"bg-error-100 text-error-800"}`,children:o.licenseVerificationStatus.charAt(0).toUpperCase()+o.licenseVerificationStatus.slice(1)}):e.jsx("span",{className:"text-gray-500 text-sm",children:"N/A"})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:e.jsxs("div",{className:"flex space-x-2",children:[e.jsx("button",{className:"text-blue-600 hover:text-blue-800",onClick:()=>{ze(o),ie(!0)},title:"View Details",children:e.jsx(Be,{size:16})}),e.jsx("button",{className:"text-green-600 hover:text-green-800",onClick:()=>{ze(o),ne({name:o.name,email:o.email,role:o.role,password:""}),we(!0),ie(!0)},title:"Edit User",children:e.jsx(Ie,{size:16})}),e.jsx("button",{className:"text-red-600 hover:text-red-800",onClick:()=>or(o.id),disabled:k,title:"Delete User",children:e.jsx(ts,{size:16})}),e.jsxs("select",{value:o.role,onChange:Y=>nr(o.id,Y.target.value),className:"text-sm border border-gray-300 rounded px-2 py-1",disabled:k,children:[e.jsx("option",{value:"client",children:"Client"}),e.jsx("option",{value:"owner",children:"Owner"}),e.jsx("option",{value:"driver",children:"Driver"}),e.jsx("option",{value:"admin",children:"Admin"})]})]})})]},o.id))})]})})]}),g==="cars"&&e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsx("h2",{className:"text-xl font-bold text-gray-900",children:"Cars Management"}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("div",{className:"text-sm text-gray-600",children:["Total Cars: ",h.length]}),e.jsxs(S,{onClick:()=>ys(!0),size:"sm",className:"flex items-center gap-2",children:[e.jsx(Z,{size:16}),"Create Car"]})]})]}),c?e.jsx("div",{className:"flex justify-center py-8",children:e.jsx(ce,{size:"lg"})}):p?e.jsx("div",{className:"bg-error-50 border border-error-200 rounded-lg p-4 text-center",children:e.jsx("p",{className:"text-error-600",children:p})}):h.length>0?e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-3 md:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Car"}),e.jsx("th",{className:"hidden md:table-cell px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Owner"}),e.jsx("th",{className:"hidden lg:table-cell px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Location"}),e.jsx("th",{className:"px-3 md:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Price/Hour"}),e.jsx("th",{className:"px-3 md:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),e.jsx("th",{className:"px-3 md:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:h.map(o=>e.jsxs("tr",{children:[e.jsx("td",{className:"px-3 md:px-6 py-4 whitespace-nowrap",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0 h-10 w-10",children:e.jsx("div",{className:"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center",children:e.jsx(Z,{className:"h-5 w-5 text-gray-600"})})}),e.jsxs("div",{className:"ml-4",children:[e.jsxs("div",{className:"text-sm font-medium text-gray-900",children:[o.make," ",o.model]}),e.jsx("div",{className:"text-sm text-gray-500",children:o.year}),e.jsxs("div",{className:"md:hidden text-xs text-gray-500 mt-1",children:["Owner #",o.ownerId," • ",o.location]})]})]})}),e.jsxs("td",{className:"hidden md:table-cell px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:["Owner #",o.ownerId]}),e.jsx("td",{className:"hidden lg:table-cell px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:o.location}),e.jsxs("td",{className:"px-3 md:px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:["$",o.pricePerHour,"/hr"]}),e.jsx("td",{className:"px-3 md:px-6 py-4 whitespace-nowrap",children:e.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${o.isActive?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:o.isActive?"Active":"Inactive"})}),e.jsx("td",{className:"px-3 md:px-6 py-4 whitespace-nowrap text-sm font-medium",children:e.jsxs("div",{className:"flex space-x-2",children:[e.jsx("button",{className:"text-blue-600 hover:text-blue-800",onClick:()=>{De(o),Re(!0)},title:"View Details",children:e.jsx(Be,{size:16})}),e.jsx("button",{className:"text-green-600 hover:text-green-800",onClick:()=>{De(o),J({make:o.make,model:o.model,year:o.year.toString(),pricePerHour:o.pricePerHour.toString(),location:o.location,description:o.description||""}),we(!0),Re(!0)},title:"Edit Car",children:e.jsx(Ie,{size:16})}),e.jsx("button",{className:`${o.isActive?"text-orange-600 hover:text-orange-800":"text-green-600 hover:text-green-800"}`,onClick:()=>ur(o.id,o.isActive),disabled:k,title:o.isActive?"Deactivate Car":"Activate Car",children:o.isActive?e.jsx(He,{size:16}):e.jsx(Te,{size:16})}),e.jsx("button",{className:"text-red-600 hover:text-red-800",onClick:()=>Gs(o.id),disabled:k,title:"Delete Car",children:e.jsx(ts,{size:16})})]})})]},o.id))})]})}):e.jsxs("div",{className:"text-center py-8",children:[e.jsx(Z,{size:48,className:"mx-auto text-gray-400 mb-4"}),e.jsx("p",{className:"text-gray-600",children:"No cars found"})]})]}),g==="drivers"&&e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsx("h2",{className:"text-xl font-bold text-gray-900",children:"Drivers Management"}),e.jsxs("div",{className:"text-sm text-gray-600",children:["Total Drivers: ",x.length]})]}),c?e.jsx("div",{className:"flex justify-center py-8",children:e.jsx(ce,{size:"lg"})}):p?e.jsx("div",{className:"bg-error-50 border border-error-200 rounded-lg p-4 text-center",children:e.jsx("p",{className:"text-error-600",children:p})}):x.length>0?e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Driver"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Experience"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Location"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Price/Hour"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"License Status"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:x.map(o=>e.jsxs("tr",{children:[e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0 h-10 w-10",children:e.jsx("div",{className:"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center",children:e.jsx(le,{className:"h-5 w-5 text-gray-600"})})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("div",{className:"text-sm font-medium text-gray-900",children:o.name}),e.jsxs("div",{className:"text-sm text-gray-500",children:["Age: ",o.age]})]})]})}),e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[o.experience," years"]}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:o.location}),e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:["$",o.pricePerHour,"/hr"]}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${o.licenseVerificationStatus==="verified"?"bg-green-100 text-green-800":o.licenseVerificationStatus==="pending"?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"}`,children:o.licenseVerificationStatus})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:e.jsxs("div",{className:"flex space-x-2",children:[e.jsx("button",{className:"text-blue-600 hover:text-blue-800",onClick:()=>{Ke(o),Je(!0)},title:"View Details",children:e.jsx(Be,{size:16})}),o.licenseVerificationStatus==="pending"&&e.jsxs(e.Fragment,{children:[e.jsx("button",{className:"text-green-600 hover:text-green-800",onClick:()=>Xe(o.id,"verified"),disabled:k,title:"Approve Driver",children:e.jsx(Te,{size:16})}),e.jsx("button",{className:"text-red-600 hover:text-red-800",onClick:()=>Xe(o.id,"rejected"),disabled:k,title:"Reject Driver",children:e.jsx(He,{size:16})})]}),e.jsx("button",{className:`${o.isActive?"text-orange-600 hover:text-orange-800":"text-green-600 hover:text-green-800"}`,onClick:()=>hr(o.id,o.isActive),disabled:k,title:o.isActive?"Deactivate Driver":"Activate Driver",children:o.isActive?e.jsx(He,{size:16}):e.jsx(Te,{size:16})})]})})]},o.id))})]})}):e.jsxs("div",{className:"text-center py-8",children:[e.jsx(le,{size:48,className:"mx-auto text-gray-400 mb-4"}),e.jsx("p",{className:"text-gray-600",children:"No drivers found"})]})]}),g==="bookings"&&e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsx("h2",{className:"text-xl font-bold text-gray-900",children:"Bookings Management"}),e.jsxs("div",{className:"text-sm text-gray-600",children:["Total Bookings: ",w.length]})]}),c?e.jsx("div",{className:"flex justify-center py-8",children:e.jsx(ce,{size:"lg"})}):p?e.jsx("div",{className:"bg-error-50 border border-error-200 rounded-lg p-4 text-center",children:e.jsx("p",{className:"text-error-600",children:p})}):w.length>0?e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Booking ID"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Item"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Duration"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Total Price"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:w.map(o=>e.jsxs("tr",{children:[e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:["#",o.id]}),e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:["User #",o.userId]}),e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[o.itemType," #",o.itemId]}),e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[new Date(o.startTime).toLocaleDateString()," - ",new Date(o.endTime).toLocaleDateString()]}),e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:["$",o.totalPrice]}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${o.status==="completed"?"bg-green-100 text-green-800":o.status==="pending"?"bg-yellow-100 text-yellow-800":o.status==="cancelled"?"bg-red-100 text-red-800":"bg-blue-100 text-blue-800"}`,children:o.status})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:e.jsxs("div",{className:"flex space-x-2",children:[e.jsx("button",{className:"text-blue-600 hover:text-blue-800",onClick:()=>{De(car),Re(!0)},title:"View Details",children:e.jsx(Be,{size:16})}),e.jsx("button",{className:"text-green-600 hover:text-green-800",onClick:()=>{De(car),we(!0),Re(!0)},title:"Edit Car",children:e.jsx(Ie,{size:16})}),e.jsx("button",{className:"text-red-600 hover:text-red-800",onClick:()=>Gs(car.id),disabled:k,title:"Delete Car",children:e.jsx(ts,{size:16})})]})})]},o.id))})]})}):e.jsxs("div",{className:"text-center py-8",children:[e.jsx(he,{size:48,className:"mx-auto text-gray-400 mb-4"}),e.jsx("p",{className:"text-gray-600",children:"No bookings found"})]})]}),g==="gps-requests"&&e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsx("h2",{className:"text-xl font-bold text-gray-900",children:"GPS Installation Requests"}),e.jsxs("div",{className:"text-sm text-gray-600",children:["Pending Requests: ",b.filter(o=>o.status==="pending").length]})]}),c?e.jsx("div",{className:"flex justify-center py-8",children:e.jsx(ce,{size:"lg"})}):p?e.jsx("div",{className:"bg-error-50 border border-error-200 rounded-lg p-4 text-center",children:e.jsx("p",{className:"text-error-600",children:p})}):b.length>0?e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Request ID"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Vehicle"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Contact"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:b.map(o=>e.jsxs("tr",{children:[e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:["#",o.id]}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:o.user.name}),e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[o.car_make," ",o.car_model," (",o.car_year,")",e.jsx("br",{}),e.jsx("span",{className:"text-xs text-gray-500",children:o.license_plate})]}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:o.contact_phone}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${o.status==="pending"?"bg-yellow-100 text-yellow-800":o.status==="approved"?"bg-blue-100 text-blue-800":o.status==="completed"?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:o.status.charAt(0).toUpperCase()+o.status.slice(1)})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:new Date(o.created_at).toLocaleDateString()}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:e.jsxs("div",{className:"flex space-x-2",children:[o.status==="pending"&&e.jsxs(e.Fragment,{children:[e.jsx("button",{className:"text-green-600 hover:text-green-800",onClick:()=>C(o.id,"approved"),disabled:k,title:"Approve Request",children:e.jsx(Is,{size:16})}),e.jsx("button",{className:"text-red-600 hover:text-red-800",onClick:()=>C(o.id,"rejected"),disabled:k,title:"Reject Request",children:e.jsx(Pe,{size:16})})]}),o.status==="approved"&&e.jsx("button",{className:"text-blue-600 hover:text-blue-800",onClick:()=>C(o.id,"completed"),disabled:k,title:"Mark as Completed",children:e.jsx(Is,{size:16})}),e.jsx("button",{className:"text-gray-600 hover:text-gray-800",onClick:()=>{alert(`Reason: ${o.reason}

Preferred Date: ${o.preferred_installation_date||"Not specified"}

Admin Notes: ${o.admin_notes||"None"}`)},title:"View Details",children:e.jsx(Be,{size:16})})]})})]},o.id))})]})}):e.jsxs("div",{className:"text-center py-8",children:[e.jsx(re,{size:48,className:"mx-auto text-gray-400 mb-4"}),e.jsx("p",{className:"text-gray-600",children:"No GPS installation requests found"}),e.jsx("p",{className:"text-sm text-gray-500 mt-2",children:"GPS installation requests will appear here when users request GPS tracking for their vehicles."})]})]}),g==="settings"&&e.jsxs("div",{className:"p-6",children:[e.jsx("div",{className:"flex justify-between items-center mb-6",children:e.jsx("h2",{className:"text-xl font-bold text-gray-900",children:"Admin Settings"})}),e.jsx("div",{className:"max-w-md",children:e.jsxs("div",{className:"bg-white p-6 rounded-lg border border-gray-200",children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Change Password"}),e.jsx(S,{onClick:()=>ae(!0),className:"w-full",children:"Change Admin Password"})]})})]})]}),X&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:e.jsxs("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Change Password"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Current Password"}),e.jsx(V,{type:"password",value:js,onChange:o=>vs(o.target.value),placeholder:"Enter current password"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"New Password"}),e.jsx(V,{type:"password",value:Oe,onChange:o=>Ns(o.target.value),placeholder:"Enter new password"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Confirm New Password"}),e.jsx(V,{type:"password",value:Ze,onChange:o=>ws(o.target.value),placeholder:"Confirm new password"})]}),Vs&&e.jsx("div",{className:"text-red-600 text-sm",children:Vs})]}),e.jsxs("div",{className:"flex justify-end space-x-3 mt-6",children:[e.jsx(S,{variant:"outline",onClick:()=>{ae(!1),vs(""),Ns(""),ws(""),Ue("")},children:"Cancel"}),e.jsx(S,{onClick:lr,disabled:k||!js||!Oe||!Ze,children:k?"Changing...":"Change Password"})]})]})}),be&&fe&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:e.jsxs("div",{className:"bg-white rounded-lg p-6 w-full max-w-lg",children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"User Details"}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{children:[e.jsx("strong",{children:"Name:"})," ",fe.name]}),e.jsxs("div",{children:[e.jsx("strong",{children:"Email:"})," ",fe.email]}),e.jsxs("div",{children:[e.jsx("strong",{children:"Role:"})," ",fe.role]}),e.jsxs("div",{children:[e.jsx("strong",{children:"Created:"})," ",new Date(fe.createdAt).toLocaleDateString()]}),fe.licenseVerificationStatus&&e.jsxs("div",{children:[e.jsx("strong",{children:"License Status:"})," ",fe.licenseVerificationStatus]})]}),e.jsx("div",{className:"flex justify-end mt-6",children:e.jsx(S,{variant:"outline",onClick:()=>{ie(!1),ze(null)},children:"Close"})})]})}),sr&&xe&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:e.jsxs("div",{className:"bg-white rounded-lg p-6 w-full max-w-lg",children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Driver Details"}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{children:[e.jsx("strong",{children:"Name:"})," ",xe.name]}),e.jsxs("div",{children:[e.jsx("strong",{children:"Age:"})," ",xe.age]}),e.jsxs("div",{children:[e.jsx("strong",{children:"Experience:"})," ",xe.experience," years"]}),e.jsxs("div",{children:[e.jsx("strong",{children:"Location:"})," ",xe.location]}),e.jsxs("div",{children:[e.jsx("strong",{children:"Price/Hour:"})," $",xe.pricePerHour]}),e.jsxs("div",{children:[e.jsx("strong",{children:"License Status:"})," ",xe.licenseVerificationStatus]}),e.jsxs("div",{children:[e.jsx("strong",{children:"Specialties:"})," ",xe.specialties.join(", ")]})]}),e.jsxs("div",{className:"flex justify-end space-x-3 mt-6",children:[xe.licenseVerificationStatus==="pending"&&e.jsxs(e.Fragment,{children:[e.jsx(S,{variant:"outline",onClick:()=>{Xe(xe.id,"rejected"),Je(!1),Ke(null)},disabled:k,children:"Reject"}),e.jsx(S,{onClick:()=>{Xe(xe.id,"verified"),Je(!1),Ke(null)},disabled:k,children:"Approve"})]}),e.jsx(S,{variant:"outline",onClick:()=>{Je(!1),Ke(null)},children:"Close"})]})]})}),rr&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:e.jsxs("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Create New User"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Name"}),e.jsx(V,{value:G.name,onChange:o=>ne({...G,name:o.target.value}),placeholder:"Enter user name"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"}),e.jsx(V,{type:"email",value:G.email,onChange:o=>ne({...G,email:o.target.value}),placeholder:"Enter email address"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Role"}),e.jsxs("select",{value:G.role,onChange:o=>ne({...G,role:o.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",children:[e.jsx("option",{value:"client",children:"Client"}),e.jsx("option",{value:"owner",children:"Owner"}),e.jsx("option",{value:"driver",children:"Driver"}),e.jsx("option",{value:"admin",children:"Admin"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Password"}),e.jsx(V,{type:"password",value:G.password,onChange:o=>ne({...G,password:o.target.value}),placeholder:"Enter password"})]})]}),e.jsxs("div",{className:"flex justify-end space-x-3 mt-6",children:[e.jsx(S,{variant:"outline",onClick:()=>{gs(!1),ne({name:"",email:"",role:"client",password:""})},children:"Cancel"}),e.jsx(S,{onClick:cr,disabled:k||!G.name||!G.email||!G.password,children:k?"Creating...":"Create User"})]})]})}),be&&$s&&fe&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:e.jsxs("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Edit User"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Name"}),e.jsx(V,{value:G.name,onChange:o=>ne({...G,name:o.target.value}),placeholder:"Enter user name"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"}),e.jsx(V,{type:"email",value:G.email,onChange:o=>ne({...G,email:o.target.value}),placeholder:"Enter email address"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Role"}),e.jsxs("select",{value:G.role,onChange:o=>ne({...G,role:o.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",children:[e.jsx("option",{value:"client",children:"Client"}),e.jsx("option",{value:"owner",children:"Owner"}),e.jsx("option",{value:"driver",children:"Driver"}),e.jsx("option",{value:"admin",children:"Admin"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"New Password (optional)"}),e.jsx(V,{type:"password",value:G.password,onChange:o=>ne({...G,password:o.target.value}),placeholder:"Leave blank to keep current password"})]})]}),e.jsxs("div",{className:"flex justify-end space-x-3 mt-6",children:[e.jsx(S,{variant:"outline",onClick:()=>{ie(!1),we(!1),ze(null),ne({name:"",email:"",role:"client",password:""})},children:"Cancel"}),e.jsx(S,{onClick:dr,disabled:k||!G.name||!G.email,children:k?"Updating...":"Update User"})]})]})}),ar&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:e.jsxs("div",{className:"bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto",children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Create New Car"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Make"}),e.jsx(V,{value:L.make,onChange:o=>J({...L,make:o.target.value}),placeholder:"Enter car make (e.g., Toyota)"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Model"}),e.jsx(V,{value:L.model,onChange:o=>J({...L,model:o.target.value}),placeholder:"Enter car model (e.g., Camry)"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Year"}),e.jsx(V,{type:"number",value:L.year,onChange:o=>J({...L,year:o.target.value}),placeholder:"Enter year (e.g., 2022)"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Price per Hour ($)"}),e.jsx(V,{type:"number",value:L.pricePerHour,onChange:o=>J({...L,pricePerHour:o.target.value}),placeholder:"Enter price per hour"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Location"}),e.jsx(V,{value:L.location,onChange:o=>J({...L,location:o.target.value}),placeholder:"Enter location"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Assign to Owner"}),e.jsxs("select",{value:L.ownerId,onChange:o=>J({...L,ownerId:o.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",required:!0,children:[e.jsx("option",{value:"",children:"Select an owner"}),ir.map(o=>e.jsxs("option",{value:o.id,children:[o.name," (",o.email,")"]},o.id))]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Car Images"}),e.jsx("input",{type:"file",multiple:!0,accept:"image/*",onChange:o=>{const Y=Array.from(o.target.files||[]);J({...L,images:Y})},className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Select multiple images for the car"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Description"}),e.jsx("textarea",{value:L.description,onChange:o=>J({...L,description:o.target.value}),placeholder:"Enter car description",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",rows:3})]})]}),e.jsxs("div",{className:"flex justify-end space-x-3 mt-6",children:[e.jsx(S,{variant:"outline",onClick:()=>{ys(!1),J({make:"",model:"",year:"",pricePerHour:"",location:"",description:""})},children:"Cancel"}),e.jsx(S,{onClick:mr,disabled:k||!L.make||!L.model||!L.year||!L.pricePerHour||!L.ownerId,children:k?"Creating...":"Create Car"})]})]})}),tr&&$s&&bs&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:e.jsxs("div",{className:"bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto",children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Edit Car"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Make"}),e.jsx(V,{value:L.make,onChange:o=>J({...L,make:o.target.value}),placeholder:"Enter car make"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Model"}),e.jsx(V,{value:L.model,onChange:o=>J({...L,model:o.target.value}),placeholder:"Enter car model"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Year"}),e.jsx(V,{type:"number",value:L.year,onChange:o=>J({...L,year:o.target.value}),placeholder:"Enter year"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Price per Hour ($)"}),e.jsx(V,{type:"number",value:L.pricePerHour,onChange:o=>J({...L,pricePerHour:o.target.value}),placeholder:"Enter price per hour"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Location"}),e.jsx(V,{value:L.location,onChange:o=>J({...L,location:o.target.value}),placeholder:"Enter location"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Description"}),e.jsx("textarea",{value:L.description,onChange:o=>J({...L,description:o.target.value}),placeholder:"Enter car description",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",rows:3})]})]}),e.jsxs("div",{className:"flex justify-end space-x-3 mt-6",children:[e.jsx(S,{variant:"outline",onClick:()=>{Re(!1),we(!1),De(null),J({make:"",model:"",year:"",pricePerHour:"",location:"",description:""})},children:"Cancel"}),e.jsx(S,{onClick:xr,disabled:k||!L.make||!L.model||!L.year||!L.pricePerHour,children:k?"Updating...":"Update Car"})]})]})})]})})},Vi=()=>{const{login:s,isLoading:t}=ee(),a=de(),[r,i]=y.useState(""),[n,l]=y.useState(""),[u,d]=y.useState(null),m=async h=>{h.preventDefault(),d(null);try{await s(r,n);const x=localStorage.getItem("user");if(x)switch(JSON.parse(x).role){case"admin":a("/admin");break;case"owner":a("/owner");break;case"driver":a("/driver");break;case"client":default:a("/");break}else a("/")}catch(x){d(x instanceof Error?x.message:"An error occurred during login")}};return e.jsx(M,{children:e.jsx("div",{className:"min-h-[calc(100vh-200px)] flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"max-w-md w-full",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx(Z,{className:"h-12 w-12 text-primary-600 mx-auto"}),e.jsx("h1",{className:"mt-4 text-3xl font-bold text-gray-900",children:"Log in to Park & Rent"}),e.jsx("p",{className:"mt-2 text-gray-600",children:"Access your account and manage your car rentals."})]}),e.jsxs("div",{className:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10",children:[u&&e.jsx("div",{className:"mb-4 p-3 bg-error-50 border border-error-200 rounded-md",children:e.jsxs("div",{className:"flex",children:[e.jsx(oe,{size:18,className:"text-error-600 mr-2 flex-shrink-0"}),e.jsx("p",{className:"text-sm text-error-600",children:u})]})}),e.jsxs("form",{className:"space-y-6",onSubmit:m,children:[e.jsx(V,{id:"email",type:"email",label:"Email address",value:r,onChange:h=>i(h.target.value),required:!0}),e.jsx(V,{id:"password",type:"password",label:"Password",value:n,onChange:h=>l(h.target.value),required:!0}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("input",{id:"remember-me",name:"remember-me",type:"checkbox",className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"}),e.jsx("label",{htmlFor:"remember-me",className:"ml-2 block text-sm text-gray-700",children:"Remember me"})]}),e.jsx("div",{className:"text-sm",children:e.jsx("a",{href:"#",className:"font-medium text-primary-600 hover:text-primary-500",children:"Forgot your password?"})})]}),e.jsx(S,{type:"submit",fullWidth:!0,disabled:t,children:t?"Logging in...":"Log in"})]}),e.jsxs("div",{className:"mt-6",children:[e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-0 flex items-center",children:e.jsx("div",{className:"w-full border-t border-gray-300"})}),e.jsx("div",{className:"relative flex justify-center text-sm",children:e.jsx("span",{className:"px-2 bg-white text-gray-500",children:"Or continue with"})})]}),e.jsxs("div",{className:"mt-6 grid grid-cols-2 gap-3",children:[e.jsx("div",{children:e.jsx(S,{fullWidth:!0,variant:"outline",className:"flex justify-center",children:"Google"})}),e.jsx("div",{children:e.jsx(S,{fullWidth:!0,variant:"outline",className:"flex justify-center",children:"Facebook"})})]})]}),e.jsx("div",{className:"mt-6 text-center",children:e.jsxs("p",{className:"text-sm text-gray-600",children:["Don't have an account?"," ",e.jsx(O,{to:"/signup",className:"font-medium text-primary-600 hover:text-primary-500",children:"Sign up"})]})})]})]})})})},Gi=()=>{const{register:s,isLoading:t}=ee(),a=de(),[r,i]=y.useState(""),[n,l]=y.useState(""),[u,d]=y.useState(""),[m,h]=y.useState(""),[x,w]=y.useState("client"),[b,c]=y.useState(""),[p,v]=y.useState(null),E=async R=>{if(R.preventDefault(),v(null),u!==m){v("Passwords do not match");return}if((x==="owner"||x==="driver")&&!b){v("Phone number is required for car owners and drivers");return}try{await s({name:r,email:n,role:x,phoneNumber:x==="owner"||x==="driver"?b:void 0},u),x==="client"?a("/account/verification"):x==="owner"?a("/owner/dashboard"):x==="driver"&&a("/driver/register")}catch(I){v(I instanceof Error?I.message:"An error occurred during registration")}};return e.jsx(M,{children:e.jsx("div",{className:"min-h-[calc(100vh-200px)] flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"max-w-md w-full",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsxs("div",{className:"flex justify-center space-x-2",children:[e.jsx(Z,{className:"h-12 w-12 text-primary-600"}),e.jsx(le,{className:"h-12 w-12 text-primary-600"})]}),e.jsx("h1",{className:"mt-4 text-3xl font-bold text-gray-900",children:"Create your account"}),e.jsx("p",{className:"mt-2 text-gray-600",children:"Join Park & Rent to rent cars, list your car, or register as a driver."})]}),e.jsxs("div",{className:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10",children:[p&&e.jsx("div",{className:"mb-4 p-3 bg-error-50 border border-error-200 rounded-md",children:e.jsxs("div",{className:"flex",children:[e.jsx(oe,{size:18,className:"text-error-600 mr-2 flex-shrink-0"}),e.jsx("p",{className:"text-sm text-error-600",children:p})]})}),e.jsxs("form",{className:"space-y-6",onSubmit:E,children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("span",{className:"block text-sm font-medium text-gray-700 mb-1",children:"I want to:"}),e.jsxs("div",{className:"flex flex-wrap gap-4",children:[e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"radio",name:"userType",value:"client",checked:x==="client",onChange:()=>w("client"),className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"}),e.jsx("span",{className:"ml-2 block text-sm text-gray-700",children:"Rent a car"})]}),e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"radio",name:"userType",value:"owner",checked:x==="owner",onChange:()=>w("owner"),className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"}),e.jsx("span",{className:"ml-2 block text-sm text-gray-700",children:"List my car"})]}),e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"radio",name:"userType",value:"driver",checked:x==="driver",onChange:()=>w("driver"),className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"}),e.jsx("span",{className:"ml-2 block text-sm text-gray-700",children:"Register as driver"})]})]})]}),e.jsx(V,{id:"name",label:"Full Name",value:r,onChange:R=>i(R.target.value),required:!0}),e.jsx(V,{id:"email",type:"email",label:"Email address",value:n,onChange:R=>l(R.target.value),required:!0}),(x==="owner"||x==="driver")&&e.jsx(V,{id:"phone",label:"Phone Number",value:b,onChange:R=>c(R.target.value),required:!0}),e.jsx(V,{id:"password",type:"password",label:"Password",value:u,onChange:R=>d(R.target.value),required:!0}),e.jsx(V,{id:"confirmPassword",type:"password",label:"Confirm Password",value:m,onChange:R=>h(R.target.value),required:!0}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("input",{id:"terms",name:"terms",type:"checkbox",required:!0,className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"}),e.jsxs("label",{htmlFor:"terms",className:"ml-2 block text-sm text-gray-700",children:["I agree to the"," ",e.jsx(O,{to:"/terms",className:"font-medium text-primary-600 hover:text-primary-500",children:"Terms of Service"})," ","and"," ",e.jsx(O,{to:"/privacy",className:"font-medium text-primary-600 hover:text-primary-500",children:"Privacy Policy"})]})]}),e.jsx(S,{type:"submit",fullWidth:!0,disabled:t,children:t?"Creating account...":"Sign up"})]}),e.jsx("div",{className:"mt-6 text-center",children:e.jsxs("p",{className:"text-sm text-gray-600",children:["Already have an account?"," ",e.jsx(O,{to:"/login",className:"font-medium text-primary-600 hover:text-primary-500",children:"Log in"})]})})]})]})})})},Yi=({id:s,label:t,onChange:a,error:r,className:i="",required:n=!1,accept:l="image/*",previewUrl:u})=>{const[d,m]=y.useState(u||null),h=w=>{var c;const b=((c=w.target.files)==null?void 0:c[0])||null;if(b){const p=new FileReader;p.onload=()=>{m(p.result)},p.readAsDataURL(b),a(b)}else m(null),a(null)},x=()=>{m(null),a(null)};return e.jsxs("div",{className:`mb-4 ${i}`,children:[t&&e.jsxs("label",{htmlFor:s,className:"block text-sm font-medium text-gray-700 mb-1",children:[t," ",n&&e.jsx("span",{className:"text-error-600",children:"*"})]}),d?e.jsxs("div",{className:"relative",children:[e.jsx("img",{src:d,alt:"Preview",className:"w-full h-48 object-cover rounded-md"}),e.jsx("button",{type:"button",onClick:x,className:"absolute top-2 right-2 bg-white rounded-full p-1 shadow-md hover:bg-gray-100",children:e.jsx(Pe,{size:16,className:"text-gray-700"})})]}):e.jsxs("div",{className:"border-2 border-dashed border-gray-300 rounded-md p-6 flex flex-col items-center justify-center",children:[e.jsx(fs,{size:24,className:"text-gray-400 mb-2"}),e.jsx("p",{className:"text-sm text-gray-500 mb-2",children:"Click to upload an image"}),e.jsx("input",{id:s,type:"file",accept:l,onChange:h,className:"hidden"}),e.jsx(S,{variant:"outline",size:"sm",onClick:()=>{var w;return(w=document.getElementById(s))==null?void 0:w.click()},children:"Select Image"})]}),r&&e.jsx("p",{className:"mt-1 text-sm text-error-600",children:r})]})},Wi=()=>{const{user:s,updateUser:t,isLoading:a}=ee(),r=de(),[i,n]=y.useState(null),[l,u]=y.useState(!1),[d,m]=y.useState(null),h=w=>{n(w),m(null)},x=async w=>{if(w.preventDefault(),m(null),!i){m("Please upload your driving license");return}try{const b=URL.createObjectURL(i);await t({licenseImageUrl:b,licenseVerificationStatus:"pending"}),u(!0),setTimeout(()=>{r("/")},3e3)}catch(b){m(b instanceof Error?b.message:"An error occurred during verification")}};return(s==null?void 0:s.role)!=="client"?(r("/"),null):(s==null?void 0:s.licenseVerificationStatus)==="verified"?e.jsx(M,{children:e.jsx("div",{className:"min-h-[calc(100vh-200px)] flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"max-w-md w-full text-center",children:[e.jsx(Te,{className:"h-16 w-16 text-success-500 mx-auto mb-4"}),e.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"License Verified"}),e.jsx("p",{className:"text-lg text-gray-600 mb-8",children:"Your driving license has been successfully verified. You can now contact car owners and arrange rentals."}),e.jsx(S,{onClick:()=>r("/cars"),children:"Browse Available Cars"})]})})}):(s==null?void 0:s.licenseVerificationStatus)==="pending"?e.jsx(M,{children:e.jsx("div",{className:"min-h-[calc(100vh-200px)] flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"max-w-md w-full text-center",children:[e.jsx("div",{className:"h-16 w-16 mx-auto mb-4 rounded-full bg-yellow-100 flex items-center justify-center",children:e.jsx(oe,{className:"h-10 w-10 text-yellow-500"})}),e.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Verification Pending"}),e.jsx("p",{className:"text-lg text-gray-600 mb-8",children:"Your driving license is currently being verified. This process usually takes 1-2 business days."}),e.jsx(S,{onClick:()=>r("/cars"),children:"Browse Available Cars"})]})})}):e.jsx(M,{children:e.jsx("div",{className:"min-h-[calc(100vh-200px)] flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:e.jsx("div",{className:"max-w-md w-full",children:l?e.jsxs("div",{className:"text-center",children:[e.jsx(Te,{className:"h-16 w-16 text-success-500 mx-auto mb-4"}),e.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"License Uploaded!"}),e.jsx("p",{className:"text-lg text-gray-600 mb-8",children:"Your driving license has been submitted for verification. We'll review it shortly."}),e.jsx("div",{className:"bg-success-50 border border-success-200 rounded-lg p-4 mb-6",children:e.jsx("p",{className:"text-success-700",children:"You'll be redirected in a few seconds..."})})]}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx(Ms,{className:"h-12 w-12 text-primary-600 mx-auto"}),e.jsx("h1",{className:"mt-4 text-3xl font-bold text-gray-900",children:"Verify Your Account"}),e.jsx("p",{className:"mt-2 text-gray-600",children:"Upload your driving license to verify your account and gain access to contact car owners."})]}),e.jsxs("div",{className:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10",children:[d&&e.jsx("div",{className:"mb-4 p-3 bg-error-50 border border-error-200 rounded-md",children:e.jsxs("div",{className:"flex",children:[e.jsx(oe,{size:18,className:"text-error-600 mr-2 flex-shrink-0"}),e.jsx("p",{className:"text-sm text-error-600",children:d})]})}),e.jsxs("form",{onSubmit:x,children:[e.jsxs("div",{className:"mb-6",children:[e.jsx(Yi,{id:"license",label:"Driving License",onChange:h,required:!0}),e.jsx("p",{className:"text-sm text-gray-600 mt-2",children:"Please upload a clear image of your valid driving license. We'll verify it to ensure the safety of our community."})]}),e.jsx("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6",children:e.jsxs("div",{className:"flex",children:[e.jsx(oe,{size:18,className:"text-yellow-600 mr-2 flex-shrink-0 mt-0.5"}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-yellow-800",children:"Important Information"}),e.jsx("p",{className:"text-sm text-yellow-700 mt-1",children:"Your license will only be used for verification purposes and will be securely stored. You won't be able to contact car owners until your license is verified."})]})]})}),e.jsx(S,{type:"submit",fullWidth:!0,disabled:a||!i,children:a?"Uploading...":"Submit for Verification"})]})]})]})})})})},ut=()=>{const{ownerCars:s,fetchOwnerCars:t,updateCar:a,deleteCar:r}=Ne();ee();const[i,n]=y.useState(!1),[l,u]=y.useState(null);y.useEffect(()=>{t()},[t]);const d=async(x,w)=>{await a(x,{isActive:!w})},m=x=>{u(x),n(!0)},h=async()=>{l&&(await r(l),n(!1),u(null))};return e.jsxs(M,{children:[e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-8",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Owner Dashboard"}),e.jsx("p",{className:"text-gray-600",children:"Manage your car listings and track rental requests."})]}),e.jsx("div",{className:"mt-4 md:mt-0",children:e.jsx(O,{to:"/owner/add-car",children:e.jsxs(S,{className:"flex items-center",children:[e.jsx(Us,{size:18,className:"mr-2"}),"Add New Car"]})})})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6 mb-8",children:[e.jsx("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"Your Listed Cars"}),s.length===0?e.jsxs("div",{className:"text-center py-8 border border-dashed border-gray-300 rounded-lg",children:[e.jsx(Z,{size:48,className:"mx-auto text-gray-400 mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No cars listed yet"}),e.jsx("p",{className:"text-gray-600 mb-6",children:"Start earning by adding your first car listing."}),e.jsx(O,{to:"/owner/add-car",children:e.jsx(S,{children:"Add Your Car"})})]}):e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Car"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Location"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Price"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:s.map(x=>e.jsxs("tr",{children:[e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"h-10 w-16 flex-shrink-0 bg-gray-200 rounded overflow-hidden",children:x.images.length>0&&e.jsx("img",{src:x.images[0],alt:`${x.make} ${x.model}`,className:"h-full w-full object-cover"})}),e.jsx("div",{className:"ml-4",children:e.jsxs("div",{className:"text-sm font-medium text-gray-900",children:[x.year," ",x.make," ",x.model]})})]})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("div",{className:"flex items-center text-sm text-gray-500",children:[e.jsx(re,{size:16,className:"mr-1"}),x.location]})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("div",{className:"flex items-center text-sm font-medium text-gray-900",children:[e.jsx(hs,{size:16,className:"text-primary-600"}),x.pricePerHour,"/hour"]})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("button",{onClick:()=>d(x.id,x.isActive),className:`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${x.isActive?"bg-success-100 text-success-800":"bg-gray-100 text-gray-800"}`,children:[e.jsx(Zt,{size:14,className:`mr-1 ${x.isActive?"text-success-600":"text-gray-500"}`}),x.isActive?"Active":"Inactive"]})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:e.jsxs("div",{className:"flex space-x-2",children:[e.jsx(O,{to:`/owner/edit-car/${x.id}`,children:e.jsx("button",{className:"text-primary-600 hover:text-primary-800",children:e.jsx(Ie,{size:18})})}),e.jsx("button",{className:"text-error-600 hover:text-error-800",onClick:()=>m(x.id),children:e.jsx(ts,{size:18})})]})})]},x.id))})]})})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[e.jsx("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"Apply for GPS Installation"}),e.jsx("p",{className:"text-gray-600 mb-6",children:"We offer GPS installation services for car owners who want to track their vehicles. Apply below to request this service."}),e.jsx(O,{to:"/owner/gps-request",children:e.jsx(S,{variant:"secondary",children:"Request GPS Installation"})})]})]}),i&&e.jsx("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:e.jsxs("div",{className:"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[e.jsx("div",{className:"fixed inset-0 transition-opacity","aria-hidden":"true",children:e.jsx("div",{className:"absolute inset-0 bg-gray-500 opacity-75"})}),e.jsx("span",{className:"hidden sm:inline-block sm:align-middle sm:h-screen","aria-hidden":"true",children:"​"}),e.jsxs("div",{className:"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full",children:[e.jsx("div",{className:"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:e.jsxs("div",{className:"sm:flex sm:items-start",children:[e.jsx("div",{className:"mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-error-100 sm:mx-0 sm:h-10 sm:w-10",children:e.jsx(oe,{className:"h-6 w-6 text-error-600"})}),e.jsxs("div",{className:"mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left",children:[e.jsx("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:"Delete Car Listing"}),e.jsx("div",{className:"mt-2",children:e.jsx("p",{className:"text-sm text-gray-500",children:"Are you sure you want to delete this car listing? This action cannot be undone."})})]})]})}),e.jsxs("div",{className:"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:[e.jsx("button",{type:"button",className:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-error-600 text-base font-medium text-white hover:bg-error-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-error-500 sm:ml-3 sm:w-auto sm:text-sm",onClick:h,children:"Delete"}),e.jsx("button",{type:"button",className:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm",onClick:()=>n(!1),children:"Cancel"})]})]})]})})]})},Ji=()=>{const s=de(),{addCar:t}=Ne(),{user:a}=ee(),[r,i]=y.useState(!1),[n,l]=y.useState(null),[u,d]=y.useState(!1),[m,h]=y.useState({make:"",model:"",year:new Date().getFullYear(),description:"",location:"",pricePerHour:0,availabilityNotes:""}),[x,w]=y.useState([]),[b,c]=y.useState(""),[p,v]=y.useState([]),[E,R]=y.useState([]);y.useState(!1);const I=g=>{const{name:N,value:k}=g.target;h({...m,[N]:N==="year"||N==="pricePerHour"?Number(k):k})},B=()=>{b.trim()&&!x.includes(b.trim())&&(w([...x,b.trim()]),c(""))},A=g=>{w(x.filter(N=>N!==g))},D=g=>{if(g.target.files){const N=Array.from(g.target.files),k=N.map(P=>URL.createObjectURL(P));R(P=>[...P,...N]),v(P=>[...P,...k])}},f=(g,N)=>{v(p.filter(k=>k!==g)),R(k=>{const P=[...k];return P.splice(N,1),P}),URL.revokeObjectURL(g)},C=async g=>{if(g.preventDefault(),!m.make||!m.model||!m.description||!m.location||m.pricePerHour<=0){l("Please fill in all required fields");return}if(p.length===0){l("Please add at least one image");return}if(x.length===0){l("Please add at least one feature");return}i(!0),l(null);try{if(!a)throw new Error("You must be logged in to add a car");await t({ownerId:a.id,make:m.make,model:m.model,year:m.year,images:[],description:m.description,features:x,location:m.location,pricePerHour:m.pricePerHour,availabilityNotes:m.availabilityNotes,isActive:!0},E),d(!0),p.forEach(N=>URL.revokeObjectURL(N)),setTimeout(()=>{s("/owner/dashboard")},2e3)}catch(N){l(N instanceof Error?N.message:"Failed to add car")}finally{i(!1)}};return e.jsx(M,{children:e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs("div",{className:"max-w-3xl mx-auto",children:[e.jsxs("div",{className:"flex items-center mb-6",children:[e.jsx("button",{onClick:()=>s("/owner/dashboard"),className:"text-primary-600 hover:text-primary-800 mr-4",children:"← Back to Dashboard"}),e.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Add New Car"})]}),u?e.jsxs("div",{className:"bg-success-100 border border-success-200 text-success-800 px-4 py-3 rounded mb-6",children:[e.jsx("p",{className:"font-medium",children:"Car added successfully!"}),e.jsx("p",{children:"Redirecting to dashboard..."})]}):e.jsxs("form",{onSubmit:C,children:[n&&e.jsx("div",{className:"bg-error-100 border border-error-200 text-error-800 px-4 py-3 rounded mb-6",children:n}),e.jsx("div",{className:"bg-white rounded-lg shadow-md overflow-hidden mb-6",children:e.jsxs("div",{className:"p-6",children:[e.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Car Details"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[e.jsxs("div",{children:[e.jsxs("label",{htmlFor:"make",className:"block text-sm font-medium text-gray-700 mb-1",children:["Make ",e.jsx("span",{className:"text-error-600",children:"*"})]}),e.jsx("input",{type:"text",id:"make",name:"make",value:m.make,onChange:I,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",required:!0})]}),e.jsxs("div",{children:[e.jsxs("label",{htmlFor:"model",className:"block text-sm font-medium text-gray-700 mb-1",children:["Model ",e.jsx("span",{className:"text-error-600",children:"*"})]}),e.jsx("input",{type:"text",id:"model",name:"model",value:m.model,onChange:I,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",required:!0})]})]}),e.jsxs("div",{className:"mb-6",children:[e.jsxs("label",{htmlFor:"year",className:"block text-sm font-medium text-gray-700 mb-1",children:["Year ",e.jsx("span",{className:"text-error-600",children:"*"})]}),e.jsx("select",{id:"year",name:"year",value:m.year,onChange:I,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",required:!0,children:Array.from({length:30},(g,N)=>new Date().getFullYear()-N).map(g=>e.jsx("option",{value:g,children:g},g))})]}),e.jsxs("div",{className:"mb-6",children:[e.jsxs("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 mb-1",children:["Description ",e.jsx("span",{className:"text-error-600",children:"*"})]}),e.jsx("textarea",{id:"description",name:"description",value:m.description,onChange:I,rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",required:!0})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[e.jsxs("div",{children:[e.jsxs("label",{htmlFor:"location",className:"block text-sm font-medium text-gray-700 mb-1",children:["Location ",e.jsx("span",{className:"text-error-600",children:"*"})]}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:e.jsx(re,{size:18,className:"text-gray-400"})}),e.jsx("input",{type:"text",id:"location",name:"location",value:m.location,onChange:I,className:"w-full pl-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",placeholder:"City, Country",required:!0})]})]}),e.jsxs("div",{children:[e.jsxs("label",{htmlFor:"pricePerHour",className:"block text-sm font-medium text-gray-700 mb-1",children:["Price per Hour (RWF) ",e.jsx("span",{className:"text-error-600",children:"*"})]}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:e.jsx(hs,{size:18,className:"text-gray-400"})}),e.jsx("input",{type:"number",id:"pricePerHour",name:"pricePerHour",value:m.pricePerHour,onChange:I,min:"0",step:"1000",className:"w-full pl-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",required:!0})]})]})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{htmlFor:"availabilityNotes",className:"block text-sm font-medium text-gray-700 mb-1",children:"Availability Notes"}),e.jsx("textarea",{id:"availabilityNotes",name:"availabilityNotes",value:m.availabilityNotes,onChange:I,rows:2,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",placeholder:"e.g., Available on weekends only"})]})]})}),e.jsx("div",{className:"bg-white rounded-lg shadow-md overflow-hidden mb-6",children:e.jsxs("div",{className:"p-6",children:[e.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Features"}),e.jsxs("div",{className:"flex mb-4",children:[e.jsx("input",{type:"text",value:b,onChange:g=>c(g.target.value),className:"flex-grow px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-primary-500",placeholder:"Add a feature (e.g., Air Conditioning)"}),e.jsxs(S,{type:"button",onClick:B,className:"rounded-l-none",children:[e.jsx(Us,{size:18,className:"mr-1"})," Add"]})]}),x.length===0?e.jsx("div",{className:"text-gray-500 italic mb-4",children:"No features added yet. Please add at least one feature."}):e.jsx("div",{className:"flex flex-wrap gap-2 mb-4",children:x.map((g,N)=>e.jsxs("div",{className:"flex items-center bg-gray-100 px-3 py-1 rounded-full",children:[e.jsx("span",{className:"text-sm",children:g}),e.jsx("button",{type:"button",onClick:()=>A(g),className:"ml-2 text-gray-500 hover:text-error-600",children:e.jsx(Pe,{size:14})})]},N))})]})}),e.jsx("div",{className:"bg-white rounded-lg shadow-md overflow-hidden mb-6",children:e.jsxs("div",{className:"p-6",children:[e.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Images"}),e.jsx("div",{className:"mb-4",children:e.jsxs("label",{htmlFor:"car-images",className:"block w-full cursor-pointer text-center py-8 px-4 border-2 border-dashed border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:[e.jsx(fs,{size:36,className:"mx-auto mb-2 text-gray-400"}),e.jsx("span",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Click to select images"}),e.jsx("span",{className:"text-xs text-gray-500",children:"JPG, PNG or WEBP (max. 5MB each)"}),e.jsx("input",{id:"car-images",type:"file",multiple:!0,accept:"image/jpeg,image/png,image/webp",onChange:D,className:"hidden"})]})}),p.length===0?e.jsx("div",{className:"text-gray-500 italic mb-4",children:"No images added yet. Please add at least one image."}):e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 mb-4",children:p.map((g,N)=>e.jsxs("div",{className:"relative group",children:[e.jsx("img",{src:g,alt:`Car image ${N+1}`,className:"w-full h-40 object-cover rounded-md",onError:k=>{k.target.src="https://via.placeholder.com/300x200?text=Image+Error"}}),e.jsx("button",{type:"button",onClick:()=>f(g,N),className:"absolute top-2 right-2 bg-white rounded-full p-1 shadow-md text-gray-700 hover:text-error-600",children:e.jsx(Pe,{size:16})})]},N))}),e.jsxs("div",{className:"text-sm text-gray-500 flex items-start",children:[e.jsx($e,{size:16,className:"mr-1 mt-0.5 flex-shrink-0"}),e.jsx("span",{children:"Selected images will be uploaded when you submit the form."})]})]})}),e.jsxs("div",{className:"flex justify-end space-x-4",children:[e.jsx(S,{type:"button",variant:"outline",onClick:()=>s("/owner/dashboard"),children:"Cancel"}),e.jsx(S,{type:"submit",disabled:r,children:r?"Adding Car...":"Add Car"})]})]})]})})})},Ki=()=>{const s=de(),{id:t}=Fs(),{cars:a,updateCar:r,getCarById:i}=Ne(),{user:n}=ee(),[l,u]=y.useState(!1),[d,m]=y.useState(null),[h,x]=y.useState(!1),[w,b]=y.useState(!0),c=a.find(F=>F.id===t),[p,v]=y.useState({make:"",model:"",year:new Date().getFullYear(),description:"",location:"",pricePerHour:0,availabilityNotes:"",isActive:!0}),[E,R]=y.useState([]),[I,B]=y.useState(""),[A,D]=y.useState([]),[f,C]=y.useState([]);y.useEffect(()=>{if(!t){s("/owner/dashboard");return}c?(v({make:c.make,model:c.model,year:c.year,description:c.description,location:c.location,pricePerHour:c.pricePerHour,availabilityNotes:c.availabilityNotes||"",isActive:c.isActive}),R(c.features||[]),D(c.images||[]),b(!1)):i(t).then(()=>{b(!1)}).catch(()=>{m("Car not found"),b(!1)})},[t,c,s,i]),y.useEffect(()=>{c&&n&&c.ownerId!==n.id&&m("You can only edit your own cars")},[c,n]);const g=F=>{const{name:q,value:X}=F.target;v({...p,[q]:q==="year"||q==="pricePerHour"?Number(X):X})},N=()=>{I.trim()&&!E.includes(I.trim())&&(R([...E,I.trim()]),B(""))},k=F=>{R(E.filter((q,X)=>X!==F))},P=F=>{Array.from(F.target.files||[]).forEach(X=>{if(X.size>5*1024*1024){m("Each image must be less than 5MB");return}const ae=new FileReader;ae.onload=()=>{const be=ae.result;D(ie=>[...ie,be]),C(ie=>[...ie,X])},ae.readAsDataURL(X)})},T=(F,q)=>{D(A.filter(X=>X!==F)),q<f.length&&C(X=>{const ae=[...X];return ae.splice(q,1),ae}),F.startsWith("data:")&&URL.revokeObjectURL(F)},U=async F=>{if(F.preventDefault(),!p.make||!p.model||!p.description||!p.location||p.pricePerHour<=0){m("Please fill in all required fields");return}if(A.length===0){m("Please keep at least one image");return}if(E.length===0){m("Please add at least one feature");return}u(!0),m(null);try{if(!n||!c)throw new Error("Invalid user or car data");await r(c.id,{make:p.make,model:p.model,year:p.year,description:p.description,features:E,location:p.location,pricePerHour:p.pricePerHour,availabilityNotes:p.availabilityNotes,isActive:p.isActive,images:A}),x(!0),A.forEach(q=>{q.startsWith("blob:")&&URL.revokeObjectURL(q)}),setTimeout(()=>{s("/owner/dashboard")},2e3)}catch(q){m(q instanceof Error?q.message:"Failed to update car")}finally{u(!1)}};return w?e.jsx(M,{children:e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsx("div",{className:"flex items-center justify-center py-12",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"}),e.jsx("p",{className:"text-gray-600",children:"Loading car details..."})]})})})}):d&&!c?e.jsx(M,{children:e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs("div",{className:"max-w-3xl mx-auto",children:[e.jsxs("div",{className:"flex items-center mb-6",children:[e.jsx("button",{onClick:()=>s("/owner/dashboard"),className:"text-primary-600 hover:text-primary-800 mr-4",children:"← Back to Dashboard"}),e.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Edit Car"})]}),e.jsxs("div",{className:"bg-error-50 border border-error-200 rounded-lg p-4 text-center",children:[e.jsx("h2",{className:"text-xl font-medium text-error-800 mb-2",children:"Error"}),e.jsx("p",{className:"text-error-600",children:d})]})]})})}):e.jsx(M,{children:e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs("div",{className:"max-w-3xl mx-auto",children:[e.jsxs("div",{className:"flex items-center mb-6",children:[e.jsxs("button",{onClick:()=>s("/owner/dashboard"),className:"text-primary-600 hover:text-primary-800 mr-4 flex items-center",children:[e.jsx(ve,{size:20,className:"mr-1"}),"Back to Dashboard"]}),e.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Edit Car"})]}),h?e.jsxs("div",{className:"bg-success-50 border border-success-200 rounded-lg p-8 text-center",children:[e.jsx("div",{className:"w-16 h-16 bg-success-100 rounded-full flex items-center justify-center mx-auto mb-4",children:e.jsx(Z,{size:32,className:"text-success-600"})}),e.jsx("h2",{className:"text-2xl font-bold text-success-800 mb-4",children:"Car Updated Successfully!"}),e.jsx("p",{className:"text-success-700 mb-6",children:"Your car listing has been updated and is now live on the platform."}),e.jsx(S,{onClick:()=>s("/owner/dashboard"),children:"Return to Dashboard"})]}):e.jsxs("form",{onSubmit:U,children:[d&&e.jsxs("div",{className:"mb-6 bg-error-50 border border-error-200 rounded-lg p-4 flex items-start",children:[e.jsx(oe,{size:20,className:"text-error-600 mr-2 flex-shrink-0 mt-0.5"}),e.jsx("div",{className:"text-error-700",children:d})]}),e.jsx("div",{className:"bg-white rounded-lg shadow-md overflow-hidden mb-6",children:e.jsxs("div",{className:"p-6",children:[e.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Car Details"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[e.jsxs("div",{children:[e.jsxs("label",{htmlFor:"make",className:"block text-sm font-medium text-gray-700 mb-1",children:["Make ",e.jsx("span",{className:"text-error-600",children:"*"})]}),e.jsx("input",{type:"text",id:"make",name:"make",value:p.make,onChange:g,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",placeholder:"e.g., Toyota",required:!0})]}),e.jsxs("div",{children:[e.jsxs("label",{htmlFor:"model",className:"block text-sm font-medium text-gray-700 mb-1",children:["Model ",e.jsx("span",{className:"text-error-600",children:"*"})]}),e.jsx("input",{type:"text",id:"model",name:"model",value:p.model,onChange:g,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",placeholder:"e.g., Camry",required:!0})]})]}),e.jsxs("div",{className:"mb-6",children:[e.jsxs("label",{htmlFor:"year",className:"block text-sm font-medium text-gray-700 mb-1",children:["Year ",e.jsx("span",{className:"text-error-600",children:"*"})]}),e.jsx("select",{id:"year",name:"year",value:p.year,onChange:g,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",required:!0,children:Array.from({length:30},(F,q)=>new Date().getFullYear()-q).map(F=>e.jsx("option",{value:F,children:F},F))})]}),e.jsxs("div",{className:"mb-6",children:[e.jsxs("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 mb-1",children:["Description ",e.jsx("span",{className:"text-error-600",children:"*"})]}),e.jsx("textarea",{id:"description",name:"description",value:p.description,onChange:g,rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",placeholder:"Describe your car, its condition, and any special features...",required:!0})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[e.jsxs("div",{children:[e.jsxs("label",{htmlFor:"location",className:"block text-sm font-medium text-gray-700 mb-1",children:["Location ",e.jsx("span",{className:"text-error-600",children:"*"})]}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:e.jsx(re,{size:18,className:"text-gray-400"})}),e.jsx("input",{type:"text",id:"location",name:"location",value:p.location,onChange:g,className:"w-full pl-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",placeholder:"City, Country",required:!0})]})]}),e.jsxs("div",{children:[e.jsxs("label",{htmlFor:"pricePerHour",className:"block text-sm font-medium text-gray-700 mb-1",children:["Price per Hour (RWF) ",e.jsx("span",{className:"text-error-600",children:"*"})]}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:e.jsx(hs,{size:18,className:"text-gray-400"})}),e.jsx("input",{type:"number",id:"pricePerHour",name:"pricePerHour",value:p.pricePerHour,onChange:g,min:"0",step:"1000",className:"w-full pl-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",required:!0})]})]})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{htmlFor:"availabilityNotes",className:"block text-sm font-medium text-gray-700 mb-1",children:"Availability Notes"}),e.jsx("input",{type:"text",id:"availabilityNotes",name:"availabilityNotes",value:p.availabilityNotes,onChange:g,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",placeholder:"e.g., Available weekdays 9 AM - 6 PM"})]}),e.jsx("div",{className:"mb-6",children:e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",checked:p.isActive,onChange:F=>v({...p,isActive:F.target.checked}),className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"}),e.jsx("span",{className:"ml-2 text-sm text-gray-700",children:"Active (visible on the website)"})]})})]})}),e.jsx("div",{className:"bg-white rounded-lg shadow-md overflow-hidden mb-6",children:e.jsxs("div",{className:"p-6",children:[e.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Features"}),e.jsxs("div",{className:"flex mb-4",children:[e.jsx("input",{type:"text",value:I,onChange:F=>B(F.target.value),className:"flex-grow px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-primary-500",placeholder:"Add a feature (e.g., Air Conditioning)"}),e.jsxs(S,{type:"button",onClick:N,className:"rounded-l-none",children:[e.jsx(Us,{size:18,className:"mr-1"})," Add"]})]}),E.length===0?e.jsx("div",{className:"text-gray-500 italic mb-4",children:"No features added yet. Please add at least one feature."}):e.jsx("div",{className:"flex flex-wrap gap-2 mb-4",children:E.map((F,q)=>e.jsxs("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm bg-primary-100 text-primary-800",children:[F,e.jsx("button",{type:"button",onClick:()=>k(q),className:"ml-2 text-primary-600 hover:text-primary-800",children:e.jsx(Pe,{size:14})})]},q))}),e.jsxs("div",{className:"text-sm text-gray-500 flex items-start",children:[e.jsx($e,{size:16,className:"mr-1 mt-0.5 flex-shrink-0"}),e.jsx("span",{children:"Add features that make your car attractive to renters (e.g., GPS, Bluetooth, etc.)"})]})]})}),e.jsx("div",{className:"bg-white rounded-lg shadow-md overflow-hidden mb-6",children:e.jsxs("div",{className:"p-6",children:[e.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Images"}),e.jsx("div",{className:"mb-4",children:e.jsxs("label",{htmlFor:"car-images",className:"block w-full cursor-pointer text-center py-8 px-4 border-2 border-dashed border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:[e.jsx(fs,{size:36,className:"mx-auto mb-2 text-gray-400"}),e.jsx("span",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Click to add more images"}),e.jsx("span",{className:"text-xs text-gray-500",children:"JPG, PNG or WEBP (max. 5MB each)"}),e.jsx("input",{id:"car-images",type:"file",multiple:!0,accept:"image/jpeg,image/png,image/webp",onChange:P,className:"hidden"})]})}),A.length===0?e.jsx("div",{className:"text-gray-500 italic mb-4",children:"No images available. Please add at least one image."}):e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 mb-4",children:A.map((F,q)=>e.jsxs("div",{className:"relative group",children:[e.jsx("img",{src:F,alt:`Car image ${q+1}`,className:"w-full h-40 object-cover rounded-md",onError:X=>{const ae=X.target;ae.style.display="none";const be=ae.parentElement;if(be&&!be.querySelector(".image-fallback")){const ie=document.createElement("div");ie.className="image-fallback w-full h-40 bg-gray-200 flex items-center justify-center text-gray-500 text-sm rounded-md",ie.textContent="Image Error",be.appendChild(ie)}}}),e.jsx("button",{type:"button",onClick:()=>T(F,q),className:"absolute top-2 right-2 bg-white rounded-full p-1 shadow-md text-gray-700 hover:text-error-600",children:e.jsx(Pe,{size:16})})]},q))}),e.jsxs("div",{className:"text-sm text-gray-500 flex items-start",children:[e.jsx($e,{size:16,className:"mr-1 mt-0.5 flex-shrink-0"}),e.jsx("span",{children:"Current images will be kept. New images will be added to your listing."})]})]})}),e.jsxs("div",{className:"flex justify-end space-x-4",children:[e.jsx(S,{type:"button",variant:"outline",onClick:()=>s("/owner/dashboard"),children:"Cancel"}),e.jsx(S,{type:"submit",disabled:l,children:l?"Updating Car...":"Update Car"})]})]})]})})})},Zi=()=>{const s=de(),{user:t}=ee(),[a,r]=y.useState(!1),[i,n]=y.useState(null),[l,u]=y.useState(!1),[d,m]=y.useState({carMake:"",carModel:"",carYear:new Date().getFullYear(),carPlateNumber:"",ownerName:(t==null?void 0:t.name)||"",ownerPhone:"",ownerEmail:(t==null?void 0:t.email)||"",installationLocation:"",preferredDate:"",preferredTime:"",additionalNotes:""}),h=w=>{const{name:b,value:c}=w.target;m({...d,[b]:b==="carYear"?Number(c):c})},x=async w=>{if(w.preventDefault(),n(null),!d.carMake||!d.carModel||!d.carPlateNumber||!d.ownerName||!d.ownerPhone||!d.installationLocation){n("Please fill in all required fields");return}r(!0);try{await new Promise(b=>setTimeout(b,2e3)),u(!0),setTimeout(()=>{s("/owner/dashboard")},3e3)}catch{n("Failed to submit GPS installation request. Please try again.")}finally{r(!1)}};return l?e.jsx(M,{children:e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsx("div",{className:"max-w-2xl mx-auto",children:e.jsxs("div",{className:"bg-success-50 border border-success-200 rounded-lg p-8 text-center",children:[e.jsx("div",{className:"w-16 h-16 bg-success-100 rounded-full flex items-center justify-center mx-auto mb-4",children:e.jsx(re,{size:32,className:"text-success-600"})}),e.jsx("h2",{className:"text-2xl font-bold text-success-800 mb-4",children:"GPS Installation Request Submitted!"}),e.jsx("p",{className:"text-success-700 mb-6",children:"Thank you for your GPS installation request. Our team will contact you within 24-48 hours to schedule the installation and provide you with pricing details."}),e.jsxs("div",{className:"space-y-2 text-sm text-success-600 mb-6",children:[e.jsx("p",{children:e.jsx("strong",{children:"What's next?"})}),e.jsx("p",{children:"• Our technician will call you to confirm details"}),e.jsx("p",{children:"• We'll schedule a convenient installation time"}),e.jsx("p",{children:"• Professional GPS installation at your location"}),e.jsx("p",{children:"• Setup and training on the GPS tracking system"})]}),e.jsx(S,{onClick:()=>s("/owner/dashboard"),children:"Return to Dashboard"})]})})})}):e.jsx(M,{children:e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs("div",{className:"max-w-3xl mx-auto",children:[e.jsxs("div",{className:"flex items-center mb-6",children:[e.jsxs("button",{onClick:()=>s("/owner/dashboard"),className:"text-primary-600 hover:text-primary-800 mr-4 flex items-center",children:[e.jsx(ve,{size:20,className:"mr-1"}),"Back to Dashboard"]}),e.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"GPS Installation Request"})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[e.jsxs("div",{className:"p-6 bg-primary-50 border-b border-primary-100",children:[e.jsx("h2",{className:"text-xl font-semibold text-primary-800 mb-2",children:"Professional GPS Tracking Installation"}),e.jsx("p",{className:"text-primary-700",children:"Enhance your car's security and tracking capabilities with our professional GPS installation service. Perfect for car owners who want to monitor their vehicle's location and usage."})]}),e.jsxs("form",{onSubmit:x,className:"p-6",children:[i&&e.jsx("div",{className:"mb-6 p-4 bg-error-50 border border-error-200 rounded-lg",children:e.jsx("p",{className:"text-error-700",children:i})}),e.jsxs("div",{className:"mb-8",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center",children:[e.jsx(Z,{size:20,className:"mr-2 text-primary-600"}),"Vehicle Information"]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsxs("label",{htmlFor:"carMake",className:"block text-sm font-medium text-gray-700 mb-1",children:["Car Make ",e.jsx("span",{className:"text-error-600",children:"*"})]}),e.jsx("input",{type:"text",id:"carMake",name:"carMake",value:d.carMake,onChange:h,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",placeholder:"e.g., Toyota",required:!0})]}),e.jsxs("div",{children:[e.jsxs("label",{htmlFor:"carModel",className:"block text-sm font-medium text-gray-700 mb-1",children:["Car Model ",e.jsx("span",{className:"text-error-600",children:"*"})]}),e.jsx("input",{type:"text",id:"carModel",name:"carModel",value:d.carModel,onChange:h,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",placeholder:"e.g., Camry",required:!0})]}),e.jsxs("div",{children:[e.jsxs("label",{htmlFor:"carYear",className:"block text-sm font-medium text-gray-700 mb-1",children:["Year ",e.jsx("span",{className:"text-error-600",children:"*"})]}),e.jsx("select",{id:"carYear",name:"carYear",value:d.carYear,onChange:h,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",required:!0,children:Array.from({length:30},(w,b)=>new Date().getFullYear()-b).map(w=>e.jsx("option",{value:w,children:w},w))})]}),e.jsxs("div",{children:[e.jsxs("label",{htmlFor:"carPlateNumber",className:"block text-sm font-medium text-gray-700 mb-1",children:["Plate Number ",e.jsx("span",{className:"text-error-600",children:"*"})]}),e.jsx("input",{type:"text",id:"carPlateNumber",name:"carPlateNumber",value:d.carPlateNumber,onChange:h,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",placeholder:"e.g., RAD 123A",required:!0})]})]})]}),e.jsxs("div",{className:"mb-8",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center",children:[e.jsx(je,{size:20,className:"mr-2 text-primary-600"}),"Contact Information"]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsxs("label",{htmlFor:"ownerName",className:"block text-sm font-medium text-gray-700 mb-1",children:["Full Name ",e.jsx("span",{className:"text-error-600",children:"*"})]}),e.jsx("input",{type:"text",id:"ownerName",name:"ownerName",value:d.ownerName,onChange:h,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",required:!0})]}),e.jsxs("div",{children:[e.jsxs("label",{htmlFor:"ownerPhone",className:"block text-sm font-medium text-gray-700 mb-1",children:["Phone Number ",e.jsx("span",{className:"text-error-600",children:"*"})]}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:e.jsx(Se,{size:18,className:"text-gray-400"})}),e.jsx("input",{type:"tel",id:"ownerPhone",name:"ownerPhone",value:d.ownerPhone,onChange:h,className:"w-full pl-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",placeholder:"**********",required:!0})]})]}),e.jsxs("div",{className:"md:col-span-2",children:[e.jsx("label",{htmlFor:"ownerEmail",className:"block text-sm font-medium text-gray-700 mb-1",children:"Email Address"}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:e.jsx(Jt,{size:18,className:"text-gray-400"})}),e.jsx("input",{type:"email",id:"ownerEmail",name:"ownerEmail",value:d.ownerEmail,onChange:h,className:"w-full pl-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",placeholder:"<EMAIL>"})]})]})]})]}),e.jsxs("div",{className:"mb-8",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center",children:[e.jsx(he,{size:20,className:"mr-2 text-primary-600"}),"Installation Details"]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"md:col-span-2",children:[e.jsxs("label",{htmlFor:"installationLocation",className:"block text-sm font-medium text-gray-700 mb-1",children:["Installation Location ",e.jsx("span",{className:"text-error-600",children:"*"})]}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:e.jsx(re,{size:18,className:"text-gray-400"})}),e.jsx("input",{type:"text",id:"installationLocation",name:"installationLocation",value:d.installationLocation,onChange:h,className:"w-full pl-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",placeholder:"Address where installation should take place",required:!0})]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"preferredDate",className:"block text-sm font-medium text-gray-700 mb-1",children:"Preferred Date"}),e.jsx("input",{type:"date",id:"preferredDate",name:"preferredDate",value:d.preferredDate,onChange:h,min:new Date().toISOString().split("T")[0],className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"preferredTime",className:"block text-sm font-medium text-gray-700 mb-1",children:"Preferred Time"}),e.jsxs("select",{id:"preferredTime",name:"preferredTime",value:d.preferredTime,onChange:h,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",children:[e.jsx("option",{value:"",children:"Select time"}),e.jsx("option",{value:"morning",children:"Morning (8:00 AM - 12:00 PM)"}),e.jsx("option",{value:"afternoon",children:"Afternoon (12:00 PM - 5:00 PM)"}),e.jsx("option",{value:"evening",children:"Evening (5:00 PM - 8:00 PM)"})]})]}),e.jsxs("div",{className:"md:col-span-2",children:[e.jsx("label",{htmlFor:"additionalNotes",className:"block text-sm font-medium text-gray-700 mb-1",children:"Additional Notes"}),e.jsx("textarea",{id:"additionalNotes",name:"additionalNotes",value:d.additionalNotes,onChange:h,rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",placeholder:"Any special requirements or additional information..."})]})]})]}),e.jsxs("div",{className:"mb-8 p-4 bg-blue-50 border border-blue-200 rounded-lg",children:[e.jsx("h4",{className:"font-semibold text-blue-800 mb-2",children:"GPS Installation Service Includes:"}),e.jsxs("ul",{className:"text-sm text-blue-700 space-y-1",children:[e.jsx("li",{children:"• Professional GPS device installation"}),e.jsx("li",{children:"• Real-time tracking setup"}),e.jsx("li",{children:"• Mobile app configuration"}),e.jsx("li",{children:"• Training on how to use the system"}),e.jsx("li",{children:"• 1-year warranty on installation"}),e.jsx("li",{children:"• 24/7 technical support"})]}),e.jsxs("p",{className:"text-sm text-blue-600 mt-3",children:[e.jsx("strong",{children:"Pricing:"})," Our team will provide a detailed quote based on your vehicle type and requirements."]})]}),e.jsxs("div",{className:"flex justify-end space-x-4",children:[e.jsx(S,{type:"button",variant:"outline",onClick:()=>s("/owner/dashboard"),children:"Cancel"}),e.jsx(S,{type:"submit",disabled:a,children:a?"Submitting Request...":"Submit GPS Installation Request"})]})]})]})]})})})},me=({element:s,isAuthenticated:t,redirectPath:a="/login"})=>t?e.jsx(e.Fragment,{children:s}):e.jsx(ht,{to:a,replace:!0});function Xi(){return y.useEffect(()=>{document.title="Park & Rent - Peer-to-Peer Car Rentals"},[]),e.jsx(ii,{children:e.jsx(ni,{children:e.jsx(ci,{children:e.jsx(di,{children:e.jsx(xi,{children:e.jsxs(vr,{children:[e.jsx(W,{path:"/",element:e.jsx(Ii,{})}),e.jsx(W,{path:"/cars",element:e.jsx(Li,{})}),e.jsx(W,{path:"/cars/:id",element:e.jsx(Ui,{})}),e.jsx(W,{path:"/drivers",element:e.jsx(Bi,{})}),e.jsx(W,{path:"/drivers/:id",element:e.jsx(Hi,{})}),e.jsx(W,{path:"/login",element:e.jsx(Vi,{})}),e.jsx(W,{path:"/signup",element:e.jsx(Gi,{})}),e.jsx(W,{path:"/account/verification",element:e.jsx(me,{element:e.jsx(Wi,{}),isAuthenticated:!0})}),e.jsx(W,{path:"/owner",element:e.jsx(me,{element:e.jsx(ut,{}),isAuthenticated:!0})}),e.jsx(W,{path:"/owner/dashboard",element:e.jsx(me,{element:e.jsx(ut,{}),isAuthenticated:!0})}),e.jsx(W,{path:"/owner/add-car",element:e.jsx(me,{element:e.jsx(Ji,{}),isAuthenticated:!0})}),e.jsx(W,{path:"/owner/edit-car/:id",element:e.jsx(me,{element:e.jsx(Ki,{}),isAuthenticated:!0})}),e.jsx(W,{path:"/owner/gps-request",element:e.jsx(me,{element:e.jsx(Zi,{}),isAuthenticated:!0})}),e.jsx(W,{path:"/driver/register",element:e.jsx(me,{element:e.jsx(qi,{}),isAuthenticated:!0})}),e.jsx(W,{path:"/driver",element:e.jsx(me,{element:e.jsx(dt,{}),isAuthenticated:!0})}),e.jsx(W,{path:"/driver/dashboard",element:e.jsx(me,{element:e.jsx(dt,{}),isAuthenticated:!0})}),e.jsx(W,{path:"/account",element:e.jsx(me,{element:e.jsx($i,{}),isAuthenticated:!0})}),e.jsx(W,{path:"/admin",element:e.jsx(me,{element:e.jsx(xt,{}),isAuthenticated:!0})}),e.jsx(W,{path:"/admin/dashboard",element:e.jsx(me,{element:e.jsx(xt,{}),isAuthenticated:!0})}),e.jsx(W,{path:"*",element:e.jsx(ht,{to:"/",replace:!0})})]})})})})})})}gt(document.getElementById("root")).render(e.jsx(y.StrictMode,{children:e.jsx(Nr,{children:e.jsx(Xi,{})})}));
