import{j as e,$ as p,Y as a}from"./app-Cj-kSxmT.js";import{c as o,C as r}from"./car-CXkXzXL9.js";import{S as j,C as N}from"./settings-CPuJKZnh.js";import{U as c}from"./users-BA_EXnY7.js";import{U as i}from"./user-round-Dc3ptzYJ.js";import{C as f}from"./calendar-C03a_VmS.js";import{E as m}from"./eye-fGPZAapK.js";import{S as x}from"./shield-C7t0tPDj.js";/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const u=[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]],y=o("trending-up",u);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const v=[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]],b=o("triangle-alert",v);function M({auth:h,stats:l,recent_users:d,recent_bookings:n,pending_verifications:w,pending_gps_requests:_}){const t=s=>`RWF ${s.toLocaleString()}`,g=s=>new Date(s).toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"});return e.jsxs(e.Fragment,{children:[e.jsx(p,{title:"Admin Dashboard"}),e.jsxs("div",{className:"min-h-screen bg-gray-50",children:[e.jsx("header",{className:"bg-white shadow-md",children:e.jsx("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"flex justify-between items-center py-4",children:[e.jsxs(a,{href:"/",className:"flex items-center space-x-2",children:[e.jsx(r,{className:"h-8 w-8 text-primary-600"}),e.jsx("span",{className:"text-xl font-bold text-gray-900",children:"Park & Rent"})]}),e.jsxs("nav",{className:"hidden md:flex space-x-8",children:[e.jsx(a,{href:"/admin/dashboard",className:"text-primary-600 font-medium",children:"Admin Dashboard"}),e.jsx(a,{href:"/admin/users",className:"text-gray-700 hover:text-primary-600",children:"Users"}),e.jsx(a,{href:"/admin/cars",className:"text-gray-700 hover:text-primary-600",children:"Cars"}),e.jsx(a,{href:"/admin/drivers",className:"text-gray-700 hover:text-primary-600",children:"Drivers"}),e.jsx(a,{href:"/admin/bookings",className:"text-gray-700 hover:text-primary-600",children:"Bookings"})]}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("span",{className:"text-gray-700",children:["Admin: ",h.user.name]}),e.jsx(a,{href:"/dashboard",className:"text-gray-700 hover:text-primary-600",children:e.jsx(j,{size:20})})]})]})})}),e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Admin Dashboard"}),e.jsx("p",{className:"text-gray-600",children:"Manage your Park & Rent platform from here."})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[e.jsx("div",{className:"bg-white p-6 rounded-lg shadow-md",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"p-3 bg-blue-100 rounded-full",children:e.jsx(c,{className:"h-6 w-6 text-blue-600"})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Total Users"}),e.jsx("p",{className:"text-2xl font-bold text-blue-600",children:l.total_users})]})]})}),e.jsx("div",{className:"bg-white p-6 rounded-lg shadow-md",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"p-3 bg-green-100 rounded-full",children:e.jsx(r,{className:"h-6 w-6 text-green-600"})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Total Cars"}),e.jsx("p",{className:"text-2xl font-bold text-green-600",children:l.total_cars})]})]})}),e.jsx("div",{className:"bg-white p-6 rounded-lg shadow-md",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"p-3 bg-purple-100 rounded-full",children:e.jsx(i,{className:"h-6 w-6 text-purple-600"})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Total Drivers"}),e.jsx("p",{className:"text-2xl font-bold text-purple-600",children:l.total_drivers})]})]})}),e.jsx("div",{className:"bg-white p-6 rounded-lg shadow-md",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"p-3 bg-yellow-100 rounded-full",children:e.jsx(f,{className:"h-6 w-6 text-yellow-600"})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Total Bookings"}),e.jsx("p",{className:"text-2xl font-bold text-yellow-600",children:l.total_bookings})]})]})})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8",children:[e.jsx("div",{className:"bg-white p-6 rounded-lg shadow-md",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"p-3 bg-indigo-100 rounded-full",children:e.jsx(N,{className:"h-6 w-6 text-indigo-600"})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Total Revenue"}),e.jsx("p",{className:"text-2xl font-bold text-indigo-600",children:t(l.total_revenue)})]})]})}),e.jsx("div",{className:"bg-white p-6 rounded-lg shadow-md",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"p-3 bg-emerald-100 rounded-full",children:e.jsx(y,{className:"h-6 w-6 text-emerald-600"})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Monthly Revenue"}),e.jsx("p",{className:"text-2xl font-bold text-emerald-600",children:t(l.monthly_revenue)})]})]})})]}),(l.pending_verifications>0||l.pending_gps_requests>0)&&e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8",children:[l.pending_verifications>0&&e.jsxs("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(b,{className:"h-5 w-5 text-yellow-600 mr-2"}),e.jsx("h3",{className:"text-lg font-medium text-yellow-800",children:"Pending Verifications"})]}),e.jsxs("p",{className:"text-yellow-700 mt-1",children:[l.pending_verifications," driver",l.pending_verifications!==1?"s":""," waiting for license verification."]}),e.jsxs(a,{href:"/admin/drivers?status=pending",className:"inline-flex items-center mt-2 text-yellow-800 hover:text-yellow-900",children:["Review Verifications",e.jsx(m,{size:16,className:"ml-1"})]})]}),l.pending_gps_requests>0&&e.jsxs("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(x,{className:"h-5 w-5 text-blue-600 mr-2"}),e.jsx("h3",{className:"text-lg font-medium text-blue-800",children:"GPS Installation Requests"})]}),e.jsxs("p",{className:"text-blue-700 mt-1",children:[l.pending_gps_requests," GPS installation request",l.pending_gps_requests!==1?"s":""," pending review."]}),e.jsxs(a,{href:"/admin/gps-requests",className:"inline-flex items-center mt-2 text-blue-800 hover:text-blue-900",children:["Review Requests",e.jsx(m,{size:16,className:"ml-1"})]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Recent Users"}),e.jsx(a,{href:"/admin/users",className:"text-primary-600 hover:text-primary-700 text-sm font-medium",children:"View All"})]}),d.length>0?e.jsx("div",{className:"space-y-4",children:d.map(s=>e.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center mr-3",children:e.jsx("span",{className:"text-primary-600 font-semibold text-sm",children:s.name.charAt(0).toUpperCase()})}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-gray-900",children:s.name}),e.jsx("p",{className:"text-sm text-gray-600",children:s.email})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("span",{className:`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${s.role==="admin"?"bg-red-100 text-red-800":s.role==="owner"?"bg-blue-100 text-blue-800":s.role==="driver"?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"}`,children:s.role.charAt(0).toUpperCase()+s.role.slice(1)}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:g(s.created_at)})]})]},s.id))}):e.jsx("p",{className:"text-gray-500 text-center py-4",children:"No recent users"})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Recent Bookings"}),e.jsx(a,{href:"/admin/bookings",className:"text-primary-600 hover:text-primary-700 text-sm font-medium",children:"View All"})]}),n.length>0?e.jsx("div",{className:"space-y-4",children:n.map(s=>e.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center mr-3",children:s.item_type==="car"?e.jsx(r,{size:20,className:"text-primary-600"}):e.jsx(i,{size:20,className:"text-primary-600"})}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-gray-900",children:s.item_type==="car"?"Car Rental":"Driver Service"}),e.jsxs("p",{className:"text-sm text-gray-600",children:["Booking #",s.id]})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("span",{className:`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${s.status==="confirmed"?"bg-green-100 text-green-800":s.status==="pending"?"bg-yellow-100 text-yellow-800":s.status==="completed"?"bg-blue-100 text-blue-800":"bg-red-100 text-red-800"}`,children:s.status.charAt(0).toUpperCase()+s.status.slice(1)}),e.jsx("p",{className:"text-sm font-medium text-gray-900 mt-1",children:t(s.total_price)})]})]},s.id))}):e.jsx("p",{className:"text-gray-500 text-center py-4",children:"No recent bookings"})]})]}),e.jsxs("div",{className:"mt-8 bg-white rounded-lg shadow-md p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Quick Actions"}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[e.jsxs(a,{href:"/admin/users",className:"flex flex-col items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors",children:[e.jsx(c,{size:24,className:"text-primary-600 mb-2"}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:"Manage Users"})]}),e.jsxs(a,{href:"/admin/cars",className:"flex flex-col items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors",children:[e.jsx(r,{size:24,className:"text-primary-600 mb-2"}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:"Manage Cars"})]}),e.jsxs(a,{href:"/admin/drivers",className:"flex flex-col items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors",children:[e.jsx(i,{size:24,className:"text-primary-600 mb-2"}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:"Manage Drivers"})]}),e.jsxs(a,{href:"/admin/gps-requests",className:"flex flex-col items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors",children:[e.jsx(x,{size:24,className:"text-primary-600 mb-2"}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:"GPS Requests"})]})]})]})]})]})]})}export{M as default};
