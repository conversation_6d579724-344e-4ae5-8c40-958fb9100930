import{v,j as e,$ as w,Y as t}from"./app-Cj-kSxmT.js";import{C as r}from"./car-CXkXzXL9.js";import{U as m}from"./user-round-Dc3ptzYJ.js";import{C as n}from"./calendar-C03a_VmS.js";import{C as b}from"./clock-B-cuJnHn.js";import{M as d}from"./map-pin-DpBG7oTH.js";import{M as C}from"./mail-BdhUp7Lm.js";import{P as _}from"./phone-DVvySU_L.js";import{M as x}from"./message-circle-CPYXbNOk.js";import{C as D}from"./circle-alert-CML8Sh-8.js";import{C as z,a as h}from"./circle-x-BmQPBxO0.js";function Y({auth:o,booking:s}){const{post:p,processing:l}=v(),j=a=>{switch(a){case"confirmed":return e.jsx(h,{size:20,className:"text-green-600"});case"completed":return e.jsx(h,{size:20,className:"text-blue-600"});case"cancelled":return e.jsx(z,{size:20,className:"text-red-600"});case"pending":default:return e.jsx(D,{size:20,className:"text-yellow-600"})}},u=a=>{switch(a){case"confirmed":return"bg-green-100 text-green-800";case"completed":return"bg-blue-100 text-blue-800";case"cancelled":return"bg-red-100 text-red-800";case"pending":default:return"bg-yellow-100 text-yellow-800"}},i=a=>new Date(a).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}),c=()=>{const a=new Date(s.start_time),g=new Date(s.end_time);return Math.ceil((g.getTime()-a.getTime())/(1e3*60*60))},N=()=>{confirm("Are you sure you want to cancel this booking?")&&p(`/bookings/${s.id}/cancel`)},y=()=>s.status==="pending"||s.status==="confirmed",f=()=>new Date(s.start_time)>new Date;return e.jsxs(e.Fragment,{children:[e.jsx(w,{title:`Booking #${s.id}`}),e.jsxs("div",{className:"min-h-screen bg-gray-50",children:[e.jsx("header",{className:"bg-white shadow-md sticky top-0 z-50",children:e.jsx("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"flex justify-between items-center py-4",children:[e.jsxs(t,{href:"/",className:"flex items-center space-x-2",children:[e.jsx(r,{className:"h-8 w-8 text-primary-600"}),e.jsx("span",{className:"text-xl font-bold text-gray-900",children:"Park & Rent"})]}),e.jsxs("nav",{className:"hidden md:flex space-x-8",children:[e.jsx(t,{href:"/",className:"text-gray-700 hover:text-primary-600",children:"Home"}),e.jsx(t,{href:"/cars",className:"text-gray-700 hover:text-primary-600",children:"Browse Cars"}),e.jsx(t,{href:"/drivers",className:"text-gray-700 hover:text-primary-600",children:"Hire a Driver"}),e.jsx(t,{href:"/dashboard",className:"text-gray-700 hover:text-primary-600",children:"Dashboard"})]}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("span",{className:"text-gray-700",children:["Welcome, ",o.user.name]}),e.jsx(t,{href:"/dashboard",className:"bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700",children:"Dashboard"})]})]})})}),e.jsx("div",{className:"bg-white border-b",children:e.jsx("div",{className:"container mx-auto px-4 py-3",children:e.jsxs("nav",{className:"flex items-center space-x-2 text-sm",children:[e.jsx(t,{href:"/",className:"text-gray-600 hover:text-primary-600",children:"Home"}),e.jsx("span",{className:"text-gray-400",children:"/"}),e.jsx(t,{href:"/bookings",className:"text-gray-600 hover:text-primary-600",children:"Bookings"}),e.jsx("span",{className:"text-gray-400",children:"/"}),e.jsxs("span",{className:"text-gray-900",children:["Booking #",s.id]})]})})}),e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[e.jsxs("div",{className:"lg:col-span-2",children:[e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6 mb-6",children:[e.jsxs("div",{className:"flex items-start justify-between mb-6",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mr-4",children:s.item_type==="car"?e.jsx(r,{size:32,className:"text-primary-600"}):e.jsx(m,{size:32,className:"text-primary-600"})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:s.item_type==="car"?"Car Rental":"Driver Service"}),e.jsxs("p",{className:"text-gray-600",children:["Booking #",s.id]}),e.jsxs("p",{className:"text-sm text-gray-500",children:["Created on ",new Date(s.created_at).toLocaleDateString()]})]})]}),e.jsx("div",{className:"text-right",children:e.jsxs("span",{className:`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${u(s.status)}`,children:[j(s.status),e.jsx("span",{className:"ml-2",children:s.status.charAt(0).toUpperCase()+s.status.slice(1)})]})})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[e.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[e.jsxs("div",{className:"flex items-center text-gray-600 mb-2",children:[e.jsx(n,{size:16,className:"mr-2"}),e.jsx("span",{className:"font-medium",children:"Start Time"})]}),e.jsx("p",{className:"text-lg font-semibold text-gray-900",children:i(s.start_time)})]}),e.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[e.jsxs("div",{className:"flex items-center text-gray-600 mb-2",children:[e.jsx(n,{size:16,className:"mr-2"}),e.jsx("span",{className:"font-medium",children:"End Time"})]}),e.jsx("p",{className:"text-lg font-semibold text-gray-900",children:i(s.end_time)})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-primary-50 rounded-lg",children:[e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"flex items-center justify-center text-primary-600 mb-1",children:[e.jsx(b,{size:16,className:"mr-1"}),e.jsx("span",{className:"font-medium",children:"Duration"})]}),e.jsxs("p",{className:"text-xl font-bold text-gray-900",children:[c()," hours"]})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"flex items-center justify-center text-primary-600 mb-1",children:e.jsx("span",{className:"font-medium",children:"Total Price"})}),e.jsxs("p",{className:"text-xl font-bold text-primary-600",children:["RWF ",s.total_price.toLocaleString()]})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"flex items-center justify-center text-primary-600 mb-1",children:e.jsx("span",{className:"font-medium",children:"Payment Status"})}),e.jsx("span",{className:`inline-flex items-center px-2 py-1 rounded-full text-sm font-medium ${s.is_paid?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"}`,children:s.is_paid?"Paid":"Pending"})]})]}),s.notes&&e.jsxs("div",{className:"mt-6 pt-6 border-t",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Booking Notes"}),e.jsx("p",{className:"text-gray-700 bg-gray-50 p-4 rounded-lg",children:s.notes})]})]}),s.item&&e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6 mb-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:s.item_type==="car"?"Car Details":"Driver Details"}),s.item_type==="car"?e.jsxs("div",{className:"flex items-start",children:[e.jsx("div",{className:"w-24 h-24 bg-gray-200 rounded-lg flex items-center justify-center mr-4",children:s.item.images&&s.item.images.length>0?e.jsx("img",{src:s.item.images[0],alt:`${s.item.make} ${s.item.model}`,className:"w-full h-full object-cover rounded-lg"}):e.jsx(r,{size:32,className:"text-gray-400"})}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("h4",{className:"text-xl font-semibold text-gray-900",children:[s.item.make," ",s.item.model," (",s.item.year,")"]}),e.jsxs("div",{className:"flex items-center text-gray-600 mt-1",children:[e.jsx(d,{size:16,className:"mr-1"}),e.jsx("span",{children:s.item.location})]}),e.jsx("p",{className:"text-gray-700 mt-2",children:s.item.description}),e.jsx("div",{className:"mt-3",children:e.jsxs("span",{className:"text-lg font-semibold text-primary-600",children:["RWF ",s.item.price_per_hour.toLocaleString(),"/hour"]})})]})]}):e.jsxs("div",{className:"flex items-start",children:[e.jsx("div",{className:"w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mr-4",children:s.item.profile_image?e.jsx("img",{src:s.item.profile_image,alt:s.item.name,className:"w-full h-full object-cover rounded-full"}):e.jsx(m,{size:24,className:"text-gray-400"})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h4",{className:"text-xl font-semibold text-gray-900",children:s.item.name}),e.jsxs("div",{className:"flex items-center text-gray-600 mt-1",children:[e.jsx(d,{size:16,className:"mr-1"}),e.jsx("span",{children:s.item.location})]}),e.jsxs("p",{className:"text-sm text-gray-600 mt-1",children:[s.item.experience," years experience • Age ",s.item.age]}),e.jsx("div",{className:"mt-3",children:e.jsxs("span",{className:"text-lg font-semibold text-primary-600",children:["RWF ",s.item.price_per_hour.toLocaleString(),"/hour"]})})]})]})]}),s.item&&s.item.owner&&e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:s.item_type==="car"?"Car Owner":"Driver Contact"}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mr-4",children:e.jsx("span",{className:"text-primary-600 font-semibold",children:s.item.owner.name.charAt(0).toUpperCase()})}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-gray-900",children:s.item.owner.name}),e.jsxs("div",{className:"flex items-center text-gray-600 text-sm mt-1",children:[e.jsx(C,{size:14,className:"mr-1"}),e.jsx("span",{children:s.item.owner.email})]}),s.item.owner.phone_number&&e.jsxs("div",{className:"flex items-center text-gray-600 text-sm mt-1",children:[e.jsx(_,{size:14,className:"mr-1"}),e.jsx("span",{children:s.item.owner.phone_number})]})]})]}),e.jsxs("button",{className:"flex items-center px-4 py-2 bg-primary-100 text-primary-700 rounded-md hover:bg-primary-200",children:[e.jsx(x,{size:16,className:"mr-2"}),"Message"]})]})]})]}),e.jsx("div",{className:"lg:col-span-1",children:e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6 sticky top-24",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Booking Actions"}),e.jsxs("div",{className:"space-y-3",children:[s.status==="pending"&&e.jsx("div",{className:"p-3 bg-yellow-50 border border-yellow-200 rounded-md",children:e.jsx("p",{className:"text-sm text-yellow-800",children:"Your booking is pending approval. You'll be notified once it's confirmed."})}),s.status==="confirmed"&&f()&&e.jsx("div",{className:"p-3 bg-green-50 border border-green-200 rounded-md",children:e.jsx("p",{className:"text-sm text-green-800",children:"Your booking is confirmed! Contact details are available above."})}),s.status==="completed"&&e.jsx("div",{className:"p-3 bg-blue-50 border border-blue-200 rounded-md",children:e.jsx("p",{className:"text-sm text-blue-800",children:"This booking has been completed. Thank you for using Park & Rent!"})}),y()&&e.jsx("button",{onClick:N,disabled:l,className:"w-full bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700 disabled:opacity-50",children:l?"Cancelling...":"Cancel Booking"}),e.jsxs("button",{className:"w-full flex items-center justify-center py-2 px-4 border border-gray-300 rounded-md hover:bg-gray-50",children:[e.jsx(x,{size:16,className:"mr-2"}),"Send Message"]}),e.jsx(t,{href:"/bookings",className:"w-full bg-gray-100 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-200 text-center block",children:"Back to Bookings"})]}),e.jsxs("div",{className:"mt-6 pt-6 border-t",children:[e.jsx("h4",{className:"font-medium text-gray-900 mb-3",children:"Booking Summary"}),e.jsxs("div",{className:"space-y-2 text-sm",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Booking ID:"}),e.jsxs("span",{className:"font-medium",children:["#",s.id]})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Type:"}),e.jsx("span",{className:"font-medium",children:s.item_type==="car"?"Car Rental":"Driver Service"})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Duration:"}),e.jsxs("span",{className:"font-medium",children:[c()," hours"]})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Status:"}),e.jsx("span",{className:"font-medium",children:s.status.charAt(0).toUpperCase()+s.status.slice(1)})]}),e.jsxs("div",{className:"flex justify-between pt-2 border-t",children:[e.jsx("span",{className:"text-gray-900 font-medium",children:"Total:"}),e.jsxs("span",{className:"font-bold text-primary-600",children:["RWF ",s.total_price.toLocaleString()]})]})]})]})]})})]})})]})]})}export{Y as default};
