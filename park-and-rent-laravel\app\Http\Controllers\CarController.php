<?php

namespace App\Http\Controllers;

use App\Models\Car;
use App\Models\Booking;
use Illuminate\Http\Request;
use Inertia\Inertia;

class CarController extends Controller
{
    public function index(Request $request)
    {
        $query = Car::with('owner')
            ->where('is_active', true);

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('make', 'like', "%{$search}%")
                  ->orWhere('model', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        if ($request->filled('location')) {
            $query->where('location', 'like', "%{$request->location}%");
        }

        if ($request->filled('make')) {
            $query->where('make', $request->make);
        }

        if ($request->filled('min_price')) {
            $query->where('price_per_hour', '>=', $request->min_price);
        }

        if ($request->filled('max_price')) {
            $query->where('price_per_hour', '<=', $request->max_price);
        }

        $cars = $query->latest()->get();

        return Inertia::render('Cars/Index', [
            'cars' => $cars,
            'filters' => $request->only(['search', 'location', 'make', 'min_price', 'max_price']),
        ]);
    }

    public function show(Car $car)
    {
        $car->load('owner');

        return Inertia::render('Cars/Show', [
            'car' => $car,
        ]);
    }

    public function create()
    {
        return Inertia::render('Cars/Create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'make' => 'required|string|max:255',
            'model' => 'required|string|max:255',
            'year' => 'required|integer|min:1900|max:' . (date('Y') + 1),
            'color' => 'nullable|string|max:100',
            'license_plate' => 'nullable|string|max:20',
            'location' => 'required|string|max:255',
            'description' => 'nullable|string|max:2000',
            'price_per_hour' => 'required|numeric|min:0',
            'fuel_type' => 'required|in:petrol,diesel,hybrid,electric',
            'transmission' => 'required|in:manual,automatic',
            'seats' => 'required|integer|min:1|max:20',
            'features' => 'nullable|string',
            'is_active' => 'boolean',
            'images' => 'nullable|array|max:10',
            'images.*' => 'image|mimes:jpeg,png,jpg,gif,webp|max:5120',
        ]);

        $carData = $request->except(['images']);
        $carData['owner_id'] = auth()->id();
        $carData['features'] = $request->features ? json_decode($request->features, true) : [];
        $carData['is_available'] = $request->boolean('is_active', true);

        // Handle image uploads
        $images = [];
        if ($request->hasFile('images')) {
            foreach ($request->file('images') as $image) {
                $filename = \Illuminate\Support\Str::random(40) . '.' . $image->getClientOriginalExtension();
                $path = $image->storeAs('cars', $filename, 'public');
                $images[] = \Illuminate\Support\Facades\Storage::url($path);
            }
        }
        $carData['images'] = $images;

        Car::create($carData);

        return redirect()->route('dashboard')
            ->with('message', 'Car added successfully! It is now available for rent.');
    }

    public function book(Request $request, Car $car)
    {
        $request->validate([
            'start_time' => 'required|date|after:now',
            'end_time' => 'required|date|after:start_time',
            'notes' => 'nullable|string|max:1000',
        ]);

        // Check if car is available
        if (!$car->is_active) {
            return back()->withErrors(['error' => 'This car is not available for booking.']);
        }

        // Check for conflicting bookings
        $conflictingBooking = Booking::where('item_type', 'car')
            ->where('item_id', $car->id)
            ->where('status', '!=', 'cancelled')
            ->where(function ($query) use ($request) {
                $query->whereBetween('start_time', [$request->start_time, $request->end_time])
                      ->orWhereBetween('end_time', [$request->start_time, $request->end_time])
                      ->orWhere(function ($q) use ($request) {
                          $q->where('start_time', '<=', $request->start_time)
                            ->where('end_time', '>=', $request->end_time);
                      });
            })
            ->exists();

        if ($conflictingBooking) {
            return back()->withErrors(['error' => 'This car is already booked for the selected time period.']);
        }

        // Calculate total price
        $startTime = new \DateTime($request->start_time);
        $endTime = new \DateTime($request->end_time);
        $hours = ceil(($endTime->getTimestamp() - $startTime->getTimestamp()) / 3600);
        $totalPrice = $hours * $car->price_per_hour;

        // Create booking
        $booking = Booking::create([
            'user_id' => auth()->id(),
            'item_type' => 'car',
            'item_id' => $car->id,
            'start_time' => $request->start_time,
            'end_time' => $request->end_time,
            'total_price' => $totalPrice,
            'notes' => $request->notes,
            'status' => 'pending',
        ]);

        return redirect()->route('bookings.show', $booking)
            ->with('message', 'Booking request submitted successfully! The car owner will review your request.');
    }
}
