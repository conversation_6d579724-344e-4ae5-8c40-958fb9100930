import React, { useState } from 'react';
import { Head, Link, useForm } from '@inertiajs/react';
import { Car, ArrowLeft, Save, MapPin, DollarSign, Calendar, Settings } from 'lucide-react';
import { PageProps } from '@/types';
import FileUpload from '@/Components/FileUpload';

export default function CarCreate({ auth }: PageProps) {
  const [selectedImages, setSelectedImages] = useState<File[]>([]);
  
  const { data, setData, post, processing, errors } = useForm({
    make: '',
    model: '',
    year: new Date().getFullYear(),
    color: '',
    license_plate: '',
    location: '',
    description: '',
    price_per_hour: '',
    fuel_type: 'petrol',
    transmission: 'manual',
    seats: 4,
    features: [] as string[],
    is_active: true,
  });

  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: 30 }, (_, i) => currentYear - i);

  const fuelTypes = [
    { value: 'petrol', label: 'Petrol' },
    { value: 'diesel', label: 'Diesel' },
    { value: 'hybrid', label: 'Hybrid' },
    { value: 'electric', label: 'Electric' },
  ];

  const transmissionTypes = [
    { value: 'manual', label: 'Manual' },
    { value: 'automatic', label: 'Automatic' },
  ];

  const availableFeatures = [
    'Air Conditioning',
    'GPS Navigation',
    'Bluetooth',
    'USB Charging',
    'Backup Camera',
    'Parking Sensors',
    'Sunroof',
    'Leather Seats',
    'Heated Seats',
    'Cruise Control',
    'Keyless Entry',
    'Push Start',
    'Premium Sound System',
    'WiFi Hotspot',
    'Child Safety Locks',
    'Anti-lock Brakes (ABS)',
    'Airbags',
    'Traction Control',
  ];

  const handleFeatureToggle = (feature: string) => {
    const updatedFeatures = data.features.includes(feature)
      ? data.features.filter(f => f !== feature)
      : [...data.features, feature];
    setData('features', updatedFeatures);
  };

  const handleImageSelect = (files: File[]) => {
    setSelectedImages(files);
  };

  const submit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const formData = new FormData();
    
    // Add form data
    Object.entries(data).forEach(([key, value]) => {
      if (key === 'features') {
        formData.append(key, JSON.stringify(value));
      } else {
        formData.append(key, value.toString());
      }
    });

    // Add images
    selectedImages.forEach((image, index) => {
      formData.append(`images[${index}]`, image);
    });

    post('/cars', {
      data: formData,
      forceFormData: true,
    });
  };

  return (
    <>
      <Head title="Add New Car" />
      
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <header className="bg-white shadow-md sticky top-0 z-50">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-4">
              <Link href="/" className="flex items-center space-x-2">
                <Car className="h-8 w-8 text-primary-600" />
                <span className="text-xl font-bold text-gray-900">Park & Rent</span>
              </Link>

              <nav className="hidden md:flex space-x-8">
                <Link href="/" className="text-gray-700 hover:text-primary-600">
                  Home
                </Link>
                <Link href="/cars" className="text-gray-700 hover:text-primary-600">
                  Browse Cars
                </Link>
                <Link href="/drivers" className="text-gray-700 hover:text-primary-600">
                  Hire a Driver
                </Link>
                <Link href="/dashboard" className="text-gray-700 hover:text-primary-600">
                  Dashboard
                </Link>
              </nav>

              <div className="flex items-center space-x-4">
                <span className="text-gray-700">Welcome, {auth.user.name}</span>
                <Link
                  href="/dashboard"
                  className="bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700"
                >
                  Dashboard
                </Link>
              </div>
            </div>
          </div>
        </header>

        {/* Breadcrumb */}
        <div className="bg-white border-b">
          <div className="container mx-auto px-4 py-3">
            <nav className="flex items-center space-x-2 text-sm">
              <Link href="/" className="text-gray-600 hover:text-primary-600">Home</Link>
              <span className="text-gray-400">/</span>
              <Link href="/dashboard" className="text-gray-600 hover:text-primary-600">Dashboard</Link>
              <span className="text-gray-400">/</span>
              <span className="text-gray-900">Add New Car</span>
            </nav>
          </div>
        </div>

        <div className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            {/* Header */}
            <div className="flex items-center justify-between mb-8">
              <div className="flex items-center">
                <Link
                  href="/dashboard"
                  className="mr-4 p-2 hover:bg-gray-100 rounded-full"
                >
                  <ArrowLeft size={20} />
                </Link>
                <div>
                  <h1 className="text-3xl font-bold text-gray-900">Add New Car</h1>
                  <p className="text-gray-600 mt-1">List your car for rent on Park & Rent</p>
                </div>
              </div>
            </div>

            <form onSubmit={submit} className="space-y-8">
              {/* Basic Information */}
              <div className="bg-white rounded-lg shadow-md p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-6">Basic Information</h2>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Make *
                    </label>
                    <input
                      type="text"
                      value={data.make}
                      onChange={(e) => setData('make', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
                      placeholder="e.g., Toyota, Honda, BMW"
                      required
                    />
                    {errors.make && (
                      <p className="mt-1 text-sm text-red-600">{errors.make}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Model *
                    </label>
                    <input
                      type="text"
                      value={data.model}
                      onChange={(e) => setData('model', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
                      placeholder="e.g., Camry, Civic, X5"
                      required
                    />
                    {errors.model && (
                      <p className="mt-1 text-sm text-red-600">{errors.model}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Year *
                    </label>
                    <select
                      value={data.year}
                      onChange={(e) => setData('year', parseInt(e.target.value))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
                      required
                    >
                      {years.map(year => (
                        <option key={year} value={year}>{year}</option>
                      ))}
                    </select>
                    {errors.year && (
                      <p className="mt-1 text-sm text-red-600">{errors.year}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Color
                    </label>
                    <input
                      type="text"
                      value={data.color}
                      onChange={(e) => setData('color', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
                      placeholder="e.g., White, Black, Silver"
                    />
                    {errors.color && (
                      <p className="mt-1 text-sm text-red-600">{errors.color}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      License Plate
                    </label>
                    <input
                      type="text"
                      value={data.license_plate}
                      onChange={(e) => setData('license_plate', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
                      placeholder="e.g., ABC 123"
                    />
                    {errors.license_plate && (
                      <p className="mt-1 text-sm text-red-600">{errors.license_plate}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Location *
                    </label>
                    <div className="relative">
                      <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                      <input
                        type="text"
                        value={data.location}
                        onChange={(e) => setData('location', e.target.value)}
                        className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
                        placeholder="e.g., Kigali, Nyamirambo"
                        required
                      />
                    </div>
                    {errors.location && (
                      <p className="mt-1 text-sm text-red-600">{errors.location}</p>
                    )}
                  </div>
                </div>

                <div className="mt-6">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <textarea
                    value={data.description}
                    onChange={(e) => setData('description', e.target.value)}
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
                    placeholder="Describe your car, its condition, and any special notes for renters..."
                  />
                  {errors.description && (
                    <p className="mt-1 text-sm text-red-600">{errors.description}</p>
                  )}
                </div>
              </div>

              {/* Specifications */}
              <div className="bg-white rounded-lg shadow-md p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-6">Specifications</h2>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Fuel Type *
                    </label>
                    <select
                      value={data.fuel_type}
                      onChange={(e) => setData('fuel_type', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
                      required
                    >
                      {fuelTypes.map(type => (
                        <option key={type.value} value={type.value}>{type.label}</option>
                      ))}
                    </select>
                    {errors.fuel_type && (
                      <p className="mt-1 text-sm text-red-600">{errors.fuel_type}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Transmission *
                    </label>
                    <select
                      value={data.transmission}
                      onChange={(e) => setData('transmission', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
                      required
                    >
                      {transmissionTypes.map(type => (
                        <option key={type.value} value={type.value}>{type.label}</option>
                      ))}
                    </select>
                    {errors.transmission && (
                      <p className="mt-1 text-sm text-red-600">{errors.transmission}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Number of Seats *
                    </label>
                    <select
                      value={data.seats}
                      onChange={(e) => setData('seats', parseInt(e.target.value))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
                      required
                    >
                      {[2, 4, 5, 6, 7, 8, 9].map(seats => (
                        <option key={seats} value={seats}>{seats} seats</option>
                      ))}
                    </select>
                    {errors.seats && (
                      <p className="mt-1 text-sm text-red-600">{errors.seats}</p>
                    )}
                  </div>
                </div>
              </div>

              {/* Pricing */}
              <div className="bg-white rounded-lg shadow-md p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-6">Pricing</h2>
                
                <div className="max-w-md">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Price per Hour (RWF) *
                  </label>
                  <div className="relative">
                    <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                    <input
                      type="number"
                      value={data.price_per_hour}
                      onChange={(e) => setData('price_per_hour', e.target.value)}
                      className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
                      placeholder="e.g., 5000"
                      min="0"
                      step="100"
                      required
                    />
                  </div>
                  {errors.price_per_hour && (
                    <p className="mt-1 text-sm text-red-600">{errors.price_per_hour}</p>
                  )}
                  <p className="mt-1 text-sm text-gray-600">
                    Set a competitive hourly rate for your car
                  </p>
                </div>
              </div>

              {/* Features */}
              <div className="bg-white rounded-lg shadow-md p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-6">Features & Amenities</h2>
                
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                  {availableFeatures.map(feature => (
                    <label key={feature} className="flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
                      <input
                        type="checkbox"
                        checked={data.features.includes(feature)}
                        onChange={() => handleFeatureToggle(feature)}
                        className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                      />
                      <span className="ml-2 text-sm text-gray-700">{feature}</span>
                    </label>
                  ))}
                </div>
                {errors.features && (
                  <p className="mt-2 text-sm text-red-600">{errors.features}</p>
                )}
              </div>

              {/* Images */}
              <div className="bg-white rounded-lg shadow-md p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-6">Car Images</h2>
                
                <FileUpload
                  onFileSelect={handleImageSelect}
                  accept="image/*"
                  multiple={true}
                  maxSize={5}
                  maxFiles={10}
                  label="Upload Car Images"
                  description="Add photos of your car from different angles. Good photos attract more renters!"
                />
                {errors.images && (
                  <p className="mt-2 text-sm text-red-600">{errors.images}</p>
                )}
              </div>

              {/* Availability */}
              <div className="bg-white rounded-lg shadow-md p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-6">Availability</h2>
                
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="is_active"
                    checked={data.is_active}
                    onChange={(e) => setData('is_active', e.target.checked)}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                  <label htmlFor="is_active" className="ml-2 text-sm text-gray-700">
                    Make this car available for rent immediately
                  </label>
                </div>
                <p className="mt-1 text-sm text-gray-600">
                  You can change this setting later from your dashboard
                </p>
              </div>

              {/* Submit Button */}
              <div className="flex justify-end space-x-4">
                <Link
                  href="/dashboard"
                  className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                >
                  Cancel
                </Link>
                <button
                  type="submit"
                  disabled={processing}
                  className="flex items-center px-6 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Save size={16} className="mr-2" />
                  {processing ? 'Adding Car...' : 'Add Car'}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </>
  );
}
