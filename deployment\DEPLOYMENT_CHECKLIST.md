# Park & Rent - Deployment Checklist

## Pre-Deployment ✅
- [x] React frontend built for production
- [x] Laravel API assets compiled
- [x] Production environment configured
- [x] Database credentials set for Hostinger
- [x] Domain configured (ebisera.com)
- [x] CORS settings updated
- [x] Root .htaccess created for routing

## Upload to <PERSON><PERSON>
- [ ] Upload all files to public_html
- [ ] Verify file structure is correct
- [ ] Check .htaccess file is in root

## Laravel Configuration
- [ ] Rename .env.production to .env in api folder
- [ ] Run composer install (if SSH available)
- [ ] Generate application key
- [ ] Run database migrations
- [ ] Seed database with admin user
- [ ] Create storage link
- [ ] Cache configuration and routes

## File Permissions
- [ ] Set storage folder to 755
- [ ] Set bootstrap/cache to 755
- [ ] Verify .env file is protected

## Testing
- [ ] Test frontend loads: https://ebisera.com
- [ ] Test API responds: https://ebisera.com/api/cars
- [ ] Test admin login works
- [ ] Test car listings display
- [ ] Test driver listings display
- [ ] Test booking functionality

## Post-Deployment
- [ ] Enable SSL certificate
- [ ] Set up monitoring
- [ ] Configure backups
- [ ] Test all user flows

## Notes
- Database: u555127771_parkandrent_db
- Domain: ebisera.com
- Contact: 0788613669 / <EMAIL>
