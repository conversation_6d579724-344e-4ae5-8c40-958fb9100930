import{r as m,v as j,j as e,$ as g,Y as i}from"./app-Cj-kSxmT.js";import{c as x,C as v}from"./car-CXkXzXL9.js";import{M as N}from"./mail-BdhUp7Lm.js";import{P as w}from"./phone-DVvySU_L.js";import{E as c}from"./eye-off-B6ErUf-o.js";import{E as d}from"./eye-fGPZAapK.js";/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const C=[["path",{d:"m16 11 2 2 4-4",key:"9rsbq5"}],["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]],k=x("user-check",C);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _=[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]],P=x("user",_);function O(){const[l,p]=m.useState(!1),[n,u]=m.useState(!1),{data:a,setData:t,post:h,processing:o,errors:r}=j({name:"",email:"",phone_number:"",password:"",password_confirmation:"",role:"client",terms:!1}),y=s=>{s.preventDefault(),h("/register")},f=[{value:"client",label:"Client",description:"I want to rent cars and hire drivers"},{value:"owner",label:"Car Owner",description:"I want to rent out my cars"},{value:"driver",label:"Driver",description:"I want to offer driving services"}];return e.jsxs(e.Fragment,{children:[e.jsx(g,{title:"Sign Up"}),e.jsxs("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:[e.jsxs("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:[e.jsxs(i,{href:"/",className:"flex justify-center items-center space-x-2",children:[e.jsx(v,{className:"h-12 w-12 text-primary-600"}),e.jsx("span",{className:"text-2xl font-bold text-gray-900",children:"Park & Rent"})]}),e.jsx("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Create your account"}),e.jsxs("p",{className:"mt-2 text-center text-sm text-gray-600",children:["Or"," ",e.jsx(i,{href:"/login",className:"font-medium text-primary-600 hover:text-primary-500",children:"sign in to your existing account"})]})]}),e.jsx("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:e.jsxs("div",{className:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10",children:[e.jsxs("form",{className:"space-y-6",onSubmit:y,children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700",children:"Full Name"}),e.jsxs("div",{className:"mt-1 relative",children:[e.jsx("input",{id:"name",name:"name",type:"text",autoComplete:"name",required:!0,value:a.name,onChange:s=>t("name",s.target.value),className:"appearance-none block w-full px-3 py-2 pl-10 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm",placeholder:"Enter your full name"}),e.jsx(P,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"}),r.name&&e.jsx("p",{className:"mt-2 text-sm text-red-600",children:r.name})]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email address"}),e.jsxs("div",{className:"mt-1 relative",children:[e.jsx("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:a.email,onChange:s=>t("email",s.target.value),className:"appearance-none block w-full px-3 py-2 pl-10 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm",placeholder:"Enter your email"}),e.jsx(N,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"}),r.email&&e.jsx("p",{className:"mt-2 text-sm text-red-600",children:r.email})]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"phone_number",className:"block text-sm font-medium text-gray-700",children:"Phone Number"}),e.jsxs("div",{className:"mt-1 relative",children:[e.jsx("input",{id:"phone_number",name:"phone_number",type:"tel",autoComplete:"tel",required:!0,value:a.phone_number,onChange:s=>t("phone_number",s.target.value),className:"appearance-none block w-full px-3 py-2 pl-10 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm",placeholder:"Enter your phone number"}),e.jsx(w,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"}),r.phone_number&&e.jsx("p",{className:"mt-2 text-sm text-red-600",children:r.phone_number})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"I want to join as:"}),e.jsx("div",{className:"space-y-3",children:f.map(s=>e.jsx("div",{className:"relative",children:e.jsxs("label",{className:"flex items-start p-3 border border-gray-300 rounded-md cursor-pointer hover:bg-gray-50",children:[e.jsx("input",{type:"radio",name:"role",value:s.value,checked:a.role===s.value,onChange:b=>t("role",b.target.value),className:"mt-1 h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"}),e.jsxs("div",{className:"ml-3",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(k,{className:"h-4 w-4 text-primary-600 mr-2"}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:s.label})]}),e.jsx("p",{className:"text-sm text-gray-600 mt-1",children:s.description})]})]})},s.value))}),r.role&&e.jsx("p",{className:"mt-2 text-sm text-red-600",children:r.role})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),e.jsxs("div",{className:"mt-1 relative",children:[e.jsx("input",{id:"password",name:"password",type:l?"text":"password",autoComplete:"new-password",required:!0,value:a.password,onChange:s=>t("password",s.target.value),className:"appearance-none block w-full px-3 py-2 pr-10 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm",placeholder:"Create a password"}),e.jsx("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>p(!l),children:l?e.jsx(c,{className:"h-5 w-5 text-gray-400"}):e.jsx(d,{className:"h-5 w-5 text-gray-400"})}),r.password&&e.jsx("p",{className:"mt-2 text-sm text-red-600",children:r.password})]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"password_confirmation",className:"block text-sm font-medium text-gray-700",children:"Confirm Password"}),e.jsxs("div",{className:"mt-1 relative",children:[e.jsx("input",{id:"password_confirmation",name:"password_confirmation",type:n?"text":"password",autoComplete:"new-password",required:!0,value:a.password_confirmation,onChange:s=>t("password_confirmation",s.target.value),className:"appearance-none block w-full px-3 py-2 pr-10 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm",placeholder:"Confirm your password"}),e.jsx("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>u(!n),children:n?e.jsx(c,{className:"h-5 w-5 text-gray-400"}):e.jsx(d,{className:"h-5 w-5 text-gray-400"})}),r.password_confirmation&&e.jsx("p",{className:"mt-2 text-sm text-red-600",children:r.password_confirmation})]})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("input",{id:"terms",name:"terms",type:"checkbox",checked:a.terms,onChange:s=>t("terms",s.target.checked),className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"}),e.jsxs("label",{htmlFor:"terms",className:"ml-2 block text-sm text-gray-900",children:["I agree to the"," ",e.jsx("a",{href:"#",className:"text-primary-600 hover:text-primary-500",children:"Terms and Conditions"})," ","and"," ",e.jsx("a",{href:"#",className:"text-primary-600 hover:text-primary-500",children:"Privacy Policy"})]})]}),r.terms&&e.jsx("p",{className:"text-sm text-red-600",children:r.terms}),e.jsx("div",{children:e.jsx("button",{type:"submit",disabled:o||!a.terms,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed",children:o?"Creating account...":"Create account"})})]}),e.jsxs("div",{className:"mt-6",children:[e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-0 flex items-center",children:e.jsx("div",{className:"w-full border-t border-gray-300"})}),e.jsx("div",{className:"relative flex justify-center text-sm",children:e.jsx("span",{className:"px-2 bg-white text-gray-500",children:"Contact Information"})})]}),e.jsx("div",{className:"mt-6 text-center",children:e.jsxs("p",{className:"text-sm text-gray-600",children:["Need help? Contact us at:",e.jsx("br",{}),e.jsx("strong",{children:"<EMAIL>"}),e.jsx("br",{}),e.jsx("strong",{children:"**********"})]})})]})]})})]})]})}export{O as default};
