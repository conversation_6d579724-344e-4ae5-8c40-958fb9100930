<?php

namespace App\Http\Controllers;

use App\Models\Chat;
use App\Models\Message;
use App\Models\User;
use Illuminate\Http\Request;
use Inertia\Inertia;

class ChatController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function index()
    {
        $chats = Chat::with(['user', 'recipient', 'messages' => function ($query) {
                $query->latest()->take(1);
            }])
            ->where(function ($query) {
                $query->where('user_id', auth()->id())
                      ->orWhere('recipient_id', auth()->id());
            })
            ->where('is_active', true)
            ->orderBy('last_message_at', 'desc')
            ->get();

        return Inertia::render('Chat/Index', [
            'chats' => $chats,
            'messages' => [],
        ]);
    }

    public function show(Chat $chat)
    {
        // Ensure user is part of this chat
        if ($chat->user_id !== auth()->id() && $chat->recipient_id !== auth()->id()) {
            abort(403);
        }

        $chats = Chat::with(['user', 'recipient', 'messages' => function ($query) {
                $query->latest()->take(1);
            }])
            ->where(function ($query) {
                $query->where('user_id', auth()->id())
                      ->orWhere('recipient_id', auth()->id());
            })
            ->where('is_active', true)
            ->orderBy('last_message_at', 'desc')
            ->get();

        $messages = Message::with('sender')
            ->where('chat_id', $chat->id)
            ->orderBy('created_at', 'asc')
            ->get();

        // Mark messages as read
        Message::where('chat_id', $chat->id)
            ->where('sender_id', '!=', auth()->id())
            ->where('is_read', false)
            ->update([
                'is_read' => true,
                'read_at' => now(),
            ]);

        $chat->load(['user', 'recipient']);

        return Inertia::render('Chat/Index', [
            'chats' => $chats,
            'activeChat' => $chat,
            'messages' => $messages,
        ]);
    }

    public function store(Request $request)
    {
        $request->validate([
            'recipient_id' => 'required|exists:users,id|different:' . auth()->id(),
            'related_to_type' => 'nullable|in:car,driver',
            'related_to_id' => 'nullable|integer',
            'content' => 'required|string|max:1000',
        ]);

        // Check if chat already exists
        $existingChat = Chat::where(function ($query) use ($request) {
                $query->where('user_id', auth()->id())
                      ->where('recipient_id', $request->recipient_id);
            })
            ->orWhere(function ($query) use ($request) {
                $query->where('user_id', $request->recipient_id)
                      ->where('recipient_id', auth()->id());
            })
            ->first();

        if ($existingChat) {
            $chat = $existingChat;
        } else {
            // Create new chat
            $chat = Chat::create([
                'user_id' => auth()->id(),
                'recipient_id' => $request->recipient_id,
                'related_to_type' => $request->related_to_type,
                'related_to_id' => $request->related_to_id,
                'is_active' => true,
                'last_message_at' => now(),
            ]);
        }

        // Create message
        Message::create([
            'chat_id' => $chat->id,
            'sender_id' => auth()->id(),
            'content' => $request->content,
        ]);

        // Update chat last message time
        $chat->update(['last_message_at' => now()]);

        return redirect()->route('chats.show', $chat);
    }

    public function storeMessage(Request $request, Chat $chat)
    {
        // Ensure user is part of this chat
        if ($chat->user_id !== auth()->id() && $chat->recipient_id !== auth()->id()) {
            abort(403);
        }

        $request->validate([
            'content' => 'required|string|max:1000',
        ]);

        Message::create([
            'chat_id' => $chat->id,
            'sender_id' => auth()->id(),
            'content' => $request->content,
        ]);

        // Update chat last message time
        $chat->update(['last_message_at' => now()]);

        return back();
    }

    public function startChat(Request $request)
    {
        $request->validate([
            'recipient_id' => 'required|exists:users,id|different:' . auth()->id(),
            'related_to_type' => 'nullable|in:car,driver',
            'related_to_id' => 'nullable|integer',
        ]);

        // Check if chat already exists
        $existingChat = Chat::where(function ($query) use ($request) {
                $query->where('user_id', auth()->id())
                      ->where('recipient_id', $request->recipient_id);
            })
            ->orWhere(function ($query) use ($request) {
                $query->where('user_id', $request->recipient_id)
                      ->where('recipient_id', auth()->id());
            })
            ->first();

        if ($existingChat) {
            return redirect()->route('chats.show', $existingChat);
        }

        // Create new chat
        $chat = Chat::create([
            'user_id' => auth()->id(),
            'recipient_id' => $request->recipient_id,
            'related_to_type' => $request->related_to_type,
            'related_to_id' => $request->related_to_id,
            'is_active' => true,
            'last_message_at' => now(),
        ]);

        return redirect()->route('chats.show', $chat);
    }
}
