<?php

namespace Database\Seeders;

use App\Models\Driver;
use App\Models\User;
use Illuminate\Database\Seeder;

class DriverSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the driver user
        $driverUser = User::where('email', '<EMAIL>')->first();

        if (!$driverUser) {
            return;
        }

        // Create driver profile
        Driver::create([
            'user_id' => $driverUser->id,
            'name' => 'John Driver',
            'age' => 35,
            'experience' => 10,
            'profile_image' => 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400',
            'license_number' => '**********',
            'license_verification_status' => 'verified',
            'location' => 'Kigali, Rwanda',
            'price_per_hour' => 10000, // RWF
            'rating' => 4.8,
            'reviews' => 24,
            'specialties' => [
                'City Driving',
                'Long Distance',
                'Tour Guide',
                'English Speaking',
            ],
            'availability_notes' => 'Available on weekdays and weekends',
            'is_available' => true,
        ]);

        // Create additional driver
        $additionalDriverUser = User::create([
            'name' => 'Sarah Professional',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'driver',
            'phone_number' => '**********',
            'is_phone_verified' => true,
            'email_verified_at' => now(),
        ]);

        Driver::create([
            'user_id' => $additionalDriverUser->id,
            'name' => 'Sarah Professional',
            'age' => 28,
            'experience' => 6,
            'profile_image' => 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=400',
            'license_number' => '**********',
            'license_verification_status' => 'verified',
            'location' => 'Kigali, Rwanda',
            'price_per_hour' => 12000, // RWF
            'rating' => 4.9,
            'reviews' => 18,
            'specialties' => [
                'Airport Transfers',
                'Business Trips',
                'French Speaking',
                'Night Driving',
            ],
            'availability_notes' => 'Available 24/7 for airport transfers',
            'is_available' => true,
        ]);
    }
}
