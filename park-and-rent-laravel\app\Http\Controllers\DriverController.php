<?php

namespace App\Http\Controllers;

use App\Models\Driver;
use App\Models\Booking;
use Illuminate\Http\Request;
use Inertia\Inertia;

class DriverController extends Controller
{
    public function index(Request $request)
    {
        $query = Driver::with('user')
            ->where('is_available', true)
            ->where('license_verification_status', 'verified');

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('location', 'like', "%{$search}%")
                  ->orWhereJsonContains('specialties', $search);
            });
        }

        if ($request->filled('location')) {
            $query->where('location', 'like', "%{$request->location}%");
        }

        if ($request->filled('specialty')) {
            $query->whereJsonContains('specialties', $request->specialty);
        }

        if ($request->filled('min_experience')) {
            $query->where('experience', '>=', $request->min_experience);
        }

        if ($request->filled('min_price')) {
            $query->where('price_per_hour', '>=', $request->min_price);
        }

        if ($request->filled('max_price')) {
            $query->where('price_per_hour', '<=', $request->max_price);
        }

        $drivers = $query->orderBy('rating', 'desc')->get();

        return Inertia::render('Drivers/Index', [
            'drivers' => $drivers,
            'filters' => $request->only(['search', 'location', 'specialty', 'min_experience', 'min_price', 'max_price']),
        ]);
    }

    public function show(Driver $driver)
    {
        $driver->load('user');

        return Inertia::render('Drivers/Show', [
            'driver' => $driver,
        ]);
    }

    public function book(Request $request, Driver $driver)
    {
        $request->validate([
            'start_time' => 'required|date|after:now',
            'end_time' => 'required|date|after:start_time',
            'notes' => 'nullable|string|max:1000',
        ]);

        // Check if driver is available
        if (!$driver->is_available || $driver->license_verification_status !== 'verified') {
            return back()->withErrors(['error' => 'This driver is not available for booking.']);
        }

        // Check for conflicting bookings
        $conflictingBooking = Booking::where('item_type', 'driver')
            ->where('item_id', $driver->id)
            ->where('status', '!=', 'cancelled')
            ->where(function ($query) use ($request) {
                $query->whereBetween('start_time', [$request->start_time, $request->end_time])
                      ->orWhereBetween('end_time', [$request->start_time, $request->end_time])
                      ->orWhere(function ($q) use ($request) {
                          $q->where('start_time', '<=', $request->start_time)
                            ->where('end_time', '>=', $request->end_time);
                      });
            })
            ->exists();

        if ($conflictingBooking) {
            return back()->withErrors(['error' => 'This driver is already booked for the selected time period.']);
        }

        // Calculate total price
        $startTime = new \DateTime($request->start_time);
        $endTime = new \DateTime($request->end_time);
        $hours = ceil(($endTime->getTimestamp() - $startTime->getTimestamp()) / 3600);
        $totalPrice = $hours * $driver->price_per_hour;

        // Create booking
        $booking = Booking::create([
            'user_id' => auth()->id(),
            'item_type' => 'driver',
            'item_id' => $driver->id,
            'start_time' => $request->start_time,
            'end_time' => $request->end_time,
            'total_price' => $totalPrice,
            'notes' => $request->notes,
            'status' => 'pending',
        ]);

        return redirect()->route('bookings.show', $booking)
            ->with('message', 'Booking request submitted successfully! The driver will review your request.');
    }
}
