import{r as i,v as V,j as e,$ as A,Y as a,m as E}from"./app-Cj-kSxmT.js";import{c as o,C as I}from"./car-CXkXzXL9.js";import{S as R}from"./search-BLfvhpd5.js";import{M as w}from"./message-circle-CPYXbNOk.js";import{A as C}from"./arrow-left-DM9sBmRu.js";import{P}from"./phone-DVvySU_L.js";/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Y=[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]],B=o("ellipsis-vertical",Y);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const F=[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]],H=o("send",F);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const O=[["path",{d:"m16 13 5.223 3.482a.5.5 0 0 0 .777-.416V7.87a.5.5 0 0 0-.752-.432L16 10.5",key:"ftymec"}],["rect",{x:"2",y:"6",width:"14",height:"12",rx:"2",key:"158x01"}]],W=o("video",O);function Z({auth:l,chats:S,activeChat:t,messages:m}){var u,j,b,N;const[x,D]=i.useState(""),[h,g]=i.useState(!1),y=i.useRef(null),{data:c,setData:_,post:k,processing:f,reset:$}=V({content:""}),p=S.filter(s=>{const r=s.user_id===l.user.id?s.recipient:s.user;return r==null?void 0:r.name.toLowerCase().includes(x.toLowerCase())}),M=()=>{var s;(s=y.current)==null||s.scrollIntoView({behavior:"smooth"})};i.useEffect(()=>{M()},[m]);const z=s=>{s.preventDefault(),!(!c.content.trim()||!t)&&k(`/chats/${t.id}/messages`,{onSuccess:()=>{$("content"),E.reload({only:["messages"]})}})},L=s=>new Date(s).toLocaleTimeString("en-US",{hour:"2-digit",minute:"2-digit"}),T=s=>{const r=new Date(s),v=new Date,d=new Date(v);return d.setDate(d.getDate()-1),r.toDateString()===v.toDateString()?"Today":r.toDateString()===d.toDateString()?"Yesterday":r.toLocaleDateString("en-US",{month:"short",day:"numeric"})},n=s=>s.user_id===l.user.id?s.recipient:s.user,U=s=>{if(s.messages&&s.messages.length>0){const r=s.messages[s.messages.length-1];return r.content.length>50?r.content.substring(0,50)+"...":r.content}return"No messages yet"};return e.jsxs(e.Fragment,{children:[e.jsx(A,{title:"Messages"}),e.jsxs("div",{className:"min-h-screen bg-gray-50",children:[e.jsx("header",{className:"bg-white shadow-md sticky top-0 z-50",children:e.jsx("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"flex justify-between items-center py-4",children:[e.jsxs(a,{href:"/",className:"flex items-center space-x-2",children:[e.jsx(I,{className:"h-8 w-8 text-primary-600"}),e.jsx("span",{className:"text-xl font-bold text-gray-900",children:"Park & Rent"})]}),e.jsxs("nav",{className:"hidden md:flex space-x-8",children:[e.jsx(a,{href:"/",className:"text-gray-700 hover:text-primary-600",children:"Home"}),e.jsx(a,{href:"/cars",className:"text-gray-700 hover:text-primary-600",children:"Browse Cars"}),e.jsx(a,{href:"/drivers",className:"text-gray-700 hover:text-primary-600",children:"Hire a Driver"}),e.jsx(a,{href:"/dashboard",className:"text-gray-700 hover:text-primary-600",children:"Dashboard"})]}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("span",{className:"text-gray-700",children:["Welcome, ",l.user.name]}),e.jsx(a,{href:"/dashboard",className:"bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700",children:"Dashboard"})]})]})})}),e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsx("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",style:{height:"600px"},children:e.jsxs("div",{className:"flex h-full",children:[e.jsxs("div",{className:`w-full md:w-1/3 border-r border-gray-200 flex flex-col ${t&&h?"hidden md:flex":""}`,children:[e.jsxs("div",{className:"p-4 border-b border-gray-200",children:[e.jsx("h2",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Messages"}),e.jsxs("div",{className:"relative",children:[e.jsx(R,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),e.jsx("input",{type:"text",placeholder:"Search conversations...",value:x,onChange:s=>D(s.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"})]})]}),e.jsx("div",{className:"flex-1 overflow-y-auto",children:p.length>0?p.map(s=>{const r=n(s);return r?e.jsxs(a,{href:`/chats/${s.id}`,className:`flex items-center p-4 hover:bg-gray-50 border-b border-gray-100 ${(t==null?void 0:t.id)===s.id?"bg-primary-50 border-primary-200":""}`,onClick:()=>g(!0),children:[e.jsx("div",{className:"w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center mr-3",children:e.jsx("span",{className:"text-gray-600 font-semibold",children:r.name.charAt(0).toUpperCase()})}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"flex justify-between items-center mb-1",children:[e.jsx("h3",{className:"font-medium text-gray-900 truncate",children:r.name}),s.last_message_at&&e.jsx("span",{className:"text-xs text-gray-500",children:T(s.last_message_at)})]}),e.jsx("p",{className:"text-sm text-gray-600 truncate",children:U(s)})]})]},s.id):null}):e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(w,{size:48,className:"mx-auto text-gray-400 mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No conversations"}),e.jsx("p",{className:"text-gray-600",children:"Start a conversation by contacting a car owner or driver."})]})})]}),e.jsx("div",{className:`flex-1 flex flex-col ${!t||!h&&window.innerWidth<768?"hidden md:flex":""}`,children:t?e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"p-4 border-b border-gray-200 flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("button",{onClick:()=>g(!1),className:"md:hidden mr-3 p-1 hover:bg-gray-100 rounded",children:e.jsx(C,{size:20})}),e.jsx("div",{className:"w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center mr-3",children:e.jsx("span",{className:"text-gray-600 font-semibold",children:(u=n(t))==null?void 0:u.name.charAt(0).toUpperCase()})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-gray-900",children:(j=n(t))==null?void 0:j.name}),e.jsx("p",{className:"text-sm text-gray-600",children:((b=n(t))==null?void 0:b.role.charAt(0).toUpperCase())+((N=n(t))==null?void 0:N.role.slice(1))})]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("button",{className:"p-2 hover:bg-gray-100 rounded-full",children:e.jsx(P,{size:18,className:"text-gray-600"})}),e.jsx("button",{className:"p-2 hover:bg-gray-100 rounded-full",children:e.jsx(W,{size:18,className:"text-gray-600"})}),e.jsx("button",{className:"p-2 hover:bg-gray-100 rounded-full",children:e.jsx(B,{size:18,className:"text-gray-600"})})]})]}),e.jsxs("div",{className:"flex-1 overflow-y-auto p-4 space-y-4",children:[m.map(s=>{const r=s.sender_id===l.user.id;return e.jsx("div",{className:`flex ${r?"justify-end":"justify-start"}`,children:e.jsxs("div",{className:`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${r?"bg-primary-600 text-white":"bg-gray-200 text-gray-900"}`,children:[e.jsx("p",{className:"text-sm",children:s.content}),e.jsx("p",{className:`text-xs mt-1 ${r?"text-primary-100":"text-gray-500"}`,children:L(s.created_at)})]})},s.id)}),e.jsx("div",{ref:y})]}),e.jsx("div",{className:"p-4 border-t border-gray-200",children:e.jsxs("form",{onSubmit:z,className:"flex space-x-2",children:[e.jsx("input",{type:"text",value:c.content,onChange:s=>_("content",s.target.value),placeholder:"Type a message...",className:"flex-1 px-4 py-2 border border-gray-300 rounded-full focus:ring-primary-500 focus:border-primary-500",disabled:f}),e.jsx("button",{type:"submit",disabled:f||!c.content.trim(),className:"p-2 bg-primary-600 text-white rounded-full hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed",children:e.jsx(H,{size:18})})]})})]}):e.jsx("div",{className:"flex-1 flex items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx(w,{size:64,className:"mx-auto text-gray-400 mb-4"}),e.jsx("h3",{className:"text-xl font-medium text-gray-900 mb-2",children:"Select a conversation"}),e.jsx("p",{className:"text-gray-600",children:"Choose a conversation from the sidebar to start messaging."})]})})})]})})})]})]})}export{Z as default};
