@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for Park & Rent */
@layer base {
  html {
    font-family: 'Figtree', system-ui, sans-serif;
  }

  body {
    background-color: #f9fafb;
    color: #111827;
  }
}

@layer components {
  /* Button styles */
  .btn-primary {
    background-color: #16a34a;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-weight: 500;
    transition: background-color 0.2s;
  }

  .btn-primary:hover {
    background-color: #15803d;
  }

  .btn-secondary {
    background-color: #f3f4f6;
    color: #374151;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-weight: 500;
    transition: background-color 0.2s;
  }

  .btn-secondary:hover {
    background-color: #e5e7eb;
  }

  .btn-outline {
    border: 1px solid #16a34a;
    color: #16a34a;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-weight: 500;
    transition: background-color 0.2s;
  }

  .btn-outline:hover {
    background-color: #f0fdf4;
  }

  /* Card styles */
  .card {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    overflow: hidden;
  }

  .card-hover {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    overflow: hidden;
    transition: box-shadow 0.2s;
  }

  .card-hover:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }

  /* Input styles */
  .input-field {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    transition: border-color 0.2s, box-shadow 0.2s;
  }

  .input-field:focus {
    outline: none;
    border-color: #16a34a;
    box-shadow: 0 0 0 3px rgba(22, 163, 74, 0.1);
  }

  /* Header styles */
  .header-nav {
    background-color: white;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    position: sticky;
    top: 0;
    z-index: 50;
  }

  /* Feature card styles */
  .feature-card {
    background-color: white;
    padding: 1.5rem;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    transition: box-shadow 0.2s;
  }

  .feature-card:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }

  /* Status badges */
  .badge-success {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    background-color: #dcfce7;
    color: #166534;
  }

  .badge-warning {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    background-color: #fef3c7;
    color: #92400e;
  }

  .badge-error {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    background-color: #fee2e2;
    color: #991b1b;
  }

  .badge-info {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    background-color: #dbeafe;
    color: #1e40af;
  }
}

@layer utilities {
  /* Custom utilities */
  .text-primary {
    color: #16a34a;
  }

  .bg-primary {
    background-color: #16a34a;
  }

  .border-primary {
    border-color: #16a34a;
  }

  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }

  .animate-slide-in {
    animation: slideIn 0.3s ease-out;
  }

  /* Gradient backgrounds */
  .bg-gradient-primary {
    background: linear-gradient(135deg, #15803d 0%, #16a34a 100%);
  }

  .bg-gradient-hero {
    background: linear-gradient(135deg, #15803d 0%, #16a34a 50%, #22c55e 100%);
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background-color: #f3f4f6;
}

::-webkit-scrollbar-thumb {
  background-color: #d1d5db;
  border-radius: 9999px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: #9ca3af;
}

/* Loading states */
.loading {
  opacity: 0.5;
  pointer-events: none;
}

/* Focus styles */
.focus-ring:focus {
  outline: none;
  box-shadow: 0 0 0 2px #16a34a, 0 0 0 4px rgba(22, 163, 74, 0.2);
}

/* Image styles */
.img-cover {
  object-fit: cover;
  width: 100%;
  height: 100%;
}

/* Responsive text */
.text-responsive {
  font-size: 0.875rem;
}

@media (min-width: 640px) {
  .text-responsive {
    font-size: 1rem;
  }
}

@media (min-width: 1024px) {
  .text-responsive {
    font-size: 1.125rem;
  }
}

/* Container styles */
.container-custom {
  max-width: 1280px;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .container-custom {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .container-custom {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}
