import React from 'react';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { Car, UserRound, Calendar, Settings, Plus, BarChart3 } from 'lucide-react';
import { PageProps, Booking, Car as CarType, Driver } from '@/types';

interface DashboardProps extends PageProps {
  bookings: Booking[];
  cars?: CarType[];
  driver?: Driver;
  stats: {
    total_bookings: number;
    pending_bookings: number;
    total_earnings: number;
    active_cars?: number;
  };
}

export default function Dashboard({ auth, bookings, cars, driver, stats }: DashboardProps) {
  const user = auth.user;

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 18) return 'Good afternoon';
    return 'Good evening';
  };

  const getRoleSpecificContent = () => {
    switch (user.role) {
      case 'admin':
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div className="bg-white p-6 rounded-lg shadow-md">
              <div className="flex items-center">
                <div className="p-3 bg-blue-100 rounded-full">
                  <UserRound className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <h3 className="text-lg font-semibold text-gray-900">Total Users</h3>
                  <p className="text-2xl font-bold text-blue-600">1,234</p>
                </div>
              </div>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-md">
              <div className="flex items-center">
                <div className="p-3 bg-green-100 rounded-full">
                  <Car className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <h3 className="text-lg font-semibold text-gray-900">Total Cars</h3>
                  <p className="text-2xl font-bold text-green-600">567</p>
                </div>
              </div>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-md">
              <div className="flex items-center">
                <div className="p-3 bg-purple-100 rounded-full">
                  <Calendar className="h-6 w-6 text-purple-600" />
                </div>
                <div className="ml-4">
                  <h3 className="text-lg font-semibold text-gray-900">Total Bookings</h3>
                  <p className="text-2xl font-bold text-purple-600">{stats.total_bookings}</p>
                </div>
              </div>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-md">
              <div className="flex items-center">
                <div className="p-3 bg-yellow-100 rounded-full">
                  <BarChart3 className="h-6 w-6 text-yellow-600" />
                </div>
                <div className="ml-4">
                  <h3 className="text-lg font-semibold text-gray-900">Revenue</h3>
                  <p className="text-2xl font-bold text-yellow-600">RWF {stats.total_earnings.toLocaleString()}</p>
                </div>
              </div>
            </div>
          </div>
        );
      
      case 'owner':
        return (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div className="bg-white p-6 rounded-lg shadow-md">
              <div className="flex items-center">
                <div className="p-3 bg-green-100 rounded-full">
                  <Car className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <h3 className="text-lg font-semibold text-gray-900">My Cars</h3>
                  <p className="text-2xl font-bold text-green-600">{stats.active_cars || 0}</p>
                </div>
              </div>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-md">
              <div className="flex items-center">
                <div className="p-3 bg-blue-100 rounded-full">
                  <Calendar className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <h3 className="text-lg font-semibold text-gray-900">Total Bookings</h3>
                  <p className="text-2xl font-bold text-blue-600">{stats.total_bookings}</p>
                </div>
              </div>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-md">
              <div className="flex items-center">
                <div className="p-3 bg-purple-100 rounded-full">
                  <BarChart3 className="h-6 w-6 text-purple-600" />
                </div>
                <div className="ml-4">
                  <h3 className="text-lg font-semibold text-gray-900">Earnings</h3>
                  <p className="text-2xl font-bold text-purple-600">RWF {stats.total_earnings.toLocaleString()}</p>
                </div>
              </div>
            </div>
          </div>
        );
      
      default:
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div className="bg-white p-6 rounded-lg shadow-md">
              <div className="flex items-center">
                <div className="p-3 bg-blue-100 rounded-full">
                  <Calendar className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <h3 className="text-lg font-semibold text-gray-900">My Bookings</h3>
                  <p className="text-2xl font-bold text-blue-600">{stats.total_bookings}</p>
                </div>
              </div>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-md">
              <div className="flex items-center">
                <div className="p-3 bg-green-100 rounded-full">
                  <Calendar className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <h3 className="text-lg font-semibold text-gray-900">Pending</h3>
                  <p className="text-2xl font-bold text-green-600">{stats.pending_bookings}</p>
                </div>
              </div>
            </div>
          </div>
        );
    }
  };

  return (
    <>
      <Head title="Dashboard" />
      
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <header className="bg-white shadow-md">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-4">
              <Link href="/" className="flex items-center space-x-2">
                <Car className="h-8 w-8 text-primary-600" />
                <span className="text-xl font-bold text-gray-900">Park & Rent</span>
              </Link>

              <nav className="hidden md:flex space-x-8">
                <Link href="/" className="text-gray-700 hover:text-primary-600">
                  Home
                </Link>
                <Link href="/cars" className="text-gray-700 hover:text-primary-600">
                  Browse Cars
                </Link>
                <Link href="/drivers" className="text-gray-700 hover:text-primary-600">
                  Hire a Driver
                </Link>
                <Link href="/dashboard" className="text-primary-600 font-medium">
                  Dashboard
                </Link>
              </nav>

              <div className="flex items-center space-x-4">
                <span className="text-gray-700">Welcome, {user.name}</span>
                <Link
                  href="/profile"
                  className="text-gray-700 hover:text-primary-600"
                >
                  <Settings size={20} />
                </Link>
              </div>
            </div>
          </div>
        </header>

        <div className="container mx-auto px-4 py-8">
          {/* Welcome Section */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              {getGreeting()}, {user.name}!
            </h1>
            <p className="text-gray-600">
              {user.role === 'admin' && 'Manage your platform from here.'}
              {user.role === 'owner' && 'Manage your cars and bookings.'}
              {user.role === 'driver' && 'View your bookings and profile.'}
              {user.role === 'client' && 'Track your bookings and discover new cars.'}
            </p>
          </div>

          {/* Stats Cards */}
          {getRoleSpecificContent()}

          {/* Quick Actions */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Quick Actions */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
              <div className="space-y-3">
                {user.role === 'admin' && (
                  <>
                    <Link
                      href="/admin/users"
                      className="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                    >
                      <UserRound size={20} className="text-primary-600 mr-3" />
                      <span>Manage Users</span>
                    </Link>
                    <Link
                      href="/admin/cars"
                      className="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                    >
                      <Car size={20} className="text-primary-600 mr-3" />
                      <span>Manage Cars</span>
                    </Link>
                  </>
                )}
                
                {user.role === 'owner' && (
                  <>
                    <Link
                      href="/cars/create"
                      className="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                    >
                      <Plus size={20} className="text-primary-600 mr-3" />
                      <span>Add New Car</span>
                    </Link>
                    <Link
                      href="/owner/cars"
                      className="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                    >
                      <Car size={20} className="text-primary-600 mr-3" />
                      <span>Manage My Cars</span>
                    </Link>
                  </>
                )}

                <Link
                  href="/cars"
                  className="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <Car size={20} className="text-primary-600 mr-3" />
                  <span>Browse Cars</span>
                </Link>
                
                <Link
                  href="/drivers"
                  className="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <UserRound size={20} className="text-primary-600 mr-3" />
                  <span>Find Drivers</span>
                </Link>
              </div>
            </div>

            {/* Recent Bookings */}
            <div className="lg:col-span-2 bg-white rounded-lg shadow-md p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Recent Bookings</h3>
                <Link
                  href="/bookings"
                  className="text-primary-600 hover:text-primary-700 text-sm font-medium"
                >
                  View All
                </Link>
              </div>
              
              {bookings.length > 0 ? (
                <div className="space-y-4">
                  {bookings.slice(0, 5).map((booking) => (
                    <div key={booking.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div className="flex items-center">
                        <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center mr-4">
                          {booking.item_type === 'car' ? (
                            <Car size={20} className="text-primary-600" />
                          ) : (
                            <UserRound size={20} className="text-primary-600" />
                          )}
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-900">
                            {booking.item_type === 'car' ? 'Car Rental' : 'Driver Service'}
                          </h4>
                          <p className="text-sm text-gray-600">
                            {new Date(booking.start_time).toLocaleDateString()} - {new Date(booking.end_time).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                          booking.status === 'confirmed' ? 'bg-green-100 text-green-800' :
                          booking.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                          booking.status === 'completed' ? 'bg-blue-100 text-blue-800' :
                          'bg-red-100 text-red-800'
                        }`}>
                          {booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}
                        </span>
                        <p className="text-sm font-medium text-gray-900 mt-1">
                          RWF {booking.total_price.toLocaleString()}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Calendar size={48} className="mx-auto text-gray-400 mb-4" />
                  <h4 className="text-lg font-medium text-gray-900 mb-2">No bookings yet</h4>
                  <p className="text-gray-600 mb-4">
                    Start by browsing available cars or drivers.
                  </p>
                  <Link
                    href="/cars"
                    className="bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700"
                  >
                    Browse Cars
                  </Link>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
