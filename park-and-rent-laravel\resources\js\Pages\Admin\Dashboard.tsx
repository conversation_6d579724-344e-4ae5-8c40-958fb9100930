import React from 'react';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { 
  Car, 
  UserRound, 
  Calendar, 
  Settings, 
  BarChart3,
  Users,
  Shield,
  AlertTriangle,
  TrendingUp,
  Eye
} from 'lucide-react';
import { PageProps, User, Car as CarType, Driver, Booking, GpsInstallationRequest } from '@/types';

interface AdminDashboardProps extends PageProps {
  stats: {
    total_users: number;
    total_cars: number;
    total_drivers: number;
    total_bookings: number;
    pending_verifications: number;
    pending_gps_requests: number;
    total_revenue: number;
    monthly_revenue: number;
  };
  recent_users: User[];
  recent_bookings: Booking[];
  pending_verifications: (Driver | User)[];
  pending_gps_requests: GpsInstallationRequest[];
}

export default function AdminDashboard({ 
  auth, 
  stats, 
  recent_users, 
  recent_bookings, 
  pending_verifications,
  pending_gps_requests 
}: AdminDashboardProps) {

  const formatCurrency = (amount: number) => {
    return `RWF ${amount.toLocaleString()}`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  return (
    <>
      <Head title="Admin Dashboard" />
      
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <header className="bg-white shadow-md">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-4">
              <Link href="/" className="flex items-center space-x-2">
                <Car className="h-8 w-8 text-primary-600" />
                <span className="text-xl font-bold text-gray-900">Park & Rent</span>
              </Link>

              <nav className="hidden md:flex space-x-8">
                <Link href="/admin/dashboard" className="text-primary-600 font-medium">
                  Admin Dashboard
                </Link>
                <Link href="/admin/users" className="text-gray-700 hover:text-primary-600">
                  Users
                </Link>
                <Link href="/admin/cars" className="text-gray-700 hover:text-primary-600">
                  Cars
                </Link>
                <Link href="/admin/drivers" className="text-gray-700 hover:text-primary-600">
                  Drivers
                </Link>
                <Link href="/admin/bookings" className="text-gray-700 hover:text-primary-600">
                  Bookings
                </Link>
              </nav>

              <div className="flex items-center space-x-4">
                <span className="text-gray-700">Admin: {auth.user.name}</span>
                <Link
                  href="/dashboard"
                  className="text-gray-700 hover:text-primary-600"
                >
                  <Settings size={20} />
                </Link>
              </div>
            </div>
          </div>
        </header>

        <div className="container mx-auto px-4 py-8">
          {/* Welcome Section */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Admin Dashboard
            </h1>
            <p className="text-gray-600">
              Manage your Park & Rent platform from here.
            </p>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div className="bg-white p-6 rounded-lg shadow-md">
              <div className="flex items-center">
                <div className="p-3 bg-blue-100 rounded-full">
                  <Users className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <h3 className="text-lg font-semibold text-gray-900">Total Users</h3>
                  <p className="text-2xl font-bold text-blue-600">{stats.total_users}</p>
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-md">
              <div className="flex items-center">
                <div className="p-3 bg-green-100 rounded-full">
                  <Car className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <h3 className="text-lg font-semibold text-gray-900">Total Cars</h3>
                  <p className="text-2xl font-bold text-green-600">{stats.total_cars}</p>
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-md">
              <div className="flex items-center">
                <div className="p-3 bg-purple-100 rounded-full">
                  <UserRound className="h-6 w-6 text-purple-600" />
                </div>
                <div className="ml-4">
                  <h3 className="text-lg font-semibold text-gray-900">Total Drivers</h3>
                  <p className="text-2xl font-bold text-purple-600">{stats.total_drivers}</p>
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-md">
              <div className="flex items-center">
                <div className="p-3 bg-yellow-100 rounded-full">
                  <Calendar className="h-6 w-6 text-yellow-600" />
                </div>
                <div className="ml-4">
                  <h3 className="text-lg font-semibold text-gray-900">Total Bookings</h3>
                  <p className="text-2xl font-bold text-yellow-600">{stats.total_bookings}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Revenue Stats */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div className="bg-white p-6 rounded-lg shadow-md">
              <div className="flex items-center">
                <div className="p-3 bg-indigo-100 rounded-full">
                  <BarChart3 className="h-6 w-6 text-indigo-600" />
                </div>
                <div className="ml-4">
                  <h3 className="text-lg font-semibold text-gray-900">Total Revenue</h3>
                  <p className="text-2xl font-bold text-indigo-600">{formatCurrency(stats.total_revenue)}</p>
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-md">
              <div className="flex items-center">
                <div className="p-3 bg-emerald-100 rounded-full">
                  <TrendingUp className="h-6 w-6 text-emerald-600" />
                </div>
                <div className="ml-4">
                  <h3 className="text-lg font-semibold text-gray-900">Monthly Revenue</h3>
                  <p className="text-2xl font-bold text-emerald-600">{formatCurrency(stats.monthly_revenue)}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Alerts */}
          {(stats.pending_verifications > 0 || stats.pending_gps_requests > 0) && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              {stats.pending_verifications > 0 && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <div className="flex items-center">
                    <AlertTriangle className="h-5 w-5 text-yellow-600 mr-2" />
                    <h3 className="text-lg font-medium text-yellow-800">Pending Verifications</h3>
                  </div>
                  <p className="text-yellow-700 mt-1">
                    {stats.pending_verifications} driver{stats.pending_verifications !== 1 ? 's' : ''} waiting for license verification.
                  </p>
                  <Link
                    href="/admin/drivers?status=pending"
                    className="inline-flex items-center mt-2 text-yellow-800 hover:text-yellow-900"
                  >
                    Review Verifications
                    <Eye size={16} className="ml-1" />
                  </Link>
                </div>
              )}

              {stats.pending_gps_requests > 0 && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-center">
                    <Shield className="h-5 w-5 text-blue-600 mr-2" />
                    <h3 className="text-lg font-medium text-blue-800">GPS Installation Requests</h3>
                  </div>
                  <p className="text-blue-700 mt-1">
                    {stats.pending_gps_requests} GPS installation request{stats.pending_gps_requests !== 1 ? 's' : ''} pending review.
                  </p>
                  <Link
                    href="/admin/gps-requests"
                    className="inline-flex items-center mt-2 text-blue-800 hover:text-blue-900"
                  >
                    Review Requests
                    <Eye size={16} className="ml-1" />
                  </Link>
                </div>
              )}
            </div>
          )}

          {/* Main Content Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Recent Users */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Recent Users</h3>
                <Link
                  href="/admin/users"
                  className="text-primary-600 hover:text-primary-700 text-sm font-medium"
                >
                  View All
                </Link>
              </div>
              
              {recent_users.length > 0 ? (
                <div className="space-y-4">
                  {recent_users.map((user) => (
                    <div key={user.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center">
                        <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center mr-3">
                          <span className="text-primary-600 font-semibold text-sm">
                            {user.name.charAt(0).toUpperCase()}
                          </span>
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-900">{user.name}</h4>
                          <p className="text-sm text-gray-600">{user.email}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                          user.role === 'admin' ? 'bg-red-100 text-red-800' :
                          user.role === 'owner' ? 'bg-blue-100 text-blue-800' :
                          user.role === 'driver' ? 'bg-green-100 text-green-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                        </span>
                        <p className="text-xs text-gray-500 mt-1">
                          {formatDate(user.created_at)}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 text-center py-4">No recent users</p>
              )}
            </div>

            {/* Recent Bookings */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Recent Bookings</h3>
                <Link
                  href="/admin/bookings"
                  className="text-primary-600 hover:text-primary-700 text-sm font-medium"
                >
                  View All
                </Link>
              </div>
              
              {recent_bookings.length > 0 ? (
                <div className="space-y-4">
                  {recent_bookings.map((booking) => (
                    <div key={booking.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center">
                        <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center mr-3">
                          {booking.item_type === 'car' ? (
                            <Car size={20} className="text-primary-600" />
                          ) : (
                            <UserRound size={20} className="text-primary-600" />
                          )}
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-900">
                            {booking.item_type === 'car' ? 'Car Rental' : 'Driver Service'}
                          </h4>
                          <p className="text-sm text-gray-600">
                            Booking #{booking.id}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                          booking.status === 'confirmed' ? 'bg-green-100 text-green-800' :
                          booking.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                          booking.status === 'completed' ? 'bg-blue-100 text-blue-800' :
                          'bg-red-100 text-red-800'
                        }`}>
                          {booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}
                        </span>
                        <p className="text-sm font-medium text-gray-900 mt-1">
                          {formatCurrency(booking.total_price)}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 text-center py-4">No recent bookings</p>
              )}
            </div>
          </div>

          {/* Quick Actions */}
          <div className="mt-8 bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Link
                href="/admin/users"
                className="flex flex-col items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
              >
                <Users size={24} className="text-primary-600 mb-2" />
                <span className="text-sm font-medium text-gray-900">Manage Users</span>
              </Link>
              <Link
                href="/admin/cars"
                className="flex flex-col items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
              >
                <Car size={24} className="text-primary-600 mb-2" />
                <span className="text-sm font-medium text-gray-900">Manage Cars</span>
              </Link>
              <Link
                href="/admin/drivers"
                className="flex flex-col items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
              >
                <UserRound size={24} className="text-primary-600 mb-2" />
                <span className="text-sm font-medium text-gray-900">Manage Drivers</span>
              </Link>
              <Link
                href="/admin/gps-requests"
                className="flex flex-col items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
              >
                <Shield size={24} className="text-primary-600 mb-2" />
                <span className="text-sm font-medium text-gray-900">GPS Requests</span>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
