import React, { useState } from 'react';
import { Head, Link, useForm } from '@inertiajs/react';
import { 
  Car, 
  MapPin, 
  Calendar, 
  Clock, 
  Star, 
  Heart, 
  Share2, 
  MessageCircle,
  ChevronLeft,
  ChevronRight,
  Check,
  X
} from 'lucide-react';
import { PageProps, Car as CarType } from '@/types';

interface CarShowProps extends PageProps {
  car: CarType;
}

export default function CarShow({ auth, car }: CarShowProps) {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [showBookingForm, setShowBookingForm] = useState(false);
  
  const { data, setData, post, processing, errors } = useForm({
    start_time: '',
    end_time: '',
    notes: '',
  });

  const nextImage = () => {
    if (car.images && car.images.length > 1) {
      setCurrentImageIndex((prev) => (prev + 1) % car.images.length);
    }
  };

  const prevImage = () => {
    if (car.images && car.images.length > 1) {
      setCurrentImageIndex((prev) => (prev - 1 + car.images.length) % car.images.length);
    }
  };

  const handleBooking = (e: React.FormEvent) => {
    e.preventDefault();
    post(`/cars/${car.id}/book`);
  };

  const calculatePrice = () => {
    if (!data.start_time || !data.end_time) return 0;
    
    const start = new Date(data.start_time);
    const end = new Date(data.end_time);
    const hours = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60));
    
    return hours > 0 ? hours * car.price_per_hour : 0;
  };

  return (
    <>
      <Head title={`${car.make} ${car.model} - ${car.year}`} />
      
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <header className="bg-white shadow-md sticky top-0 z-50">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-4">
              <Link href="/" className="flex items-center space-x-2">
                <Car className="h-8 w-8 text-primary-600" />
                <span className="text-xl font-bold text-gray-900">Park & Rent</span>
              </Link>

              <nav className="hidden md:flex space-x-8">
                <Link href="/" className="text-gray-700 hover:text-primary-600">
                  Home
                </Link>
                <Link href="/cars" className="text-primary-600 font-medium">
                  Browse Cars
                </Link>
                <Link href="/drivers" className="text-gray-700 hover:text-primary-600">
                  Hire a Driver
                </Link>
              </nav>

              <div className="flex items-center space-x-4">
                {auth.user ? (
                  <div className="flex items-center space-x-4">
                    <span className="text-gray-700">Welcome, {auth.user.name}</span>
                    <Link
                      href="/dashboard"
                      className="bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700"
                    >
                      Dashboard
                    </Link>
                  </div>
                ) : (
                  <div className="flex items-center space-x-4">
                    <Link
                      href="/login"
                      className="text-gray-700 hover:text-primary-600"
                    >
                      Log In
                    </Link>
                    <Link
                      href="/register"
                      className="bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700"
                    >
                      Sign Up
                    </Link>
                  </div>
                )}
              </div>
            </div>
          </div>
        </header>

        {/* Breadcrumb */}
        <div className="bg-white border-b">
          <div className="container mx-auto px-4 py-3">
            <nav className="flex items-center space-x-2 text-sm">
              <Link href="/" className="text-gray-600 hover:text-primary-600">Home</Link>
              <span className="text-gray-400">/</span>
              <Link href="/cars" className="text-gray-600 hover:text-primary-600">Cars</Link>
              <span className="text-gray-400">/</span>
              <span className="text-gray-900">{car.make} {car.model}</span>
            </nav>
          </div>
        </div>

        <div className="container mx-auto px-4 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-2">
              {/* Image Gallery */}
              <div className="bg-white rounded-lg shadow-md overflow-hidden mb-6">
                <div className="relative h-96">
                  {car.images && car.images.length > 0 ? (
                    <>
                      <img
                        src={car.images[currentImageIndex]}
                        alt={`${car.make} ${car.model}`}
                        className="w-full h-full object-cover"
                      />
                      
                      {car.images.length > 1 && (
                        <>
                          <button
                            onClick={prevImage}
                            className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70"
                          >
                            <ChevronLeft size={20} />
                          </button>
                          <button
                            onClick={nextImage}
                            className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70"
                          >
                            <ChevronRight size={20} />
                          </button>
                        </>
                      )}

                      {/* Image Indicators */}
                      {car.images.length > 1 && (
                        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                          {car.images.map((_, index) => (
                            <button
                              key={index}
                              onClick={() => setCurrentImageIndex(index)}
                              className={`w-3 h-3 rounded-full ${
                                index === currentImageIndex ? 'bg-white' : 'bg-white bg-opacity-50'
                              }`}
                            />
                          ))}
                        </div>
                      )}
                    </>
                  ) : (
                    <div className="w-full h-full flex items-center justify-center bg-gray-200">
                      <Car size={64} className="text-gray-400" />
                    </div>
                  )}

                  {/* Action Buttons */}
                  <div className="absolute top-4 right-4 flex space-x-2">
                    <button className="bg-white p-2 rounded-full shadow-md hover:bg-gray-50">
                      <Heart size={20} className="text-gray-600" />
                    </button>
                    <button className="bg-white p-2 rounded-full shadow-md hover:bg-gray-50">
                      <Share2 size={20} className="text-gray-600" />
                    </button>
                  </div>

                  {/* Status Badge */}
                  <div className="absolute top-4 left-4">
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                      car.is_active 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {car.is_active ? 'Available' : 'Unavailable'}
                    </span>
                  </div>
                </div>

                {/* Thumbnail Gallery */}
                {car.images && car.images.length > 1 && (
                  <div className="p-4 border-t">
                    <div className="flex space-x-2 overflow-x-auto">
                      {car.images.map((image, index) => (
                        <button
                          key={index}
                          onClick={() => setCurrentImageIndex(index)}
                          className={`flex-shrink-0 w-20 h-20 rounded-md overflow-hidden border-2 ${
                            index === currentImageIndex ? 'border-primary-600' : 'border-gray-200'
                          }`}
                        >
                          <img
                            src={image}
                            alt={`${car.make} ${car.model} ${index + 1}`}
                            className="w-full h-full object-cover"
                          />
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* Car Details */}
              <div className="bg-white rounded-lg shadow-md p-6 mb-6">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h1 className="text-3xl font-bold text-gray-900 mb-2">
                      {car.make} {car.model}
                    </h1>
                    <div className="flex items-center text-gray-600 mb-2">
                      <Calendar size={16} className="mr-2" />
                      <span>{car.year}</span>
                    </div>
                    <div className="flex items-center text-gray-600">
                      <MapPin size={16} className="mr-2" />
                      <span>{car.location}</span>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-3xl font-bold text-primary-600">
                      RWF {car.price_per_hour.toLocaleString()}
                    </div>
                    <div className="text-gray-600">per hour</div>
                  </div>
                </div>

                <div className="border-t pt-4">
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">Description</h3>
                  <p className="text-gray-700 leading-relaxed">{car.description}</p>
                </div>

                {car.availability_notes && (
                  <div className="border-t pt-4 mt-4">
                    <h3 className="text-lg font-semibold text-gray-900 mb-3">Availability Notes</h3>
                    <p className="text-gray-700">{car.availability_notes}</p>
                  </div>
                )}
              </div>

              {/* Features */}
              {car.features && car.features.length > 0 && (
                <div className="bg-white rounded-lg shadow-md p-6 mb-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Features & Amenities</h3>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                    {car.features.map((feature, index) => (
                      <div key={index} className="flex items-center">
                        <Check size={16} className="text-green-600 mr-2" />
                        <span className="text-gray-700">{feature}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Owner Info */}
              {car.owner && (
                <div className="bg-white rounded-lg shadow-md p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Car Owner</h3>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mr-4">
                        <span className="text-primary-600 font-semibold">
                          {car.owner.name.charAt(0).toUpperCase()}
                        </span>
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-900">{car.owner.name}</h4>
                        <p className="text-sm text-gray-600">Car Owner</p>
                      </div>
                    </div>
                    {auth.user && auth.user.id !== car.owner.id && (
                      <button className="flex items-center px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50">
                        <MessageCircle size={16} className="mr-2" />
                        Message
                      </button>
                    )}
                  </div>
                </div>
              )}
            </div>

            {/* Booking Sidebar */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow-md p-6 sticky top-24">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Book This Car</h3>
                
                {auth.user ? (
                  car.is_active ? (
                    <form onSubmit={handleBooking} className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Start Date & Time
                        </label>
                        <input
                          type="datetime-local"
                          value={data.start_time}
                          onChange={(e) => setData('start_time', e.target.value)}
                          min={new Date().toISOString().slice(0, 16)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
                          required
                        />
                        {errors.start_time && (
                          <p className="mt-1 text-sm text-red-600">{errors.start_time}</p>
                        )}
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          End Date & Time
                        </label>
                        <input
                          type="datetime-local"
                          value={data.end_time}
                          onChange={(e) => setData('end_time', e.target.value)}
                          min={data.start_time || new Date().toISOString().slice(0, 16)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
                          required
                        />
                        {errors.end_time && (
                          <p className="mt-1 text-sm text-red-600">{errors.end_time}</p>
                        )}
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Notes (Optional)
                        </label>
                        <textarea
                          value={data.notes}
                          onChange={(e) => setData('notes', e.target.value)}
                          rows={3}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
                          placeholder="Any special requests or notes..."
                        />
                      </div>

                      {/* Price Calculation */}
                      {data.start_time && data.end_time && (
                        <div className="bg-gray-50 p-4 rounded-md">
                          <div className="flex justify-between items-center mb-2">
                            <span className="text-gray-700">Duration:</span>
                            <span className="font-medium">
                              {Math.ceil((new Date(data.end_time).getTime() - new Date(data.start_time).getTime()) / (1000 * 60 * 60))} hours
                            </span>
                          </div>
                          <div className="flex justify-between items-center mb-2">
                            <span className="text-gray-700">Rate:</span>
                            <span className="font-medium">RWF {car.price_per_hour.toLocaleString()}/hr</span>
                          </div>
                          <div className="border-t pt-2 flex justify-between items-center">
                            <span className="font-semibold text-gray-900">Total:</span>
                            <span className="font-bold text-primary-600 text-lg">
                              RWF {calculatePrice().toLocaleString()}
                            </span>
                          </div>
                        </div>
                      )}

                      <button
                        type="submit"
                        disabled={processing || !data.start_time || !data.end_time}
                        className="w-full bg-primary-600 text-white py-3 rounded-md hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed font-medium"
                      >
                        {processing ? 'Booking...' : 'Book Now'}
                      </button>
                    </form>
                  ) : (
                    <div className="text-center py-8">
                      <X size={48} className="mx-auto text-red-400 mb-4" />
                      <h4 className="text-lg font-medium text-gray-900 mb-2">Car Unavailable</h4>
                      <p className="text-gray-600">This car is currently not available for booking.</p>
                    </div>
                  )
                ) : (
                  <div className="text-center py-8">
                    <h4 className="text-lg font-medium text-gray-900 mb-2">Sign in to Book</h4>
                    <p className="text-gray-600 mb-4">You need to be logged in to book this car.</p>
                    <Link
                      href="/login"
                      className="w-full bg-primary-600 text-white py-3 rounded-md hover:bg-primary-700 font-medium inline-block text-center"
                    >
                      Sign In
                    </Link>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
