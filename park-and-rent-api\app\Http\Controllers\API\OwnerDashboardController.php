<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use App\Models\Car;
use App\Models\Chat;
use App\Models\Message;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use App\Notifications\NewChatMessage;

class OwnerDashboardController extends Controller
{
    /**
     * Get owner dashboard overview
     */
    public function overview()
    {
        try {
            $user = auth()->user();

            if (!$user) {
                return response()->json([
                    'message' => 'Authentication required',
                    'error' => 'User not authenticated.',
                    'error_type' => 'unauthenticated'
                ], 401);
            }

            if ($user->role !== 'owner') {
                return response()->json([
                    'message' => 'Access denied',
                    'error' => 'Only car owners can access this dashboard.',
                    'error_type' => 'unauthorized_access'
                ], 403);
            }

            // Get owner's cars
            $cars = Car::where('owner_id', $user->id)->get();
            $carIds = $cars->pluck('id');

            // Get bookings for owner's cars
            $bookings = Booking::where('item_type', 'car')
                ->whereIn('item_id', $carIds)
                ->with(['user'])
                ->orderBy('created_at', 'desc')
                ->get();

            // Get recent messages (with error handling)
            $chats = collect();
            try {
                $chats = Chat::where(function($query) use ($user) {
                    $query->where('user_id', $user->id)
                          ->orWhere('recipient_id', $user->id);
                })
                ->with(['user', 'recipient', 'messages' => function($query) {
                    $query->latest()->limit(1);
                }])
                ->orderBy('last_message_at', 'desc')
                ->limit(10)
                ->get();
            } catch (\Exception $e) {
                Log::error('Error fetching chats: ' . $e->getMessage());
                // Continue without chats
            }

            // Get unread messages count (with error handling)
            $unreadMessages = 0;
            try {
                $unreadMessages = $this->getUnreadMessagesCount($user->id);
            } catch (\Exception $e) {
                Log::error('Error fetching unread messages: ' . $e->getMessage());
                // Continue with 0 unread messages
            }

            // Calculate statistics
            $stats = [
                'total_cars' => $cars->count(),
                'active_cars' => $cars->where('is_active', true)->count(),
                'total_bookings' => $bookings->count(),
                'pending_bookings' => $bookings->where('status', 'pending')->count(),
                'confirmed_bookings' => $bookings->where('status', 'confirmed')->count(),
                'total_revenue' => $bookings->where('status', 'confirmed')->sum('total_price'),
                'unread_messages' => $unreadMessages,
            ];

            return response()->json([
                'message' => 'Dashboard data retrieved successfully',
                'data' => [
                    'stats' => $stats,
                    'recent_bookings' => $bookings->take(5),
                    'recent_chats' => $chats,
                    'cars' => $cars
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Dashboard overview error: ' . $e->getMessage());
            Log::error('Stack trace: ' . $e->getTraceAsString());

            return response()->json([
                'message' => 'Internal server error',
                'error' => 'An error occurred while fetching dashboard data.',
                'error_type' => 'server_error',
                'debug' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * Get owner's messages/chats
     */
    public function getMessages(Request $request)
    {
        $user = auth()->user();

        if ($user->role !== 'owner') {
            return response()->json([
                'message' => 'Access denied',
                'error' => 'Only car owners can access messages.',
                'error_type' => 'unauthorized_access'
            ], 403);
        }

        $chats = Chat::where(function($query) use ($user) {
            $query->where('user_id', $user->id)
                  ->orWhere('recipient_id', $user->id);
        })
        ->with(['user', 'recipient', 'messages' => function($query) {
            $query->with('sender')->orderBy('created_at', 'desc');
        }])
        ->orderBy('last_message_at', 'desc')
        ->paginate(10);

        return response()->json([
            'message' => 'Messages retrieved successfully',
            'data' => $chats
        ]);
    }

    /**
     * Get specific chat conversation
     */
    public function getChat($chatId)
    {
        $user = auth()->user();

        if ($user->role !== 'owner') {
            return response()->json([
                'message' => 'Access denied',
                'error' => 'Only car owners can access chat conversations.',
                'error_type' => 'unauthorized_access'
            ], 403);
        }

        $chat = Chat::where('id', $chatId)
            ->where(function($query) use ($user) {
                $query->where('user_id', $user->id)
                      ->orWhere('recipient_id', $user->id);
            })
            ->with(['user', 'recipient', 'messages' => function($query) {
                $query->with('sender')->orderBy('created_at', 'asc');
            }])
            ->first();

        if (!$chat) {
            return response()->json([
                'message' => 'Chat not found',
                'error' => 'The requested chat conversation was not found or you do not have access to it.',
                'error_type' => 'chat_not_found'
            ], 404);
        }

        // Mark messages as read
        Message::where('chat_id', $chatId)
            ->where('sender_id', '!=', $user->id)
            ->where('is_read', false)
            ->update(['is_read' => true]);

        return response()->json([
            'message' => 'Chat retrieved successfully',
            'data' => $chat
        ]);
    }

    /**
     * Send a reply message
     */
    public function sendMessage(Request $request)
    {
        $user = auth()->user();

        if ($user->role !== 'owner') {
            return response()->json([
                'message' => 'Access denied',
                'error' => 'Only car owners can send messages.',
                'error_type' => 'unauthorized_access'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'chat_id' => 'required|exists:chats,id',
            'content' => 'required|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'error' => 'Please check your input and try again.',
                'error_type' => 'validation_error',
                'errors' => $validator->errors()
            ], 422);
        }

        // Verify user has access to this chat
        $chat = Chat::where('id', $request->chat_id)
            ->where(function($query) use ($user) {
                $query->where('user_id', $user->id)
                      ->orWhere('recipient_id', $user->id);
            })
            ->first();

        if (!$chat) {
            return response()->json([
                'message' => 'Chat not found',
                'error' => 'The requested chat conversation was not found or you do not have access to it.',
                'error_type' => 'chat_not_found'
            ], 404);
        }

        // Create the message
        $message = Message::create([
            'chat_id' => $request->chat_id,
            'sender_id' => $user->id,
            'content' => $request->content,
            'is_read' => false,
        ]);

        // Update chat last message time
        $chat->update([
            'last_message_at' => now(),
            'is_active' => true,
        ]);

        // Send email notification to recipient
        $recipient = $chat->user_id === $user->id ? $chat->recipient : $chat->user;
        $recipient->notify(new NewChatMessage($message, $chat));

        return response()->json([
            'message' => 'Message sent successfully',
            'data' => $message->load('sender')
        ], 201);
    }

    /**
     * Get owner's bookings
     */
    public function getBookings(Request $request)
    {
        $user = auth()->user();

        if ($user->role !== 'owner') {
            return response()->json([
                'message' => 'Access denied',
                'error' => 'Only car owners can access bookings.',
                'error_type' => 'unauthorized_access'
            ], 403);
        }

        // Get owner's cars
        $carIds = Car::where('owner_id', $user->id)->pluck('id');

        $query = Booking::where('item_type', 'car')
            ->whereIn('item_id', $carIds)
            ->with(['user', 'car']);

        // Apply filters
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        if ($request->has('from_date')) {
            $query->where('start_time', '>=', $request->from_date);
        }

        if ($request->has('to_date')) {
            $query->where('end_time', '<=', $request->to_date);
        }

        $bookings = $query->orderBy('created_at', 'desc')->paginate(15);

        return response()->json([
            'message' => 'Bookings retrieved successfully',
            'data' => $bookings
        ]);
    }

    /**
     * Update booking status
     */
    public function updateBookingStatus(Request $request, $bookingId)
    {
        $user = auth()->user();

        if ($user->role !== 'owner') {
            return response()->json([
                'message' => 'Access denied',
                'error' => 'Only car owners can update booking status.',
                'error_type' => 'unauthorized_access'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'status' => 'required|in:confirmed,cancelled,completed',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'error' => 'Invalid status provided. Status must be confirmed, cancelled, or completed.',
                'error_type' => 'validation_error',
                'errors' => $validator->errors()
            ], 422);
        }

        $booking = Booking::findOrFail($bookingId);

        // Verify owner has access to this booking
        $car = Car::where('id', $booking->item_id)
            ->where('owner_id', $user->id)
            ->first();

        if (!$car || $booking->item_type !== 'car') {
            return response()->json([
                'message' => 'Booking not found',
                'error' => 'The requested booking was not found or you do not have access to it.',
                'error_type' => 'booking_not_found'
            ], 404);
        }

        $booking->update(['status' => $request->status]);

        // Send notification to customer
        if ($request->status === 'confirmed') {
            $booking->user->notify(new \App\Notifications\BookingConfirmed($booking));
        } elseif ($request->status === 'cancelled') {
            $booking->user->notify(new \App\Notifications\BookingCancelled($booking));
        }

        return response()->json([
            'message' => 'Booking status updated successfully. Customer has been notified.',
            'data' => $booking->load('user', 'car')
        ]);
    }

    /**
     * Get unread messages count for user
     */
    private function getUnreadMessagesCount($userId)
    {
        return Message::whereHas('chat', function($query) use ($userId) {
            $query->where('user_id', $userId)
                  ->orWhere('recipient_id', $userId);
        })
        ->where('sender_id', '!=', $userId)
        ->where('is_read', false)
        ->count();
    }
}
