import{r as d,v as x,j as e,$ as p,Y as i}from"./app-Cj-kSxmT.js";import{C as h}from"./car-CXkXzXL9.js";import{E as u}from"./eye-off-B6ErUf-o.js";import{E as y}from"./eye-fGPZAapK.js";function N(){const[a,n]=d.useState(!1),{data:t,setData:m,post:o,processing:l,errors:r}=x({email:"",password:"",remember:!1}),c=s=>{s.preventDefault(),o("/login")};return e.jsxs(e.Fragment,{children:[e.jsx(p,{title:"Log In"}),e.jsxs("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:[e.jsxs("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:[e.jsxs(i,{href:"/",className:"flex justify-center items-center space-x-2",children:[e.jsx(h,{className:"h-12 w-12 text-primary-600"}),e.jsx("span",{className:"text-2xl font-bold text-gray-900",children:"Park & Rent"})]}),e.jsx("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Sign in to your account"}),e.jsxs("p",{className:"mt-2 text-center text-sm text-gray-600",children:["Or"," ",e.jsx(i,{href:"/register",className:"font-medium text-primary-600 hover:text-primary-500",children:"create a new account"})]})]}),e.jsx("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:e.jsxs("div",{className:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10",children:[e.jsxs("form",{className:"space-y-6",onSubmit:c,children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email address"}),e.jsxs("div",{className:"mt-1",children:[e.jsx("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:t.email,onChange:s=>m("email",s.target.value),className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm",placeholder:"Enter your email"}),r.email&&e.jsx("p",{className:"mt-2 text-sm text-red-600",children:r.email})]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),e.jsxs("div",{className:"mt-1 relative",children:[e.jsx("input",{id:"password",name:"password",type:a?"text":"password",autoComplete:"current-password",required:!0,value:t.password,onChange:s=>m("password",s.target.value),className:"appearance-none block w-full px-3 py-2 pr-10 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm",placeholder:"Enter your password"}),e.jsx("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>n(!a),children:a?e.jsx(u,{className:"h-5 w-5 text-gray-400"}):e.jsx(y,{className:"h-5 w-5 text-gray-400"})}),r.password&&e.jsx("p",{className:"mt-2 text-sm text-red-600",children:r.password})]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("input",{id:"remember",name:"remember",type:"checkbox",checked:t.remember,onChange:s=>m("remember",s.target.checked),className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"}),e.jsx("label",{htmlFor:"remember",className:"ml-2 block text-sm text-gray-900",children:"Remember me"})]}),e.jsx("div",{className:"text-sm",children:e.jsx("a",{href:"#",className:"font-medium text-primary-600 hover:text-primary-500",children:"Forgot your password?"})})]}),e.jsx("div",{children:e.jsx("button",{type:"submit",disabled:l,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed",children:l?"Signing in...":"Sign in"})})]}),e.jsxs("div",{className:"mt-6",children:[e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-0 flex items-center",children:e.jsx("div",{className:"w-full border-t border-gray-300"})}),e.jsx("div",{className:"relative flex justify-center text-sm",children:e.jsx("span",{className:"px-2 bg-white text-gray-500",children:"Demo Accounts"})})]}),e.jsxs("div",{className:"mt-6 grid grid-cols-2 gap-3",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("p",{className:"text-xs text-gray-500 mb-1",children:"Admin"}),e.jsx("p",{className:"text-xs font-mono text-gray-700",children:"<EMAIL>"}),e.jsx("p",{className:"text-xs font-mono text-gray-700",children:"password"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("p",{className:"text-xs text-gray-500 mb-1",children:"Owner"}),e.jsx("p",{className:"text-xs font-mono text-gray-700",children:"<EMAIL>"}),e.jsx("p",{className:"text-xs font-mono text-gray-700",children:"password"})]})]})]})]})})]})]})}export{N as default};
