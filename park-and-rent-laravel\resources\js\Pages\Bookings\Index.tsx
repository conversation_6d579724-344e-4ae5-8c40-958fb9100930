import React, { useState } from 'react';
import { Head, Link, router } from '@inertiajs/react';
import { 
  Car, 
  UserRound, 
  Calendar, 
  Clock, 
  MapPin, 
  Filter,
  Eye,
  MessageCircle,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react';
import { PageProps, Booking } from '@/types';

interface BookingsIndexProps extends PageProps {
  bookings: Booking[];
  filters: {
    status?: string;
    type?: string;
  };
}

export default function BookingsIndex({ auth, bookings, filters }: BookingsIndexProps) {
  const [showFilters, setShowFilters] = useState(false);
  const [localFilters, setLocalFilters] = useState({
    status: filters.status || '',
    type: filters.type || '',
  });

  const handleFilterChange = () => {
    router.get('/bookings', localFilters, { 
      preserveState: true,
      preserveScroll: true 
    });
  };

  const clearFilters = () => {
    setLocalFilters({
      status: '',
      type: '',
    });
    router.get('/bookings');
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'confirmed':
        return <CheckCircle size={16} className="text-green-600" />;
      case 'completed':
        return <CheckCircle size={16} className="text-blue-600" />;
      case 'cancelled':
        return <XCircle size={16} className="text-red-600" />;
      case 'pending':
      default:
        return <AlertCircle size={16} className="text-yellow-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'bg-green-100 text-green-800';
      case 'completed':
        return 'bg-blue-100 text-blue-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      case 'pending':
      default:
        return 'bg-yellow-100 text-yellow-800';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const calculateDuration = (startTime: string, endTime: string) => {
    const start = new Date(startTime);
    const end = new Date(endTime);
    const hours = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60));
    return hours;
  };

  return (
    <>
      <Head title="My Bookings" />
      
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <header className="bg-white shadow-md sticky top-0 z-50">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-4">
              <Link href="/" className="flex items-center space-x-2">
                <Car className="h-8 w-8 text-primary-600" />
                <span className="text-xl font-bold text-gray-900">Park & Rent</span>
              </Link>

              <nav className="hidden md:flex space-x-8">
                <Link href="/" className="text-gray-700 hover:text-primary-600">
                  Home
                </Link>
                <Link href="/cars" className="text-gray-700 hover:text-primary-600">
                  Browse Cars
                </Link>
                <Link href="/drivers" className="text-gray-700 hover:text-primary-600">
                  Hire a Driver
                </Link>
                <Link href="/dashboard" className="text-gray-700 hover:text-primary-600">
                  Dashboard
                </Link>
              </nav>

              <div className="flex items-center space-x-4">
                <span className="text-gray-700">Welcome, {auth.user.name}</span>
                <Link
                  href="/dashboard"
                  className="bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700"
                >
                  Dashboard
                </Link>
              </div>
            </div>
          </div>
        </header>

        <div className="container mx-auto px-4 py-8">
          {/* Page Header */}
          <div className="flex justify-between items-center mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">My Bookings</h1>
              <p className="text-gray-600 mt-2">Manage your car rentals and driver bookings</p>
            </div>
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
            >
              <Filter size={16} className="mr-2" />
              Filters
            </button>
          </div>

          {/* Filters */}
          {showFilters && (
            <div className="bg-white rounded-lg shadow-md p-6 mb-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Status
                  </label>
                  <select
                    value={localFilters.status}
                    onChange={(e) => setLocalFilters({...localFilters, status: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
                  >
                    <option value="">All Statuses</option>
                    <option value="pending">Pending</option>
                    <option value="confirmed">Confirmed</option>
                    <option value="completed">Completed</option>
                    <option value="cancelled">Cancelled</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Type
                  </label>
                  <select
                    value={localFilters.type}
                    onChange={(e) => setLocalFilters({...localFilters, type: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
                  >
                    <option value="">All Types</option>
                    <option value="car">Car Rentals</option>
                    <option value="driver">Driver Services</option>
                  </select>
                </div>
                <div className="flex items-end space-x-2">
                  <button
                    onClick={handleFilterChange}
                    className="bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700"
                  >
                    Apply Filters
                  </button>
                  <button
                    onClick={clearFilters}
                    className="text-gray-600 hover:text-gray-800 px-4 py-2"
                  >
                    Clear
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Bookings List */}
          {bookings.length > 0 ? (
            <div className="space-y-6">
              {bookings.map((booking) => (
                <div key={booking.id} className="bg-white rounded-lg shadow-md overflow-hidden">
                  <div className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center">
                        <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mr-4">
                          {booking.item_type === 'car' ? (
                            <Car size={24} className="text-primary-600" />
                          ) : (
                            <UserRound size={24} className="text-primary-600" />
                          )}
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900">
                            {booking.item_type === 'car' ? 'Car Rental' : 'Driver Service'}
                          </h3>
                          <p className="text-sm text-gray-600">
                            Booking #{booking.id}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(booking.status)}`}>
                          {getStatusIcon(booking.status)}
                          <span className="ml-1">{booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}</span>
                        </span>
                        <Link
                          href={`/bookings/${booking.id}`}
                          className="text-primary-600 hover:text-primary-700"
                        >
                          <Eye size={20} />
                        </Link>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                      <div>
                        <div className="flex items-center text-gray-600 mb-1">
                          <Calendar size={16} className="mr-2" />
                          <span className="text-sm font-medium">Start Time</span>
                        </div>
                        <p className="text-gray-900">{formatDate(booking.start_time)}</p>
                      </div>
                      <div>
                        <div className="flex items-center text-gray-600 mb-1">
                          <Calendar size={16} className="mr-2" />
                          <span className="text-sm font-medium">End Time</span>
                        </div>
                        <p className="text-gray-900">{formatDate(booking.end_time)}</p>
                      </div>
                      <div>
                        <div className="flex items-center text-gray-600 mb-1">
                          <Clock size={16} className="mr-2" />
                          <span className="text-sm font-medium">Duration</span>
                        </div>
                        <p className="text-gray-900">{calculateDuration(booking.start_time, booking.end_time)} hours</p>
                      </div>
                    </div>

                    {booking.notes && (
                      <div className="mb-4">
                        <h4 className="text-sm font-medium text-gray-700 mb-1">Notes</h4>
                        <p className="text-gray-600 text-sm">{booking.notes}</p>
                      </div>
                    )}

                    <div className="flex items-center justify-between pt-4 border-t">
                      <div className="flex items-center space-x-4">
                        <div>
                          <span className="text-2xl font-bold text-primary-600">
                            RWF {booking.total_price.toLocaleString()}
                          </span>
                        </div>
                        {booking.is_paid && (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            Paid
                          </span>
                        )}
                      </div>
                      <div className="flex items-center space-x-2">
                        <button className="flex items-center px-3 py-1 text-sm text-gray-600 hover:text-gray-800">
                          <MessageCircle size={16} className="mr-1" />
                          Message
                        </button>
                        <Link
                          href={`/bookings/${booking.id}`}
                          className="bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700 text-sm"
                        >
                          View Details
                        </Link>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="bg-white rounded-lg shadow-md p-12 text-center">
              <Calendar size={64} className="mx-auto text-gray-400 mb-4" />
              <h3 className="text-xl font-medium text-gray-900 mb-2">No bookings found</h3>
              <p className="text-gray-600 mb-6">
                {Object.values(localFilters).some(filter => filter) 
                  ? 'No bookings match your current filters. Try adjusting your search criteria.'
                  : 'You haven\'t made any bookings yet. Start by browsing available cars or drivers.'
                }
              </p>
              <div className="flex justify-center space-x-4">
                <Link
                  href="/cars"
                  className="bg-primary-600 text-white px-6 py-2 rounded-md hover:bg-primary-700"
                >
                  Browse Cars
                </Link>
                <Link
                  href="/drivers"
                  className="border border-primary-600 text-primary-600 px-6 py-2 rounded-md hover:bg-primary-50"
                >
                  Find Drivers
                </Link>
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
}
