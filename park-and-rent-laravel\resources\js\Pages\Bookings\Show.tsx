import React from 'react';
import { Head, Link, useForm } from '@inertiajs/react';
import { 
  Car, 
  UserRound, 
  Calendar, 
  Clock, 
  MapPin, 
  MessageCircle,
  CheckCircle,
  XCircle,
  AlertCircle,
  Phone,
  Mail,
  Edit
} from 'lucide-react';
import { PageProps, Booking } from '@/types';

interface BookingShowProps extends PageProps {
  booking: Booking;
}

export default function BookingShow({ auth, booking }: BookingShowProps) {
  const { post, processing } = useForm();

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'confirmed':
        return <CheckCircle size={20} className="text-green-600" />;
      case 'completed':
        return <CheckCircle size={20} className="text-blue-600" />;
      case 'cancelled':
        return <XCircle size={20} className="text-red-600" />;
      case 'pending':
      default:
        return <AlertCircle size={20} className="text-yellow-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'bg-green-100 text-green-800';
      case 'completed':
        return 'bg-blue-100 text-blue-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      case 'pending':
      default:
        return 'bg-yellow-100 text-yellow-800';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const calculateDuration = () => {
    const start = new Date(booking.start_time);
    const end = new Date(booking.end_time);
    const hours = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60));
    return hours;
  };

  const handleCancelBooking = () => {
    if (confirm('Are you sure you want to cancel this booking?')) {
      post(`/bookings/${booking.id}/cancel`);
    }
  };

  const canCancelBooking = () => {
    return booking.status === 'pending' || booking.status === 'confirmed';
  };

  const isUpcoming = () => {
    return new Date(booking.start_time) > new Date();
  };

  return (
    <>
      <Head title={`Booking #${booking.id}`} />
      
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <header className="bg-white shadow-md sticky top-0 z-50">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-4">
              <Link href="/" className="flex items-center space-x-2">
                <Car className="h-8 w-8 text-primary-600" />
                <span className="text-xl font-bold text-gray-900">Park & Rent</span>
              </Link>

              <nav className="hidden md:flex space-x-8">
                <Link href="/" className="text-gray-700 hover:text-primary-600">
                  Home
                </Link>
                <Link href="/cars" className="text-gray-700 hover:text-primary-600">
                  Browse Cars
                </Link>
                <Link href="/drivers" className="text-gray-700 hover:text-primary-600">
                  Hire a Driver
                </Link>
                <Link href="/dashboard" className="text-gray-700 hover:text-primary-600">
                  Dashboard
                </Link>
              </nav>

              <div className="flex items-center space-x-4">
                <span className="text-gray-700">Welcome, {auth.user.name}</span>
                <Link
                  href="/dashboard"
                  className="bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700"
                >
                  Dashboard
                </Link>
              </div>
            </div>
          </div>
        </header>

        {/* Breadcrumb */}
        <div className="bg-white border-b">
          <div className="container mx-auto px-4 py-3">
            <nav className="flex items-center space-x-2 text-sm">
              <Link href="/" className="text-gray-600 hover:text-primary-600">Home</Link>
              <span className="text-gray-400">/</span>
              <Link href="/bookings" className="text-gray-600 hover:text-primary-600">Bookings</Link>
              <span className="text-gray-400">/</span>
              <span className="text-gray-900">Booking #{booking.id}</span>
            </nav>
          </div>
        </div>

        <div className="container mx-auto px-4 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-2">
              {/* Booking Header */}
              <div className="bg-white rounded-lg shadow-md p-6 mb-6">
                <div className="flex items-start justify-between mb-6">
                  <div className="flex items-center">
                    <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mr-4">
                      {booking.item_type === 'car' ? (
                        <Car size={32} className="text-primary-600" />
                      ) : (
                        <UserRound size={32} className="text-primary-600" />
                      )}
                    </div>
                    <div>
                      <h1 className="text-2xl font-bold text-gray-900">
                        {booking.item_type === 'car' ? 'Car Rental' : 'Driver Service'}
                      </h1>
                      <p className="text-gray-600">Booking #{booking.id}</p>
                      <p className="text-sm text-gray-500">
                        Created on {new Date(booking.created_at).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(booking.status)}`}>
                      {getStatusIcon(booking.status)}
                      <span className="ml-2">{booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}</span>
                    </span>
                  </div>
                </div>

                {/* Booking Timeline */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="flex items-center text-gray-600 mb-2">
                      <Calendar size={16} className="mr-2" />
                      <span className="font-medium">Start Time</span>
                    </div>
                    <p className="text-lg font-semibold text-gray-900">{formatDate(booking.start_time)}</p>
                  </div>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="flex items-center text-gray-600 mb-2">
                      <Calendar size={16} className="mr-2" />
                      <span className="font-medium">End Time</span>
                    </div>
                    <p className="text-lg font-semibold text-gray-900">{formatDate(booking.end_time)}</p>
                  </div>
                </div>

                {/* Duration and Price */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-primary-50 rounded-lg">
                  <div className="text-center">
                    <div className="flex items-center justify-center text-primary-600 mb-1">
                      <Clock size={16} className="mr-1" />
                      <span className="font-medium">Duration</span>
                    </div>
                    <p className="text-xl font-bold text-gray-900">{calculateDuration()} hours</p>
                  </div>
                  <div className="text-center">
                    <div className="flex items-center justify-center text-primary-600 mb-1">
                      <span className="font-medium">Total Price</span>
                    </div>
                    <p className="text-xl font-bold text-primary-600">RWF {booking.total_price.toLocaleString()}</p>
                  </div>
                  <div className="text-center">
                    <div className="flex items-center justify-center text-primary-600 mb-1">
                      <span className="font-medium">Payment Status</span>
                    </div>
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-sm font-medium ${
                      booking.is_paid ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {booking.is_paid ? 'Paid' : 'Pending'}
                    </span>
                  </div>
                </div>

                {booking.notes && (
                  <div className="mt-6 pt-6 border-t">
                    <h3 className="text-lg font-semibold text-gray-900 mb-3">Booking Notes</h3>
                    <p className="text-gray-700 bg-gray-50 p-4 rounded-lg">{booking.notes}</p>
                  </div>
                )}
              </div>

              {/* Item Details */}
              {booking.item && (
                <div className="bg-white rounded-lg shadow-md p-6 mb-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">
                    {booking.item_type === 'car' ? 'Car Details' : 'Driver Details'}
                  </h3>
                  
                  {booking.item_type === 'car' ? (
                    <div className="flex items-start">
                      <div className="w-24 h-24 bg-gray-200 rounded-lg flex items-center justify-center mr-4">
                        {booking.item.images && booking.item.images.length > 0 ? (
                          <img
                            src={booking.item.images[0]}
                            alt={`${booking.item.make} ${booking.item.model}`}
                            className="w-full h-full object-cover rounded-lg"
                          />
                        ) : (
                          <Car size={32} className="text-gray-400" />
                        )}
                      </div>
                      <div className="flex-1">
                        <h4 className="text-xl font-semibold text-gray-900">
                          {booking.item.make} {booking.item.model} ({booking.item.year})
                        </h4>
                        <div className="flex items-center text-gray-600 mt-1">
                          <MapPin size={16} className="mr-1" />
                          <span>{booking.item.location}</span>
                        </div>
                        <p className="text-gray-700 mt-2">{booking.item.description}</p>
                        <div className="mt-3">
                          <span className="text-lg font-semibold text-primary-600">
                            RWF {booking.item.price_per_hour.toLocaleString()}/hour
                          </span>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="flex items-start">
                      <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mr-4">
                        {booking.item.profile_image ? (
                          <img
                            src={booking.item.profile_image}
                            alt={booking.item.name}
                            className="w-full h-full object-cover rounded-full"
                          />
                        ) : (
                          <UserRound size={24} className="text-gray-400" />
                        )}
                      </div>
                      <div className="flex-1">
                        <h4 className="text-xl font-semibold text-gray-900">{booking.item.name}</h4>
                        <div className="flex items-center text-gray-600 mt-1">
                          <MapPin size={16} className="mr-1" />
                          <span>{booking.item.location}</span>
                        </div>
                        <p className="text-sm text-gray-600 mt-1">
                          {booking.item.experience} years experience • Age {booking.item.age}
                        </p>
                        <div className="mt-3">
                          <span className="text-lg font-semibold text-primary-600">
                            RWF {booking.item.price_per_hour.toLocaleString()}/hour
                          </span>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Contact Information */}
              {booking.item && booking.item.owner && (
                <div className="bg-white rounded-lg shadow-md p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">
                    {booking.item_type === 'car' ? 'Car Owner' : 'Driver Contact'}
                  </h3>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mr-4">
                        <span className="text-primary-600 font-semibold">
                          {booking.item.owner.name.charAt(0).toUpperCase()}
                        </span>
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-900">{booking.item.owner.name}</h4>
                        <div className="flex items-center text-gray-600 text-sm mt-1">
                          <Mail size={14} className="mr-1" />
                          <span>{booking.item.owner.email}</span>
                        </div>
                        {booking.item.owner.phone_number && (
                          <div className="flex items-center text-gray-600 text-sm mt-1">
                            <Phone size={14} className="mr-1" />
                            <span>{booking.item.owner.phone_number}</span>
                          </div>
                        )}
                      </div>
                    </div>
                    <button className="flex items-center px-4 py-2 bg-primary-100 text-primary-700 rounded-md hover:bg-primary-200">
                      <MessageCircle size={16} className="mr-2" />
                      Message
                    </button>
                  </div>
                </div>
              )}
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow-md p-6 sticky top-24">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Booking Actions</h3>
                
                <div className="space-y-3">
                  {booking.status === 'pending' && (
                    <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                      <p className="text-sm text-yellow-800">
                        Your booking is pending approval. You'll be notified once it's confirmed.
                      </p>
                    </div>
                  )}

                  {booking.status === 'confirmed' && isUpcoming() && (
                    <div className="p-3 bg-green-50 border border-green-200 rounded-md">
                      <p className="text-sm text-green-800">
                        Your booking is confirmed! Contact details are available above.
                      </p>
                    </div>
                  )}

                  {booking.status === 'completed' && (
                    <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
                      <p className="text-sm text-blue-800">
                        This booking has been completed. Thank you for using Park & Rent!
                      </p>
                    </div>
                  )}

                  {canCancelBooking() && (
                    <button
                      onClick={handleCancelBooking}
                      disabled={processing}
                      className="w-full bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700 disabled:opacity-50"
                    >
                      {processing ? 'Cancelling...' : 'Cancel Booking'}
                    </button>
                  )}

                  <button className="w-full flex items-center justify-center py-2 px-4 border border-gray-300 rounded-md hover:bg-gray-50">
                    <MessageCircle size={16} className="mr-2" />
                    Send Message
                  </button>

                  <Link
                    href="/bookings"
                    className="w-full bg-gray-100 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-200 text-center block"
                  >
                    Back to Bookings
                  </Link>
                </div>

                {/* Booking Summary */}
                <div className="mt-6 pt-6 border-t">
                  <h4 className="font-medium text-gray-900 mb-3">Booking Summary</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Booking ID:</span>
                      <span className="font-medium">#{booking.id}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Type:</span>
                      <span className="font-medium">{booking.item_type === 'car' ? 'Car Rental' : 'Driver Service'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Duration:</span>
                      <span className="font-medium">{calculateDuration()} hours</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Status:</span>
                      <span className="font-medium">{booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}</span>
                    </div>
                    <div className="flex justify-between pt-2 border-t">
                      <span className="text-gray-900 font-medium">Total:</span>
                      <span className="font-bold text-primary-600">RWF {booking.total_price.toLocaleString()}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
