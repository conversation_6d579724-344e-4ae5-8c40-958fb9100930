import React from 'react';
import { Head } from '@inertiajs/react';

export default function Test() {
  return (
    <>
      <Head title="Test Page" />
      <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
        <h1 style={{ color: '#16a34a', fontSize: '2rem', marginBottom: '1rem' }}>
          🎉 Inertia.js is Working!
        </h1>
        <p style={{ fontSize: '1.2rem', marginBottom: '1rem' }}>
          If you can see this page, then Inertia.js is working correctly.
        </p>
        <div style={{ 
          backgroundColor: '#f0fdf4', 
          border: '1px solid #16a34a', 
          borderRadius: '8px', 
          padding: '1rem',
          marginBottom: '1rem'
        }}>
          <h2 style={{ color: '#15803d', marginBottom: '0.5rem' }}>✅ What's Working:</h2>
          <ul style={{ color: '#166534' }}>
            <li><PERSON><PERSON> Server</li>
            <li>Inertia.js Rendering</li>
            <li>React Components</li>
            <li>TypeScript</li>
          </ul>
        </div>
        <a 
          href="/" 
          style={{ 
            backgroundColor: '#16a34a', 
            color: 'white', 
            padding: '10px 20px', 
            textDecoration: 'none', 
            borderRadius: '6px',
            display: 'inline-block'
          }}
        >
          Go to Home Page
        </a>
      </div>
    </>
  );
}
