import React from 'react';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { Car, Users, Shield, Search, MapPin, UserRound } from 'lucide-react';
import { PageProps, Car as CarType, Driver } from '@/types';

interface HomeProps extends PageProps {
  cars: CarType[];
  drivers: Driver[];
}

export default function Home({ auth, cars, drivers }: HomeProps) {
  const featuredCars = cars.slice(0, 3);
  const featuredDrivers = drivers.slice(0, 3);

  return (
    <>
      <Head title="Home" />
      
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <header className="bg-white shadow-md sticky top-0 z-50">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-4">
              {/* Logo */}
              <Link href="/" className="flex items-center space-x-2">
                <Car className="h-8 w-8 text-primary-600" />
                <span className="text-xl font-bold text-gray-900">Park & Rent</span>
              </Link>

              {/* Navigation */}
              <nav className="hidden md:flex space-x-8">
                <Link href="/" className="text-gray-700 hover:text-primary-600">
                  Home
                </Link>
                <Link href="/cars" className="text-gray-700 hover:text-primary-600">
                  Browse Cars
                </Link>
                <Link href="/drivers" className="text-gray-700 hover:text-primary-600">
                  Hire a Driver
                </Link>
              </nav>

              {/* Auth Links */}
              <div className="flex items-center space-x-4">
                {auth.user ? (
                  <div className="flex items-center space-x-4">
                    <span className="text-gray-700">Welcome, {auth.user.name}</span>
                    <Link
                      href="/dashboard"
                      className="bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700"
                    >
                      Dashboard
                    </Link>
                  </div>
                ) : (
                  <div className="flex items-center space-x-4">
                    <Link
                      href="/login"
                      className="text-gray-700 hover:text-primary-600"
                    >
                      Log In
                    </Link>
                    <Link
                      href="/register"
                      className="bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700"
                    >
                      Sign Up
                    </Link>
                  </div>
                )}
              </div>
            </div>
          </div>
        </header>

        {/* Hero Section */}
        <section className="relative bg-gradient-to-r from-primary-600 to-primary-800 text-white">
          <div className="absolute inset-0 bg-black opacity-20"></div>
          
          <div className="container mx-auto px-4 py-16 md:py-24 relative z-10">
            <div className="max-w-3xl">
              <h1 className="text-4xl md:text-5xl font-bold mb-4 leading-tight">
                Rent Cars Directly From Local Owners
              </h1>
              <p className="text-xl mb-8 text-gray-100">
                Find affordable rentals from car owners in your area.
                No middlemen, no hidden fees — just direct peer-to-peer car rentals.
              </p>
              <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-4">
                <Link
                  href="/cars"
                  className="bg-white text-primary-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition duration-200 text-center"
                >
                  Browse Available Cars
                </Link>
                <Link
                  href="/register"
                  className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-primary-600 transition duration-200 text-center"
                >
                  List Your Car
                </Link>
              </div>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Why Choose Park & Rent?
              </h2>
              <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                Experience the future of car rentals with our peer-to-peer platform
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="text-center p-6">
                <div className="bg-primary-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Search className="h-8 w-8 text-primary-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Easy to Find</h3>
                <p className="text-gray-600">
                  Browse hundreds of cars in your area with detailed photos and descriptions
                </p>
              </div>

              <div className="text-center p-6">
                <div className="bg-primary-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Shield className="h-8 w-8 text-primary-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Safe & Secure</h3>
                <p className="text-gray-600">
                  All users are verified and cars are insured for your peace of mind
                </p>
              </div>

              <div className="text-center p-6">
                <div className="bg-primary-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Users className="h-8 w-8 text-primary-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Community Driven</h3>
                <p className="text-gray-600">
                  Connect directly with local car owners and build lasting relationships
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Featured Cars Section */}
        <section className="py-16 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="flex justify-between items-center mb-8">
              <h2 className="text-3xl font-bold text-gray-900">Featured Cars</h2>
              <Link
                href="/cars"
                className="text-primary-600 hover:text-primary-700 font-medium"
              >
                View All Cars →
              </Link>
            </div>

            {featuredCars.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {featuredCars.map((car) => (
                  <div key={car.id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                    <div className="h-48 bg-gray-200 flex items-center justify-center">
                      {car.images && car.images.length > 0 ? (
                        <img
                          src={car.images[0]}
                          alt={`${car.make} ${car.model}`}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <Car size={48} className="text-gray-400" />
                      )}
                    </div>
                    <div className="p-6">
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">
                        {car.make} {car.model} ({car.year})
                      </h3>
                      <div className="flex items-center text-gray-600 mb-2">
                        <MapPin size={16} className="mr-1" />
                        <span className="text-sm">{car.location}</span>
                      </div>
                      <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                        {car.description}
                      </p>
                      <div className="flex justify-between items-center">
                        <span className="text-2xl font-bold text-primary-600">
                          RWF {car.price_per_hour.toLocaleString()}/hr
                        </span>
                        <Link
                          href={`/cars/${car.id}`}
                          className="bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700 transition duration-200"
                        >
                          View Details
                        </Link>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12 bg-white rounded-lg shadow-sm border border-gray-200">
                <Car size={48} className="mx-auto text-gray-400 mb-4" />
                <h3 className="text-xl font-medium text-gray-900 mb-2">No cars available right now</h3>
                <p className="text-gray-600 mb-6">
                  Be the first to list your car and start earning!
                </p>
                <Link
                  href="/register"
                  className="bg-primary-600 text-white px-6 py-2 rounded-md hover:bg-primary-700"
                >
                  List Your Car
                </Link>
              </div>
            )}
          </div>
        </section>

        {/* Featured Drivers Section */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <div className="flex justify-between items-center mb-8">
              <h2 className="text-3xl font-bold text-gray-900">Professional Drivers</h2>
              <Link
                href="/drivers"
                className="text-primary-600 hover:text-primary-700 font-medium"
              >
                View All Drivers →
              </Link>
            </div>

            {featuredDrivers.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {featuredDrivers.map((driver) => (
                  <div key={driver.id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow border border-gray-200">
                    <div className="p-6">
                      <div className="flex items-center mb-4">
                        <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mr-4">
                          {driver.profile_image ? (
                            <img
                              src={driver.profile_image}
                              alt={driver.name}
                              className="w-full h-full object-cover rounded-full"
                            />
                          ) : (
                            <UserRound size={32} className="text-gray-400" />
                          )}
                        </div>
                        <div>
                          <h3 className="text-xl font-semibold text-gray-900">{driver.name}</h3>
                          <div className="flex items-center text-gray-600">
                            <MapPin size={16} className="mr-1" />
                            <span className="text-sm">{driver.location}</span>
                          </div>
                        </div>
                      </div>
                      
                      <div className="mb-4">
                        <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
                          <span>Experience: {driver.experience} years</span>
                          <span>Age: {driver.age}</span>
                        </div>
                        <div className="flex items-center text-sm text-gray-600">
                          <span>Rating: {driver.rating}/5 ({driver.reviews} reviews)</span>
                        </div>
                      </div>

                      {driver.specialties && driver.specialties.length > 0 && (
                        <div className="mb-4">
                          <div className="flex flex-wrap gap-2">
                            {driver.specialties.slice(0, 3).map((specialty, index) => (
                              <span
                                key={index}
                                className="bg-primary-100 text-primary-800 text-xs px-2 py-1 rounded-full"
                              >
                                {specialty}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}

                      <div className="flex justify-between items-center">
                        <span className="text-2xl font-bold text-primary-600">
                          RWF {driver.price_per_hour.toLocaleString()}/hr
                        </span>
                        <Link
                          href={`/drivers/${driver.id}`}
                          className="bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700 transition duration-200"
                        >
                          View Profile
                        </Link>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12 bg-white rounded-lg shadow-sm border border-gray-200">
                <UserRound size={48} className="mx-auto text-gray-400 mb-4" />
                <h3 className="text-xl font-medium text-gray-900 mb-2">No drivers available right now</h3>
                <p className="text-gray-600 mb-6">
                  Check back soon or register as a driver yourself!
                </p>
                <Link
                  href="/register"
                  className="bg-primary-600 text-white px-6 py-2 rounded-md hover:bg-primary-700"
                >
                  Register as Driver
                </Link>
              </div>
            )}
          </div>
        </section>

        {/* Footer */}
        <footer className="bg-gray-900 text-white py-12">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
              <div>
                <div className="flex items-center space-x-2 mb-4">
                  <Car className="h-8 w-8 text-primary-400" />
                  <span className="text-xl font-bold">Park & Rent</span>
                </div>
                <p className="text-gray-400">
                  The trusted peer-to-peer car rental platform connecting car owners with renters.
                </p>
              </div>
              
              <div>
                <h4 className="text-lg font-semibold mb-4">Quick Links</h4>
                <ul className="space-y-2">
                  <li><Link href="/cars" className="text-gray-400 hover:text-white">Browse Cars</Link></li>
                  <li><Link href="/drivers" className="text-gray-400 hover:text-white">Find Drivers</Link></li>
                  <li><Link href="/register" className="text-gray-400 hover:text-white">List Your Car</Link></li>
                </ul>
              </div>
              
              <div>
                <h4 className="text-lg font-semibold mb-4">Support</h4>
                <ul className="space-y-2">
                  <li><a href="#" className="text-gray-400 hover:text-white">Help Center</a></li>
                  <li><a href="#" className="text-gray-400 hover:text-white">Safety</a></li>
                  <li><a href="#" className="text-gray-400 hover:text-white">Contact Us</a></li>
                </ul>
              </div>
              
              <div>
                <h4 className="text-lg font-semibold mb-4">Contact</h4>
                <p className="text-gray-400">
                  Email: <EMAIL><br />
                  Phone: 0788613669
                </p>
              </div>
            </div>
            
            <div className="border-t border-gray-800 mt-8 pt-8 text-center">
              <p className="text-gray-400">
                © 2025 Park & Rent. All rights reserved.
              </p>
            </div>
          </div>
        </footer>
      </div>
    </>
  );
}
