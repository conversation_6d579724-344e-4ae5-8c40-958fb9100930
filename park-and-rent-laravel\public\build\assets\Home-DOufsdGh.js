import{j as e,$ as o,Y as r}from"./app-Cj-kSxmT.js";import{C as a}from"./car-CXkXzXL9.js";import{S as p}from"./search-BLfvhpd5.js";import{S as j}from"./shield-C7t0tPDj.js";import{U as g}from"./users-BA_EXnY7.js";import{M as c}from"./map-pin-DpBG7oTH.js";import{U as x}from"./user-round-Dc3ptzYJ.js";function C({auth:t,cars:n,drivers:d}){const i=n.slice(0,3),l=d.slice(0,3);return e.jsxs(e.Fragment,{children:[e.jsx(o,{title:"Home"}),e.jsxs("div",{className:"min-h-screen bg-gray-50",children:[e.jsx("header",{className:"bg-white shadow-md sticky top-0 z-50",children:e.jsx("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"flex justify-between items-center py-4",children:[e.jsxs(r,{href:"/",className:"flex items-center space-x-2",children:[e.jsx(a,{className:"h-8 w-8 text-primary-600"}),e.jsx("span",{className:"text-xl font-bold text-gray-900",children:"Park & Rent"})]}),e.jsxs("nav",{className:"hidden md:flex space-x-8",children:[e.jsx(r,{href:"/",className:"text-gray-700 hover:text-primary-600",children:"Home"}),e.jsx(r,{href:"/cars",className:"text-gray-700 hover:text-primary-600",children:"Browse Cars"}),e.jsx(r,{href:"/drivers",className:"text-gray-700 hover:text-primary-600",children:"Hire a Driver"})]}),e.jsx("div",{className:"flex items-center space-x-4",children:t.user?e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("span",{className:"text-gray-700",children:["Welcome, ",t.user.name]}),e.jsx(r,{href:"/dashboard",className:"bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700",children:"Dashboard"})]}):e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx(r,{href:"/login",className:"text-gray-700 hover:text-primary-600",children:"Log In"}),e.jsx(r,{href:"/register",className:"bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700",children:"Sign Up"})]})})]})})}),e.jsxs("section",{className:"relative bg-gradient-to-r from-primary-600 to-primary-800 text-white",children:[e.jsx("div",{className:"absolute inset-0 bg-black opacity-20"}),e.jsx("div",{className:"container mx-auto px-4 py-16 md:py-24 relative z-10",children:e.jsxs("div",{className:"max-w-3xl",children:[e.jsx("h1",{className:"text-4xl md:text-5xl font-bold mb-4 leading-tight",children:"Rent Cars Directly From Local Owners"}),e.jsx("p",{className:"text-xl mb-8 text-gray-100",children:"Find affordable rentals from car owners in your area. No middlemen, no hidden fees — just direct peer-to-peer car rentals."}),e.jsxs("div",{className:"flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-4",children:[e.jsx(r,{href:"/cars",className:"bg-white text-primary-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition duration-200 text-center",children:"Browse Available Cars"}),e.jsx(r,{href:"/register",className:"border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-primary-600 transition duration-200 text-center",children:"List Your Car"})]})]})})]}),e.jsx("section",{className:"py-16 bg-white",children:e.jsxs("div",{className:"container mx-auto px-4",children:[e.jsxs("div",{className:"text-center mb-12",children:[e.jsx("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Why Choose Park & Rent?"}),e.jsx("p",{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:"Experience the future of car rentals with our peer-to-peer platform"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[e.jsxs("div",{className:"text-center p-6",children:[e.jsx("div",{className:"bg-primary-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4",children:e.jsx(p,{className:"h-8 w-8 text-primary-600"})}),e.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Easy to Find"}),e.jsx("p",{className:"text-gray-600",children:"Browse hundreds of cars in your area with detailed photos and descriptions"})]}),e.jsxs("div",{className:"text-center p-6",children:[e.jsx("div",{className:"bg-primary-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4",children:e.jsx(j,{className:"h-8 w-8 text-primary-600"})}),e.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Safe & Secure"}),e.jsx("p",{className:"text-gray-600",children:"All users are verified and cars are insured for your peace of mind"})]}),e.jsxs("div",{className:"text-center p-6",children:[e.jsx("div",{className:"bg-primary-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4",children:e.jsx(g,{className:"h-8 w-8 text-primary-600"})}),e.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Community Driven"}),e.jsx("p",{className:"text-gray-600",children:"Connect directly with local car owners and build lasting relationships"})]})]})]})}),e.jsx("section",{className:"py-16 bg-gray-50",children:e.jsxs("div",{className:"container mx-auto px-4",children:[e.jsxs("div",{className:"flex justify-between items-center mb-8",children:[e.jsx("h2",{className:"text-3xl font-bold text-gray-900",children:"Featured Cars"}),e.jsx(r,{href:"/cars",className:"text-primary-600 hover:text-primary-700 font-medium",children:"View All Cars →"})]}),i.length>0?e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:i.map(s=>e.jsxs("div",{className:"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow",children:[e.jsx("div",{className:"h-48 bg-gray-200 flex items-center justify-center",children:s.images&&s.images.length>0?e.jsx("img",{src:s.images[0],alt:`${s.make} ${s.model}`,className:"w-full h-full object-cover"}):e.jsx(a,{size:48,className:"text-gray-400"})}),e.jsxs("div",{className:"p-6",children:[e.jsxs("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:[s.make," ",s.model," (",s.year,")"]}),e.jsxs("div",{className:"flex items-center text-gray-600 mb-2",children:[e.jsx(c,{size:16,className:"mr-1"}),e.jsx("span",{className:"text-sm",children:s.location})]}),e.jsx("p",{className:"text-gray-600 text-sm mb-4 line-clamp-2",children:s.description}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("span",{className:"text-2xl font-bold text-primary-600",children:["RWF ",s.price_per_hour.toLocaleString(),"/hr"]}),e.jsx(r,{href:`/cars/${s.id}`,className:"bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700 transition duration-200",children:"View Details"})]})]})]},s.id))}):e.jsxs("div",{className:"text-center py-12 bg-white rounded-lg shadow-sm border border-gray-200",children:[e.jsx(a,{size:48,className:"mx-auto text-gray-400 mb-4"}),e.jsx("h3",{className:"text-xl font-medium text-gray-900 mb-2",children:"No cars available right now"}),e.jsx("p",{className:"text-gray-600 mb-6",children:"Be the first to list your car and start earning!"}),e.jsx(r,{href:"/register",className:"bg-primary-600 text-white px-6 py-2 rounded-md hover:bg-primary-700",children:"List Your Car"})]})]})}),e.jsx("section",{className:"py-16 bg-white",children:e.jsxs("div",{className:"container mx-auto px-4",children:[e.jsxs("div",{className:"flex justify-between items-center mb-8",children:[e.jsx("h2",{className:"text-3xl font-bold text-gray-900",children:"Professional Drivers"}),e.jsx(r,{href:"/drivers",className:"text-primary-600 hover:text-primary-700 font-medium",children:"View All Drivers →"})]}),l.length>0?e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:l.map(s=>e.jsx("div",{className:"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow border border-gray-200",children:e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx("div",{className:"w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mr-4",children:s.profile_image?e.jsx("img",{src:s.profile_image,alt:s.name,className:"w-full h-full object-cover rounded-full"}):e.jsx(x,{size:32,className:"text-gray-400"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-xl font-semibold text-gray-900",children:s.name}),e.jsxs("div",{className:"flex items-center text-gray-600",children:[e.jsx(c,{size:16,className:"mr-1"}),e.jsx("span",{className:"text-sm",children:s.location})]})]})]}),e.jsxs("div",{className:"mb-4",children:[e.jsxs("div",{className:"flex items-center justify-between text-sm text-gray-600 mb-2",children:[e.jsxs("span",{children:["Experience: ",s.experience," years"]}),e.jsxs("span",{children:["Age: ",s.age]})]}),e.jsx("div",{className:"flex items-center text-sm text-gray-600",children:e.jsxs("span",{children:["Rating: ",s.rating,"/5 (",s.reviews," reviews)"]})})]}),s.specialties&&s.specialties.length>0&&e.jsx("div",{className:"mb-4",children:e.jsx("div",{className:"flex flex-wrap gap-2",children:s.specialties.slice(0,3).map((m,h)=>e.jsx("span",{className:"bg-primary-100 text-primary-800 text-xs px-2 py-1 rounded-full",children:m},h))})}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("span",{className:"text-2xl font-bold text-primary-600",children:["RWF ",s.price_per_hour.toLocaleString(),"/hr"]}),e.jsx(r,{href:`/drivers/${s.id}`,className:"bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700 transition duration-200",children:"View Profile"})]})]})},s.id))}):e.jsxs("div",{className:"text-center py-12 bg-white rounded-lg shadow-sm border border-gray-200",children:[e.jsx(x,{size:48,className:"mx-auto text-gray-400 mb-4"}),e.jsx("h3",{className:"text-xl font-medium text-gray-900 mb-2",children:"No drivers available right now"}),e.jsx("p",{className:"text-gray-600 mb-6",children:"Check back soon or register as a driver yourself!"}),e.jsx(r,{href:"/register",className:"bg-primary-600 text-white px-6 py-2 rounded-md hover:bg-primary-700",children:"Register as Driver"})]})]})}),e.jsx("footer",{className:"bg-gray-900 text-white py-12",children:e.jsxs("div",{className:"container mx-auto px-4",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center space-x-2 mb-4",children:[e.jsx(a,{className:"h-8 w-8 text-primary-400"}),e.jsx("span",{className:"text-xl font-bold",children:"Park & Rent"})]}),e.jsx("p",{className:"text-gray-400",children:"The trusted peer-to-peer car rental platform connecting car owners with renters."})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-lg font-semibold mb-4",children:"Quick Links"}),e.jsxs("ul",{className:"space-y-2",children:[e.jsx("li",{children:e.jsx(r,{href:"/cars",className:"text-gray-400 hover:text-white",children:"Browse Cars"})}),e.jsx("li",{children:e.jsx(r,{href:"/drivers",className:"text-gray-400 hover:text-white",children:"Find Drivers"})}),e.jsx("li",{children:e.jsx(r,{href:"/register",className:"text-gray-400 hover:text-white",children:"List Your Car"})})]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-lg font-semibold mb-4",children:"Support"}),e.jsxs("ul",{className:"space-y-2",children:[e.jsx("li",{children:e.jsx("a",{href:"#",className:"text-gray-400 hover:text-white",children:"Help Center"})}),e.jsx("li",{children:e.jsx("a",{href:"#",className:"text-gray-400 hover:text-white",children:"Safety"})}),e.jsx("li",{children:e.jsx("a",{href:"#",className:"text-gray-400 hover:text-white",children:"Contact Us"})})]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-lg font-semibold mb-4",children:"Contact"}),e.jsxs("p",{className:"text-gray-400",children:["Email: <EMAIL>",e.jsx("br",{}),"Phone: 0788613669"]})]})]}),e.jsx("div",{className:"border-t border-gray-800 mt-8 pt-8 text-center",children:e.jsx("p",{className:"text-gray-400",children:"© 2025 Park & Rent. All rights reserved."})})]})})]})]})}export{C as default};
