import React, { useState, useEffect } from 'react';
import { Send, User } from 'lucide-react';
import Button from '../ui/Button';
import { useAuth } from '../../context/AuthContext';

interface Message {
  id: string;
  senderId: string;
  receiverId: string;
  text: string;
  timestamp: string;
  isRead: boolean;
}

interface ChatBoxProps {
  recipientId: string;
  recipientName: string;
  itemType: 'car' | 'driver';
  itemId: string;
}

const ChatBox: React.FC<ChatBoxProps> = ({ recipientId, recipientName, itemType, itemId }) => {
  const { user, isAuthenticated } = useAuth();
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [isSending, setIsSending] = useState(false);

  // Load messages from backend (no auto-generated messages)
  useEffect(() => {
    // In a real app, this would fetch messages from an API
    // For now, start with empty messages - no auto-generated content
    setMessages([]);
  }, [recipientId, recipientName, user?.id]);

  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault();

    if (!newMessage.trim() || !isAuthenticated) return;

    setIsSending(true);

    // Simulate sending a message to the backend
    setTimeout(() => {
      const newMsg: Message = {
        id: `msg-${Date.now()}`,
        senderId: user?.id || 'guest',
        receiverId: recipientId,
        text: newMessage,
        timestamp: new Date().toISOString(),
        isRead: false,
      };

      setMessages(prev => [...prev, newMsg]);
      setNewMessage('');
      setIsSending(false);

      // Note: Removed auto-response feature - owners should respond manually
    }, 500);
  };

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden border border-gray-200">
      <div className="bg-primary-50 p-4 border-b border-gray-200">
        <h3 className="font-medium text-primary-800">
          Chat with {recipientName}
        </h3>
        <p className="text-sm text-gray-600">
          Ask questions before booking
        </p>
      </div>

      <div className="h-64 overflow-y-auto p-4 space-y-3">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.senderId === user?.id ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`max-w-[80%] rounded-lg px-3 py-2 ${
                message.senderId === user?.id
                  ? 'bg-primary-100 text-primary-800'
                  : 'bg-gray-100 text-gray-800'
              }`}
            >
              <div className="text-sm">{message.text}</div>
              <div className="text-xs text-gray-500 mt-1">
                {new Date(message.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
              </div>
            </div>
          </div>
        ))}

        {messages.length === 0 && (
          <div className="flex items-center justify-center h-full text-gray-500">
            No messages yet. Start the conversation!
          </div>
        )}
      </div>

      <form onSubmit={handleSendMessage} className="p-3 border-t border-gray-200">
        {isAuthenticated ? (
          <div className="flex">
            <input
              type="text"
              placeholder="Type your message..."
              className="flex-grow px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              disabled={isSending}
            />
            <Button
              type="submit"
              className="rounded-l-none"
              disabled={isSending || !newMessage.trim()}
            >
              <Send size={18} />
            </Button>
          </div>
        ) : (
          <div className="text-center py-2 bg-gray-50 rounded-md">
            <User size={18} className="inline-block mr-2 text-gray-500" />
            <span className="text-sm text-gray-600">Please log in to send messages</span>
          </div>
        )}
      </form>
    </div>
  );
};

export default ChatBox;
