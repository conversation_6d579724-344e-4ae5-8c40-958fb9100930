# Park & Rent Deployment Script for Hostinger
# This script prepares files for deployment to Hostinger

Write-Host "🚀 Starting Park & Rent deployment preparation..." -ForegroundColor Green

# Create deployment directory
$deployDir = "deployment"
if (Test-Path $deployDir) {
    Remove-Item $deployDir -Recurse -Force
}
New-Item -ItemType Directory -Path $deployDir

Write-Host "📁 Created deployment directory" -ForegroundColor Yellow

# Copy React frontend build files
Write-Host "📦 Copying React frontend files..." -ForegroundColor Yellow
Copy-Item "dist\*" -Destination $deployDir -Recurse

# Create API directory and copy Laravel files
Write-Host "📦 Copying Laravel API files..." -ForegroundColor Yellow
New-Item -ItemType Directory -Path "$deployDir\api"

# Copy Laravel files (excluding unnecessary files)
$laravelFiles = @(
    "park-and-rent-api\app",
    "park-and-rent-api\bootstrap",
    "park-and-rent-api\config",
    "park-and-rent-api\database",
    "park-and-rent-api\public",
    "park-and-rent-api\resources",
    "park-and-rent-api\routes",
    "park-and-rent-api\storage",
    "park-and-rent-api\artisan",
    "park-and-rent-api\composer.json",
    "park-and-rent-api\composer.lock",
    "park-and-rent-api\.env.production"
)

foreach ($file in $laravelFiles) {
    if (Test-Path $file) {
        Copy-Item $file -Destination "$deployDir\api" -Recurse
    }
}

# Copy production environment file as .env
Copy-Item "park-and-rent-api\.env.production" -Destination "$deployDir\api\.env"

# Copy root .htaccess file
Copy-Item "hostinger-root.htaccess" -Destination "$deployDir\.htaccess"

# Create deployment instructions
$instructions = @'
DEPLOYMENT INSTRUCTIONS FOR HOSTINGER

1. Upload Contents:
   - Upload everything in the deployment folder to your public_html directory
   - The structure should be:
     public_html/
     - index.html (React app)
     - assets/ (React assets)
     - api/ (Laravel API)
     - .htaccess (routing rules)

2. Set up Database:
   - Create MySQL database in Hostinger control panel
   - Update api/.env with your database credentials:
     DB_DATABASE=your_database_name
     DB_USERNAME=your_database_username
     DB_PASSWORD=your_database_password

3. Install Dependencies (via SSH or Hostinger terminal):
   cd public_html/api
   composer install --optimize-autoloader --no-dev

4. Run Laravel Setup:
   php artisan key:generate --force
   php artisan migrate --force
   php artisan db:seed --class=AdminSeeder --force
   php artisan storage:link
   php artisan config:cache
   php artisan route:cache

5. Set File Permissions:
   - storage/ folder: 755
   - bootstrap/cache/ folder: 755
   - .env file: 644

6. Test Your Application:
   - Frontend: https://ebisera.com
   - API: https://ebisera.com/api/cars

Support: **********
Email: <EMAIL>
'@

$instructions | Out-File -FilePath "$deployDir\DEPLOYMENT_INSTRUCTIONS.txt" -Encoding UTF8

Write-Host "Deployment files prepared successfully!" -ForegroundColor Green
Write-Host "Files are ready in the 'deployment' folder" -ForegroundColor Cyan
Write-Host "Check DEPLOYMENT_INSTRUCTIONS.txt for next steps" -ForegroundColor Cyan

# Show deployment folder contents
Write-Host "Deployment folder contents:" -ForegroundColor Yellow
Get-ChildItem $deployDir -Recurse | Select-Object Name, Length, LastWriteTime | Format-Table

Write-Host "Ready for upload to Hostinger!" -ForegroundColor Green
