import{v as b,j as e,$ as g,Y as t}from"./app-Cj-kSxmT.js";import{c as j,C as o}from"./car-CXkXzXL9.js";import{S as x}from"./shield-C7t0tPDj.js";import{M as f}from"./map-pin-DpBG7oTH.js";import{P as N}from"./phone-DVvySU_L.js";import{C as v}from"./calendar-C03a_VmS.js";/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _=[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]],w=j("file-text",_);function M({auth:i,cars:n}){var m;const{data:s,setData:l,post:h,processing:c,errors:r}=b({car_id:"",car_make:"",car_model:"",car_year:"",license_plate:"",reason:"",contact_phone:((m=i.user)==null?void 0:m.phone_number)||"",preferred_installation_date:""}),p=a=>{const d=n.find(y=>y.id.toString()===a);d&&l({...s,car_id:a,car_make:d.make,car_model:d.model,car_year:d.year.toString()})},u=a=>{a.preventDefault(),h("/gps-request")};return e.jsxs(e.Fragment,{children:[e.jsx(g,{title:"GPS Installation Request"}),e.jsxs("div",{className:"min-h-screen bg-gray-50",children:[e.jsx("header",{className:"bg-white shadow-md sticky top-0 z-50",children:e.jsx("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"flex justify-between items-center py-4",children:[e.jsxs(t,{href:"/",className:"flex items-center space-x-2",children:[e.jsx(o,{className:"h-8 w-8 text-primary-600"}),e.jsx("span",{className:"text-xl font-bold text-gray-900",children:"Park & Rent"})]}),e.jsxs("nav",{className:"hidden md:flex space-x-8",children:[e.jsx(t,{href:"/",className:"text-gray-700 hover:text-primary-600",children:"Home"}),e.jsx(t,{href:"/cars",className:"text-gray-700 hover:text-primary-600",children:"Browse Cars"}),e.jsx(t,{href:"/drivers",className:"text-gray-700 hover:text-primary-600",children:"Hire a Driver"}),e.jsx(t,{href:"/dashboard",className:"text-gray-700 hover:text-primary-600",children:"Dashboard"})]}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("span",{className:"text-gray-700",children:["Welcome, ",i.user.name]}),e.jsx(t,{href:"/dashboard",className:"bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700",children:"Dashboard"})]})]})})}),e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs("div",{className:"max-w-2xl mx-auto",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx("div",{className:"w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4",children:e.jsx(x,{className:"h-8 w-8 text-primary-600"})}),e.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"GPS Installation Request"}),e.jsx("p",{className:"text-gray-600",children:"Request GPS installation for your car to enhance security and tracking capabilities."})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6 mb-8",children:[e.jsx("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Benefits of GPS Installation"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"flex items-start",children:[e.jsx(x,{className:"h-5 w-5 text-green-600 mr-2 mt-0.5"}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-gray-900",children:"Enhanced Security"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Real-time tracking and theft protection"})]})]}),e.jsxs("div",{className:"flex items-start",children:[e.jsx(f,{className:"h-5 w-5 text-blue-600 mr-2 mt-0.5"}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-gray-900",children:"Location Tracking"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Always know where your car is located"})]})]}),e.jsxs("div",{className:"flex items-start",children:[e.jsx(o,{className:"h-5 w-5 text-purple-600 mr-2 mt-0.5"}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-gray-900",children:"Rental Confidence"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Increased trust from potential renters"})]})]}),e.jsxs("div",{className:"flex items-start",children:[e.jsx(w,{className:"h-5 w-5 text-orange-600 mr-2 mt-0.5"}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-gray-900",children:"Trip Reports"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Detailed usage and mileage reports"})]})]})]})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[e.jsx("h2",{className:"text-lg font-semibold text-gray-900 mb-6",children:"Request Form"}),e.jsxs("form",{onSubmit:u,className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Select Your Car"}),e.jsxs("select",{value:s.car_id,onChange:a=>p(a.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500",required:!0,children:[e.jsx("option",{value:"",children:"Choose a car..."}),n.map(a=>e.jsxs("option",{value:a.id,children:[a.make," ",a.model," (",a.year,") - ",a.location]},a.id))]}),r.car_id&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:r.car_id})]}),s.car_id&&e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Make"}),e.jsx("input",{type:"text",value:s.car_make,readOnly:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Model"}),e.jsx("input",{type:"text",value:s.car_model,readOnly:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Year"}),e.jsx("input",{type:"text",value:s.car_year,readOnly:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"License Plate Number"}),e.jsx("input",{type:"text",value:s.license_plate,onChange:a=>l("license_plate",a.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500",placeholder:"Enter license plate number"}),r.license_plate&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:r.license_plate})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Contact Phone Number"}),e.jsxs("div",{className:"relative",children:[e.jsx(N,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"}),e.jsx("input",{type:"tel",value:s.contact_phone,onChange:a=>l("contact_phone",a.target.value),className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500",placeholder:"Enter your phone number",required:!0})]}),r.contact_phone&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:r.contact_phone})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Preferred Installation Date"}),e.jsxs("div",{className:"relative",children:[e.jsx(v,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"}),e.jsx("input",{type:"date",value:s.preferred_installation_date,onChange:a=>l("preferred_installation_date",a.target.value),min:new Date().toISOString().split("T")[0],className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"})]}),r.preferred_installation_date&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:r.preferred_installation_date})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Reason for GPS Installation"}),e.jsx("textarea",{value:s.reason,onChange:a=>l("reason",a.target.value),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500",placeholder:"Please explain why you need GPS installation (e.g., security concerns, rental business, fleet management, etc.)",required:!0}),r.reason&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:r.reason})]}),e.jsxs("div",{className:"flex justify-end space-x-4",children:[e.jsx(t,{href:"/dashboard",className:"px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50",children:"Cancel"}),e.jsx("button",{type:"submit",disabled:c,className:"px-6 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed",children:c?"Submitting...":"Submit Request"})]})]})]}),e.jsxs("div",{className:"mt-8 bg-blue-50 border border-blue-200 rounded-lg p-4",children:[e.jsx("h3",{className:"text-lg font-medium text-blue-900 mb-2",children:"Need Help?"}),e.jsx("p",{className:"text-blue-800 mb-2",children:"If you have any questions about GPS installation, please contact our support team:"}),e.jsxs("div",{className:"text-blue-800",children:[e.jsxs("p",{children:[e.jsx("strong",{children:"Email:"})," <EMAIL>"]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Phone:"})," 0788613669"]})]})]})]})})]})]})}export{M as default};
